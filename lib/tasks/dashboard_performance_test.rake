namespace :dashboard do
  desc "Test dashboard performance and query count"
  task performance_test: :environment do
    puts "Dashboard Performance Test"
    puts "=" * 50
    
    # Create test data if needed
    unless Brand.exists?
      puts "Creating test data..."
      brand1 = Brand.create!(name: "Test Brand 1")
      brand2 = Brand.create!(name: "Test Brand 2")
      
      store1 = Store.create!(name: "Store 1", brand: brand1)
      store2 = Store.create!(name: "Store 2", brand: brand1)
      store3 = Store.create!(name: "Store 3", brand: brand2)
      
      user1 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "One", store: store1)
      user2 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "Two", store: store2)
      user3 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "Three", store: store3)
      
      product = Product.create!(name: "Test Product", points: 100)
      
      Sale.create!(user: user1, product: product, brand: brand1, status: :approved, points: 100)
      Sale.create!(user: user2, product: product, brand: brand1, status: :approved, points: 150)
      Sale.create!(user: user3, product: product, brand: brand2, status: :approved, points: 200)
      
      puts "Test data created."
    end
    
    puts "\nTesting OLD approach (N+1 queries):"
    puts "-" * 30
    
    query_count = 0
    ActiveSupport::Notifications.subscribe "sql.active_record" do |*args|
      query_count += 1
    end
    
    # Simulate old approach
    Brand.includes(:stores, :users, :sales).each do |brand|
      brand.stores.count
      brand.users.count
      brand.sales.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count
      brand.sales.approved.sum(:points)
    end
    
    old_query_count = query_count
    puts "Queries executed: #{old_query_count}"
    
    # Reset counter
    query_count = 0
    
    puts "\nTesting NEW approach (optimized):"
    puts "-" * 30
    
    # Simulate new approach
    controller = DashboardController.new
    brands = Brand.order(:name)
    store_counts = Store.group(:brand_id).count
    user_counts = User.joins(:store).group("stores.brand_id").count
    monthly_sales_counts = Sale.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month)
                              .group(:brand_id).count
    approved_points = Sale.approved.group(:brand_id).sum(:points)
    
    brand_stats = brands.map do |brand|
      {
        brand: brand,
        stores_count: store_counts[brand.id] || 0,
        users_count: user_counts[brand.id] || 0,
        monthly_sales_count: monthly_sales_counts[brand.id] || 0,
        approved_points: approved_points[brand.id] || 0
      }
    end
    
    new_query_count = query_count
    puts "Queries executed: #{new_query_count}"
    
    puts "\nPerformance Improvement:"
    puts "-" * 30
    puts "Old approach: #{old_query_count} queries"
    puts "New approach: #{new_query_count} queries"
    puts "Reduction: #{old_query_count - new_query_count} queries (#{((old_query_count - new_query_count).to_f / old_query_count * 100).round(1)}%)"
    
    puts "\nBrand Statistics Results:"
    puts "-" * 30
    brand_stats.each do |stat|
      puts "#{stat[:brand].name}:"
      puts "  Stores: #{stat[:stores_count]}"
      puts "  Users: #{stat[:users_count]}"
      puts "  Monthly Sales: #{stat[:monthly_sales_count]}"
      puts "  Approved Points: #{stat[:approved_points]}"
      puts
    end
  end
  
  desc "Benchmark dashboard methods"
  task benchmark: :environment do
    require 'benchmark'
    
    puts "Dashboard Method Benchmarking"
    puts "=" * 50
    
    controller = DashboardController.new
    
    Benchmark.bm(25) do |x|
      x.report("Old brand stats approach:") do
        10.times do
          Brand.includes(:stores, :users, :sales).each do |brand|
            brand.stores.count
            brand.users.count
            brand.sales.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count
            brand.sales.approved.sum(:points)
          end
        end
      end
      
      x.report("New brand stats approach:") do
        10.times do
          controller.send(:compute_brand_statistics)
        end
      end
      
      x.report("User growth stats:") do
        10.times do
          controller.send(:compute_user_growth_statistics)
        end
      end
    end
  end
end
