services:
  db:
    image: postgres:14
    volumes:
      - db_data:/var/lib/postgresql/data
      - $PWD/postgresql.conf:/etc/postgresql/postgresql.conf
    environment:
      POSTGRES_PASSWORD: dev2021pass
    ports:
      - "5432:5432"
    command:
      [
        "postgres",
        "-c",
        "config_file=/etc/postgresql/postgresql.conf"
      ]

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  search:
    image: getmeili/meilisearch:v1.8
    environment:
      MEILI_NO_ANALYTICS: true
      MEILI_LOG_LEVEL: DEBUG
    ports:
      - "7700:7700"
    volumes:
      - search_data:/data.ms

  mail:
    image: sj26/mailcatcher:latest
    ports:
      - "1025:1025"
      - "1080:1080"

volumes:
  db_data:
  search_data:
