class DataIntegrityJob < ApplicationJob
  queue_as :low_priority

  def perform
    check_orphaned_activities
    check_point_mismatches
    check_counter_cache_accuracy
    check_wallet_balances
  end

  private

  def check_orphaned_activities
    orphaned_count = Activity.left_joins(:transactable).where(transactable: nil).count

    if orphaned_count > 0
      Rails.logger.warn "Found #{orphaned_count} orphaned activities"
      # Optionally clean up or notify admins
    end
  end

  def check_point_mismatches
    mismatches = []

    Sale.approved.find_each do |sale|
      expected_points = calculate_expected_points(sale)
      if sale.points != expected_points
        mismatches << {
          type: "Sale",
          id: sale.id,
          expected: expected_points,
          actual: sale.points
        }
      end
    end

    if mismatches.any?
      Rails.logger.warn "Found #{mismatches.count} point mismatches: #{mismatches.inspect}"
      # Optionally fix automatically or notify admins
    end
  end

  def check_counter_cache_accuracy
    # Check a sample of counter caches
    User.limit(100).find_each do |user|
      actual_sales_count = user.sales.count
      if user.sales_count != actual_sales_count
        Rails.logger.warn "Counter cache mismatch for User #{user.id}: expected #{actual_sales_count}, got #{user.sales_count}"
        User.reset_counters(user.id, :sales)
      end
    end
  end

  def check_wallet_balances
    # Verify wallet balances match activity totals
    Wallet.joins(:activities).group(:id).having("SUM(CASE WHEN activities.kind = ? THEN activities.points ELSE -activities.points END) != wallets.balance", "credit").limit(10).each do |wallet|
      Rails.logger.warn "Wallet balance mismatch for Wallet #{wallet.id}"
      # Optionally recalculate balance
    end
  end

  def calculate_expected_points(sale)
    base_points = sale.product.points_earned_for_country(sale.user.address.country.id)
    multiplier = sale.promotion&.multiplier || 1.0
    (base_points * multiplier).round
  end
end
