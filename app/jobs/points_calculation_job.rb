class PointsCalculationJob < ApplicationJob
  queue_as :default

  def perform(sale_id)
    sale = Sale.find(sale_id)

    # Calculate points with proper error handling
    begin
      calculated_points = calculate_points_for_sale(sale)

      # Update points atomically
      sale.update_column(:points, calculated_points)

      # Update wallet if sale is approved
      if sale.approved?
        sale.user.wallet.credit!(calculated_points)
      end

      Rails.logger.info "Points calculated for Sale #{sale_id}: #{calculated_points}"
    rescue => e
      Rails.logger.error "Failed to calculate points for Sale #{sale_id}: #{e.message}"
      raise e
    end
  end

  private

  def calculate_points_for_sale(sale)
    base_points = sale.product.points_earned_for_country(sale.user.address.country.id)
    multiplier = sale.promotion&.multiplier || 1.0

    (base_points * multiplier).round
  end
end
