class GiftcardJob < ApplicationJob
  queue_as :default

  def perform(...)
    fulfillment, filename = GiftcardService.call(...)

    if fulfillment
      # Net::SFTP.start(ENV["GIFTCARD_HOST"], ENV["GIFTCARD_USERNAME"], password: ENV["GIFTCARD_PASSWORD"], kex: ["diffie-hellman-group-exchange-sha1", "diffie-hellman-group1-sha1", "diffie-hellman-group14-sha1"]) do |sftp|
      #  sftp.upload!(file, "/inbound")
      # end
      GiftcardMailer.with(file: fulfillment, filename: filename).giftcard_email.deliver_later
    end
  end
end
