# frozen_string_literal: true

module Attachment
  # Promote Shrine attacher job
  class PromoteJob < ApplicationJob
    def perform(record, name, file_data)
      attacher = Shrine::Attacher.retrieve(model: record, name: name, file: file_data)
      attacher.create_derivatives if record.is_a?(User)
      attacher.atomic_promote
    rescue Shrine::AttachmentChanged, ActiveRecord::RecordNotFound
    end
  end
end
