class BatchNotificationJob < ApplicationJob
  queue_as :default

  def perform(model_class, ids, notification_type)
    records = model_class.constantize.includes(:user).where(id: ids)

    # Group records by user to send batch notifications
    records.group_by(&:user).each do |user, user_records|
      case notification_type
      when "new_sales"
        BatchSaleNotificationMailer.new_sales_batch(user, user_records).deliver_now
      when "updated_sales"
        BatchSaleNotificationMailer.updated_sales_batch(user, user_records).deliver_now
      when "new_orders"
        BatchOrderNotificationMailer.new_orders_batch(user, user_records).deliver_now
      when "updated_orders"
        BatchOrderNotificationMailer.updated_orders_batch(user, user_records).deliver_now
      end
    end
  end
end
