class CheckPromotionsJob < ApplicationJob
  queue_as :default

  def perform(*args)
    # Find upcoming promotions
    upcoming = Promotion.where(starts_at: 2.days.from_now..4.days.from_now)
    # Send notifications
    upcoming.each do |promotion|
      UpcomingPromotionNotification.with(promotion: promotion).deliver_later(User.where(status: "active", store: promotion.stores))
    end

    # Find starting promotions
    starting = Promotion.includes(:products).where(starts_at: Time.zone.today.beginning_of_day..Time.zone.today.end_of_day)
    starting.each do |promotion|
      StartingPromotionNotification.with(promotion: promotion).deliver_later(User.where(status: "active", store: promotion.stores))
    end
  end
end
