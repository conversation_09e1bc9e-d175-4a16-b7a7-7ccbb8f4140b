class BatchSaleNotificationMailer < ApplicationMailer
  def new_sales_batch(user, sales)
    @user = user
    @sales = sales
    @sales_count = sales.count

    mail(
      to: email_address_with_name(@user.email, @user.name),
      subject: "ZEISS Points: #{@sales_count} New Sale#{"s" if @sales_count > 1} Submitted"
    )
  end

  def updated_sales_batch(user, sales)
    @user = user
    @sales = sales
    @sales_count = sales.count
    @approved_count = sales.count(&:approved?)
    @declined_count = sales.count(&:declined?)

    mail(
      to: email_address_with_name(@user.email, @user.name),
      subject: "ZEISS Points: #{@sales_count} Sale#{"s" if @sales_count > 1} Updated"
    )
  end
end
