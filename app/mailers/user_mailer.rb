class UserMailer < ApplicationMailer
  def new_user_notification
    @user = params[:user]
    @admin = params[:recipient]

    mail(to: email_address_with_name(@admin.email, @admin.name), subject: "ZEISS Points: New Agent") do |format|
      format.html
    end
  end

  def approved_user_notification
    @user = params[:recipient]

    mail(to: email_address_with_name(@user.email, @user.name), subject: "ZEISS Points: Account Approved") do |format|
      format.html
    end
  end
end
