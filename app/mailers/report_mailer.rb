class ReportMailer < ApplicationMailer
  default from: '<EMAIL>'

  def scheduled_report(recipients:, report_type:, filename:, data:, user:)
    @user = user
    @report_type = report_type.humanize
    @generated_at = Time.current
    @filename = filename

    # Attach the report file
    case filename.split('.').last.downcase
    when 'csv'
      attachments[filename] = { mime_type: 'text/csv', content: data }
    when 'xlsx'
      # For XLSX, we'd need to generate the actual file
      attachments[filename] = { mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', content: data }
    when 'pdf'
      attachments[filename] = { mime_type: 'application/pdf', content: data }
    when 'json'
      attachments[filename] = { mime_type: 'application/json', content: data }
    end

    mail(
      to: recipients,
      subject: "Scheduled #{@report_type} Report - #{@generated_at.strftime('%B %d, %Y')}"
    )
  end

  def report_generation_complete(user:, report_type:, filename:, download_url: nil)
    @user = user
    @report_type = report_type.humanize
    @filename = filename
    @download_url = download_url
    @generated_at = Time.current

    mail(
      to: user.email,
      subject: "Your #{@report_type} Report is Ready"
    )
  end

  def report_generation_failed(user:, report_type:, error_message:)
    @user = user
    @report_type = report_type.humanize
    @error_message = error_message
    @generated_at = Time.current

    mail(
      to: user.email,
      subject: "Report Generation Failed - #{@report_type}"
    )
  end

  def weekly_summary_report(user:, summary_data:)
    @user = user
    @summary_data = summary_data
    @week_start = 1.week.ago.beginning_of_week
    @week_end = 1.week.ago.end_of_week

    mail(
      to: user.email,
      subject: "Weekly Summary Report - #{@week_start.strftime('%B %d')} to #{@week_end.strftime('%B %d, %Y')}"
    )
  end

  def monthly_analytics_digest(user:, analytics_data:)
    @user = user
    @analytics_data = analytics_data
    @month = 1.month.ago.strftime('%B %Y')

    mail(
      to: user.email,
      subject: "Monthly Analytics Digest - #{@month}"
    )
  end
end
