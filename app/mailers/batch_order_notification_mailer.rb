class BatchOrderNotificationMailer < ApplicationMailer
  def new_orders_batch(user, orders)
    @user = user
    @orders = orders
    @orders_count = orders.count
    @total_points = orders.sum(&:points)

    mail(
      to: email_address_with_name(@user.email, @user.name),
      subject: "ZEISS Points: #{@orders_count} New Order#{"s" if @orders_count > 1} Submitted"
    )
  end

  def updated_orders_batch(user, orders)
    @user = user
    @orders = orders
    @orders_count = orders.count
    @approved_count = orders.count(&:approved?)
    @declined_count = orders.count(&:declined?)
    @shipped_count = orders.count(&:shipped?)

    mail(
      to: email_address_with_name(@user.email, @user.name),
      subject: "ZEISS Points: #{@orders_count} Order#{"s" if @orders_count > 1} Updated"
    )
  end
end
