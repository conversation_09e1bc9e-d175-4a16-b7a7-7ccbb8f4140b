class OrderMailer < ApplicationMailer
  def updated_order_notification
    @order = params[:order]
    @user = params[:recipient]

    mail(to: email_address_with_name(@user.email, @user.name), subject: "ZEISS Points: Your Order has been #{@order.status.titleize}")
  end

  def new_order_notification
    @order = params[:order]
    @user = @order.user
    @admin = params[:recipient]

    mail(to: @admin.email, subject: "ZEISS Points: New Order for Review")
  end
end
