class SaleMailer < ApplicationMailer
  def updated_sale_notification
    @sale = params[:sale]
    @user = params[:recipient]

    mail(to: email_address_with_name(@user.email, @user.name), subject: "ZEISS Points: Your Sale Has Been #{@sale.status.titleize}")
  end

  def new_sale_notification
    @sale = params[:sale]
    @user = @sale.user
    @admin = params[:recipient]
    mail(to: @admin.email, subject: "ZEISS Points: New Agent Sale")
  end
end
