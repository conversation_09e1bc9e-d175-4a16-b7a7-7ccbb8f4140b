# frozen_string_literal: true

module Maintenance
  class ImportStoresTask < MaintenanceTasks::Task
    attribute :brand_id, :integer
    validates :brand_id, presence: true

    csv_collection

    def process(row)
      state = State.find_by!(abbreviation: row["state"])
      Store.create!(name: row["name"]&.strip, brand_id: brand_id, address_attributes: {line1: row["street"], city: row["city"], state: state, zip_code: row["zip"], country: state.country})
    end
  end
end
