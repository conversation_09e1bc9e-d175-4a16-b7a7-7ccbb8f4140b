# frozen_string_literal: true

module Maintenance
  class ImportProductsTask < MaintenanceTasks::Task
    csv_collection

    def process(row)
      category = Category.find_by!(old_category_id: row["old_category_id"])
      country = Country.find_by(abbreviation: "US")
      product = Product.find_by(sku: row["sku"])
      return if product.present?
      Product.create!(name: row["name"]&.strip, category: category, description: row["description"], sku: row["sku"], upc: "829576019311", prices_attributes: [{msrp: row["msrp"], points_needed: row["points_needed"], points_earned: row["points_earned"], country: country}])
    end
  end
end
