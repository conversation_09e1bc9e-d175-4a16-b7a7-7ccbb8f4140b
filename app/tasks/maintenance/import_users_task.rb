# frozen_string_literal: true

module Maintenance
  class ImportUsersTask < MaintenanceTasks::Task
    csv_collection

    def process(user)
      state = State.find_by!(abbreviation: user["state"].strip)
      store = Store.find_by!(old_store_id: user["old_store_id"])
      user = User.new(first_name: user["first_name"], last_name: user["last_name"], email: user["email"], phone_number: user["phone_number"], old_user_id: user["old_user_id"], password: "WIfFXwJS%D", password_confirmation: "WIfFXwJS%D", status: "active", store: store, address_attributes: [line1: user["street"], state: state, city: user["city"], zip_code: user["zip"], country: state.country])
      user.skip_confirmation!
      user.save!
    end
  end
end
