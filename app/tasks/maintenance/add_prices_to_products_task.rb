# frozen_string_literal: true

module Maintenance
  class AddPricesToProductsTask < MaintenanceTasks::Task
    def collection
      Product.all
    end

    def process(product)
      country = Country.find_by(abbreviation: "US")
      return if product.prices.where(country: country).present?
      product.prices.create(country: country, msrp_in_cents: product.msrp * 100, points_needed: product.points_needed, points_earned: product.points_earned)
      product.upc = "829576019311" if product.upc.blank?
      product.save!
    end
  end
end
