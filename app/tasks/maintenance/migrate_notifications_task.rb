# frozen_string_literal: true

module Maintenance
  class MigrateNotificationsTask < MaintenanceTasks::Task
    def collection
      Notification.all
    end

    def process(notification)
      attributes = notification.attributes.slice("type", "created_at", "updated_at").with_indifferent_access
      attributes[:type] = attributes[:type].sub("Notification", "Notifier")
      attributes[:params] = Noticed::Coder.load(notification.params)
      attributes[:params] = {} if attributes[:params].try(:has_key?, "noticed_error")

      attributes[:notifications_attributes] = [{
        type: "#{attributes[:type]}::Notification",
        recipient_type: notification.recipient_type,
        recipient_id: notification.recipient_id,
        read_at: notification.read_at,
        seen_at: notification.read_at,
        created_at: notification.created_at,
        updated_at: notification.updated_at
      }]
      Noticed::Event.create!(attributes)
    end

    def count
      Notification.all.size
    end
  end
end
