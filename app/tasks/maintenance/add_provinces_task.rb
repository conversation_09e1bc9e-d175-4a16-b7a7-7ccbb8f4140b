# frozen_string_literal: true

module Maintenance
  class AddProvincesTask < MaintenanceTasks::Task
    no_collection

    def process
      region = Region.create(name: "Canada")
      can = Country.find_by(abbreviation: "CA")

      can.states.create_with(region_id: region.id).insert_all(
        [
          {name: "British Columbia", abbreviation: "BC"},
          {name: "Alberta", abbreviation: "AB"},
          {name: "Manitoba", abbreviation: "MB"},
          {name: "Ontario", abbreviation: "ON"},
          {name: "Quebec", abbreviation: "QC"},
          {name: "New Brunswick", abbreviation: "NB"},
          {name: "Nova Scotia", abbreviation: "NS"},
          {name: "Prince Edward Island", abbreviation: "PE"},
          {name: "Newfoundland and Labrador", abbreviation: "NL"},
          {name: "Yukon", abbreviation: "YK"},
          {name: "Northwest Territories", abbreviation: "NT"},
          {name: "Nunavut", abbreviation: "NU"}
        ]
      )
    end
  end
end
