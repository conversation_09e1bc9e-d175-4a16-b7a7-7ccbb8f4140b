# == Schema Information
#
# Table name: prices
#
#  id            :bigint           not null, primary key
#  msrp_in_cents :integer          default(0), not null
#  points_earned :integer          default(0), not null
#  points_needed :integer          default(0), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint
#  product_id    :bigint
#
# Indexes
#
#  index_prices_on_country_id                 (country_id)
#  index_prices_on_country_id_and_product_id  (country_id,product_id) UNIQUE
#  index_prices_on_product_id                 (product_id)
#
class Price < ApplicationRecord
  belongs_to :product
  belongs_to :country

  validates :country_id, uniqueness: {scope: :product_id}

  delegate :currency, to: :country, allow_nil: true

  monetize :msrp_in_cents, with_model_currency: :currency

  after_create_commit -> { broadcast_append_later_to :prices }
  after_update_commit -> { broadcast_replace_later_to self }
end
