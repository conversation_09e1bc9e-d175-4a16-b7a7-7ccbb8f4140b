# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved               :boolean          default(FALSE)
#  approved_at            :date
#  approved_sales_count   :integer          default(0), not null
#  avatar_data            :jsonb
#  city                   :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  email_frequency        :integer          default("daily")
#  encrypted_password     :string           default(""), not null
#  failed_email           :boolean          default(FALSE)
#  first_name_ciphertext  :text
#  last_name              :string
#  last_seen              :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  orders_count           :integer          default(0), not null
#  pending_sales_count    :integer          default(0), not null
#  phone_number           :string
#  points_earned          :integer
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular_user")
#  sales_count            :integer          default(0), not null
#  settings               :jsonb
#  sign_in_count          :integer          default(0), not null
#  slug                   :string
#  status                 :integer          default("new")
#  store_zip              :string
#  street_ciphertext      :text
#  time_zone              :string
#  unconfirmed_email      :string
#  username               :string
#  zip_ciphertext         :text
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  admin_brand_id         :bigint
#  admin_region_id        :bigint
#  old_user_id            :integer
#  state_id               :bigint
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_admin_brand_id        (admin_brand_id)
#  index_users_on_admin_region_id       (admin_region_id)
#  index_users_on_approved              (approved)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_email_frequency       (email_frequency)
#  index_users_on_last_seen             (last_seen)
#  index_users_on_orders_count          (orders_count)
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_role                  (role)
#  index_users_on_sales_count           (sales_count)
#  index_users_on_slug                  (slug) UNIQUE
#  index_users_on_state_id              (state_id)
#  index_users_on_status                (status)
#  index_users_on_status_and_role       (status,role)
#  index_users_on_store_id              (store_id)
#
class User < ApplicationRecord
  include UserSearchable
  include UserNotifications
  include UserValidations
  include PhoneNormalizable

  after_create :set_point_account

  extend FriendlyId
  ActiveRecord_Relation.include Pagy::Meilisearch

  self.ignored_columns = ["first_name", "street", "zip"]

  FILTER_FIELDS = %w[status role brand region_name country_name].freeze
  SORT_TO_GROUP = {state: "state_name", status: "status", store: "store_name", role: "role"}.freeze

  enum :role, regular_user: 0, region_admin: 1, optics_admin: 2, photo_admin: 3, admin: 4, super_admin: 5
  enum :status, {new: 0, active: 2, inactive: 3, deleted: 4}, suffix: true
  enum :email_frequency, daily: 0, hourly: 1, every: 2

  validates :status, inclusion: {in: statuses.keys}

  store_accessor :settings,
    # Email preferences
    :mail_user_new, :mail_sale_new, :mail_sale_updated,
    :mail_order_new, :mail_order_updated, :mail_store_request_new,
    :mail_promotion,
    # SMS preferences
    :sms_promotion,
    # UI preferences
    :theme_preference, :language_preference, :timezone_preference

  attribute :store_search_name, :string

  has_encrypted :first_name, :street, :zip

  devise :database_authenticatable, :registerable, :recoverable, :rememberable, :validatable, :confirmable, :trackable, :lastseenable

  has_person_name

  belongs_to :store, -> { includes :state }, optional: true
  belongs_to :admin_region, class_name: "Region", optional: true
  belongs_to :admin_brand, class_name: "Brand", optional: true

  has_one :admin_cart, dependent: :destroy, class_name: "Cart"
  has_one :wallet
  has_one :address, as: :addressable

  has_many :orders
  has_many :sales
  has_many :adjustments
  has_many :notifications, as: :recipient, dependent: :destroy
  has_many :visits, class_name: "Ahoy::Visit"

  has_noticed_notifications

  normalizes :phone_number, with: ->(phone) { normalize_phone_number(phone) }
  normalizes :first_name, with: ->(first_name) { first_name.titleize }
  normalizes :last_name, with: ->(last_name) { last_name.titleize }
  normalizes :email, with: ->(email) { email.strip.downcase }
  normalizes :city, with: ->(city) { city&.titleize }
  normalizes :username, with: ->(username) { username&.strip&.downcase }

  friendly_id :email, use: :slugged

  has_one_attached :avatar do |attachable|
    attachable.variant :thumb, resize_to_limit: [100, 100]
    attachable.variant :medium, resize_to_limit: [300, 300]
  end

  # TODO: Add avatar validation when active_storage_validations gem is available
  # validates :avatar, content_type: ["image/png", "image/jpg", "image/jpeg"],
  #           size: {less_than: 5.megabytes}

  accepts_nested_attributes_for :address

  scope :regular_users, -> { order(:first_name).where(role: :regular_user, status: :active) }
  scope :super_admins, -> { where(role: :super_admin) }
  scope :admins, -> { where(role: :admin) }
  scope :inactive_users, -> { includes(:wallet).where(status: :inactive) }
  scope :new_users, -> { where(status: :new) }
  scope :search_import, -> { where.not(status: :deleted) }
  scope :with_associations, -> {
    includes(:store, :wallet, :address, address: [:state, :country])
  }
  scope :recently_active, ->(within: 30.days) {
    where("last_seen > ?", within.ago)
  }

  has_paper_trail skip: [:first_name, :street, :zip]

  counter_culture :store
  counter_culture [:store, :brand]
  counter_culture [:store, :store_chain]
  counter_culture [:store, :state, :region]

  after_touch :index!

  def active_for_authentication?
    super and active_status?
  end

  def name_with_points
    "#{name} (#{wallet.available_balance})"
  end

  def avatar_path
    avatar.attached? ? avatar : "avatar-missing.svg"
  end

  # Performance optimization methods
  def cached_available_balance
    Rails.cache.fetch("user_#{id}_available_balance", expires_in: 5.minutes) do
      wallet&.available_points || 0
    end
  end

  def recently_active?(within: 30.days)
    last_seen.present? && last_seen > within.ago
  end

  def activity_status
    return "never_logged_in" if last_sign_in_at.nil?
    return "recently_active" if recently_active?
    return "inactive" if last_seen < 90.days.ago
    "moderately_active"
  end

  def avatar_url(variant: :medium)
    return ActionController::Base.helpers.asset_path("avatar-missing.svg") unless avatar.attached?

    Rails.application.routes.url_helpers.rails_representation_url(
      avatar.variant(variant), only_path: true
    )
  end

  private

  def set_point_account
    create_wallet if wallet.nil?
  end

  def send_devise_notification(notification, *)
    devise_mailer.send(notification, self, *).deliver_later
  end
end
