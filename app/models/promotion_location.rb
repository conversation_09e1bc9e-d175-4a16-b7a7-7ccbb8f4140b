# == Schema Information
#
# Table name: promotion_locations
#
#  id              :bigint           not null, primary key
#  promotable_type :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  promotable_id   :bigint
#  promotion_id    :bigint
#
# Indexes
#
#  index_promotion_locations_on_promotable    (promotable_type,promotable_id)
#  index_promotion_locations_on_promotion_id  (promotion_id)
#
class PromotionLocation < ApplicationRecord
  belongs_to :promotion
  belongs_to :promotable, polymorphic: true, dependent: :destroy
end
