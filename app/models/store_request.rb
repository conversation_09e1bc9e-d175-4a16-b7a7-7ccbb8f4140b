# == Schema Information
#
# Table name: store_requests
#
#  id           :bigint           not null, primary key
#  city         :string
#  email        :string
#  manager_name :string
#  name         :string
#  status       :integer          default("pending")
#  store_name   :string
#  street       :string
#  zip          :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  brand_id     :bigint
#  state_id     :bigint           not null
#
# Indexes
#
#  index_store_requests_on_brand_id  (brand_id)
#  index_store_requests_on_state_id  (state_id)
#
class StoreRequest < ApplicationRecord
  after_create_commit :notify_admins
  before_update :create_store

  enum :status, pending: 0, approved: 1, declined: 2, exists: 3

  belongs_to :state
  belongs_to :brand
  has_one :region, through: :state

  validates :name, :store_name, :street, :zip, :city, :state, :brand, :manager_name, :email, presence: true

  private

  def create_store
    if status_changed?(from: "pending", to: "approved")
      Store.create(name: store_name, street: street, city: city, state: state, zip: zip, brand: brand)
    end
  end

  def notify_admins
    NewStoreRequestNotification.with(request: self).deliver_later(User.admins)
  end
end
