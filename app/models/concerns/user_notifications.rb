module UserNotifications
  extend ActiveSupport::Concern
  
  included do
    after_update_commit :send_approved_email
    after_create_commit :send_newuser_email
  end
  
  def notification_preferences
    {
      email: {
        user_new: mail_user_new == "1",
        sale_new: mail_sale_new == "1",
        sale_updated: mail_sale_updated == "1",
        order_new: mail_order_new == "1",
        order_updated: mail_order_updated == "1",
        store_request_new: mail_store_request_new == "1",
        promotion: mail_promotion == "1"
      },
      sms: {
        promotion: sms_promotion == "1"
      }
    }
  end
  
  private
  
  def send_approved_email
    return unless status_changed? && active_status?
    ApprovedUserNotifier.deliver_later(self)
  end
  
  def send_newuser_email
    NewUserNotifier.with(user: self).deliver_later(User.admins)
  end
end
