# frozen_string_literal: true

module PhoneNormalizable
  extend ActiveSupport::Concern

  class_methods do
    # Normalize a phone number by removing all non-digit characters
    # This ensures consistent storage format for phone numbers
    #
    # @param phone [String] the phone number to normalize
    # @return [String] the normalized phone number containing only digits
    #
    # Examples:
    #   PhoneNormalizable.normalize_phone_number("(*************") => "5551234567"
    #   PhoneNormalizable.normalize_phone_number("******-123-4567") => "15551234567"
    #   PhoneNormalizable.normalize_phone_number("************ ext 123") => "5551234567123"
    #   PhoneNormalizable.normalize_phone_number("(022) 235-48") => "02223548"
    def normalize_phone_number(phone)
      return phone if phone.blank?

      # Remove all non-digit characters
      phone.delete("^0-9")
    end
  end

  # Instance method version for convenience
  def normalize_phone_number(phone)
    self.class.normalize_phone_number(phone)
  end
end
