module UserSearchable
  extend ActiveSupport::Concern
  
  included do
    include MeiliSearch::Rails
    extend Pagy::Meilisearch
    
    meilisearch do
      attribute :first_name, :last_name, :email, :status, :role
      
      attribute :brand do
        store.present? ? store.brand_name : "Unknown"
      end

      attribute :brand_id do
        store.present? ? store.brand_id : 0
      end

      attribute :region_id do
        address&.state&.region&.id || nil
      end

      attribute :region_name do
        address&.state&.region&.name || "Unknown"
      end

      attribute :state_name do
        address&.state&.name || "Unknown"
      end

      attribute :store_name do
        store.present? ? store.name : "Admin"
      end

      attribute :country_name do
        address&.country&.name || "Unknown"
      end

      filterable_attributes [:status, :role, :brand, :region_name, :brand_id, :region_id, :country_name]
      sortable_attributes [:status, :state_name, :role, :store_name, :last_name]
    end
  end
  
  def search_index?
    !deleted?
  end
end
