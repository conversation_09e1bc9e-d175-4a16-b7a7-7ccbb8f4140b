require "csv"

class Categories::Import
  include ActiveModel::Model

  attr_accessor :file, :brand_id, :imported_count

  def process!
    brand = Brand.find(brand_id)

    categories = []
    CSV.foreach(file.path, headers: true, header_converters: :symbol) do |row|
      data = row.to_hash
      category = brand.categories.build(data.slice(:name, :description, :old_category_id))
      categories << category
    end

    result = Category.import(categories)
    @imported_count = result.ids.size
  end

  def save
    process!
    errors.none?
  end
end
