# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  approved_at      :date
#  declined_at      :datetime
#  line_items_count :integer          default(0), not null
#  notes            :text
#  points           :integer          default(0), not null
#  ship_to          :integer          default("home"), not null
#  status           :integer          default("pending")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  admin_id         :bigint
#  ahoy_visit_id    :bigint
#  brand_id         :bigint           not null
#  region_id        :bigint           not null
#  sap_id           :string
#  user_id          :bigint           not null
#
# Indexes
#
#  index_orders_on_admin_id                             (admin_id)
#  index_orders_on_approved_at                          (approved_at) WHERE (approved_at IS NOT NULL)
#  index_orders_on_brand_id                             (brand_id)
#  index_orders_on_brand_id_and_status_and_created_at   (brand_id,status,created_at)
#  index_orders_on_declined_at                          (declined_at) WHERE (declined_at IS NOT NULL)
#  index_orders_on_region_id                            (region_id)
#  index_orders_on_region_id_and_status_and_created_at  (region_id,status,created_at)
#  index_orders_on_status_and_created_at                (status,created_at)
#  index_orders_on_user_id                              (user_id)
#  index_orders_on_user_id_and_status_and_created_at    (user_id,status,created_at)
#
class Order < ApplicationRecord
  FILTER_FIELDS = %w[status brand_name created_at country_name].freeze

  include MeiliSearch::Rails
  extend Pagy::Meilisearch
  ActiveRecord_Relation.include Pagy::Meilisearch

  before_update :update_timestamps

  after_create_commit :notify_admins
  after_update_commit :notify_user
  after_create_commit :set_activity
  after_update :log_status_changes, if: :saved_change_to_status?
  after_update :log_points_changes, if: :saved_change_to_points?

  enum :status, pending: 0, processed: 1, shipped: 2, canceled: 3, approved: 4, declined: 5
  enum :ship_to, home: 0, work: 1

  belongs_to :user, counter_cache: true
  belongs_to :admin, class_name: "User", optional: true
  belongs_to :brand, counter_cache: true
  belongs_to :region, counter_cache: true

  has_many :line_items, -> { includes(:product) }, dependent: :destroy
  has_many :notations, as: :noteable, dependent: :destroy, class_name: "Note"

  validates :user, :brand, :region, :points, presence: true
  validates :points, numericality: {greater_than: 0}
  validates :ship_to, inclusion: {in: ship_tos.keys}

  # Business rule validations
  validate :user_has_sufficient_points, on: :create
  validate :line_items_present
  validate :shipping_address_complete, if: :requires_physical_shipping?
  validate :user_can_place_order
  validate :brand_consistency

  has_one :activity, as: :transactable, dependent: :destroy

  visitable :ahoy_visit

  has_paper_trail

  delegate :name, to: :user, prefix: true
  delegate :name, to: :admin, prefix: true, allow_nil: true
  delegate :name, to: :brand, prefix: true

  accepts_nested_attributes_for :notations

  def name
    "Order by #{user.name} on #{created_at}"
  end

  # Calculated fields and business logic methods
  def total_value
    return 0 unless line_items.any? && user&.address&.country
    line_items.sum do |item|
      item.quantity * (item.product.msrp_for_country(user.address.country.id) || 0)
    end
  end

  def total_items
    line_items.sum(:quantity)
  end

  def estimated_shipping_date
    return nil unless approved?
    # Assume 5-7 business days for standard shipping
    business_days = 7
    date = approved_at.to_date
    business_days.times do
      date += 1.day
      date += 1.day while date.saturday? || date.sunday?
    end
    date
  end

  def processing_time_days
    return nil unless approved_at && created_at
    ((approved_at - created_at) / 1.day).round(1)
  end

  def contains_gift_cards?
    line_items.any?(&:gift_card)
  end

  def gift_card_value
    return 0 unless contains_gift_cards?
    line_items.where(gift_card: true).sum do |item|
      item.quantity * (item.product.gift_card_value || 0)
    end
  end

  def is_high_value?
    points > 500 || total_value > 1000
  end

  def days_since_order
    ((Date.current - created_at.to_date)).to_i
  end

  def status_display
    case status
    when "pending"
      "Awaiting Approval"
    when "approved"
      "Approved - Ready for Processing"
    when "processed"
      "Processed - Ready to Ship"
    when "shipped"
      "Shipped"
    when "canceled"
      "Canceled"
    when "declined"
      "Declined"
    else
      status.humanize
    end
  end

  def can_be_canceled?
    pending? || approved?
  end

  def requires_physical_shipping?
    line_items.any? { |item| !item.product.gift_card }
  end

  # Class methods for reporting and analytics
  def self.monthly_summary(month = Date.current.beginning_of_month)
    where(created_at: month..month.end_of_month)
      .group(:status)
      .group_by_day(:created_at)
      .sum(:points)
  end

  def self.fulfillment_metrics(start_date = 1.month.ago, end_date = Date.current)
    orders_in_period = where(created_at: start_date..end_date)
    total_count = orders_in_period.count

    return {} if total_count.zero?

    {
      total_orders: total_count,
      pending_count: orders_in_period.pending.count,
      approved_count: orders_in_period.approved.count,
      processed_count: orders_in_period.processed.count,
      shipped_count: orders_in_period.shipped.count,
      canceled_count: orders_in_period.canceled.count,
      declined_count: orders_in_period.declined.count,
      fulfillment_rate: (orders_in_period.shipped.count.to_f / total_count * 100).round(2)
    }
  end

  def self.average_order_value
    where(status: [:approved, :processed, :shipped])
      .average(:points)
      &.round(2)
  end

  def self.top_products_ordered(limit = 10)
    joins(line_items: :product)
      .where(status: [:approved, :processed, :shipped])
      .group("products.name")
      .sum("line_items.quantity")
      .sort_by { |_, quantity| -quantity }
      .first(limit)
  end

  def self.regional_order_volume(start_date = 1.month.ago, end_date = Date.current)
    joins(:region)
      .where(created_at: start_date..end_date)
      .where(status: [:approved, :processed, :shipped])
      .group("regions.name")
      .sum(:points)
  end

  def self.gift_card_analytics(start_date = 1.month.ago, end_date = Date.current)
    gift_card_orders = joins(:line_items)
      .where(created_at: start_date..end_date)
      .where(line_items: {gift_card: true})
      .where(status: [:approved, :processed, :shipped])

    {
      gift_card_orders_count: gift_card_orders.distinct.count,
      total_gift_card_value: gift_card_orders.joins(line_items: :product)
        .sum("line_items.quantity * products.gift_card_value"),
      average_gift_card_order: gift_card_orders.average(:points)&.round(2)
    }
  end

  def self.processing_time_analysis
    approved_orders = where(status: [:processed, :shipped]).where.not(approved_at: nil)

    {
      average_processing_days: approved_orders.average("EXTRACT(EPOCH FROM (updated_at - approved_at)) / 86400")&.round(2),
      fastest_processing: approved_orders.minimum("EXTRACT(EPOCH FROM (updated_at - approved_at)) / 86400")&.round(2),
      slowest_processing: approved_orders.maximum("EXTRACT(EPOCH FROM (updated_at - approved_at)) / 86400")&.round(2)
    }
  end

  scope :meilisearch_import, -> { includes(:brand, user: [address: :country]) }

  # Common query scopes
  scope :recent, -> { where(created_at: 1.month.ago..) }
  scope :by_status, ->(status) { where(status: status) }
  scope :fulfillable, -> { where(status: [:approved, :processed]) }
  scope :with_gift_cards, -> { joins(:line_items).where(line_items: {gift_card: true}) }
  scope :for_reporting, -> { includes(:user, :brand, :region, line_items: :product) }
  scope :pending, -> { where(status: :pending) }
  scope :approved, -> { where(status: :approved) }
  scope :processed, -> { where(status: :processed) }
  scope :shipped, -> { where(status: :shipped) }
  scope :canceled, -> { where(status: :canceled) }
  scope :declined, -> { where(status: :declined) }
  scope :this_month, -> { where(created_at: Date.current.beginning_of_month..Date.current.end_of_month) }
  scope :last_month, -> { where(created_at: 1.month.ago.beginning_of_month..1.month.ago.end_of_month) }
  scope :by_brand, ->(brand_id) { where(brand_id: brand_id) }
  scope :by_region, ->(region_id) { where(region_id: region_id) }
  scope :by_user, ->(user_id) { where(user_id: user_id) }
  scope :high_value, -> { where("points > ?", 500) }
  scope :ship_to_home, -> { where(ship_to: :home) }
  scope :ship_to_work, -> { where(ship_to: :work) }
  scope :awaiting_approval, -> { where(status: :pending) }
  scope :needs_processing, -> { where(status: :approved) }

  meilisearch do
    attribute :status, :brand_name, :created_at, :user_name, :user_id, :brand_id, :region_id

    attribute :country_name do
      user.address.country.name
    end

    filterable_attributes [:status, :brand_name, :created_at, :user_id, :brand_id, :region_id, :country_name]
    sortable_attributes [:created_at]
  end

  private

  def notify_user
    UpdatedOrderNotifier.with(order: self).deliver_later(user)
  end

  def notify_admins
    NewOrderNotifier.with(order: self).deliver_later(User.admins)
  end

  def set_activity
    create_activity(wallet: user.wallet, kind: "debit", transactable: self)
  end

  def update_timestamps
    if status_changed?
      if approved?
        self.approved_at = Time.zone.now
        activity.approved!

        user.wallet.debit!(points)
      elsif declined?
        self.declined_at = Time.zone.now
        activity.declined!
      end
    end
  end

  # Custom validation methods
  def user_has_sufficient_points
    return unless user && points
    available_balance = user.wallet&.available_balance || 0
    if available_balance < points
      errors.add(:points, "insufficient balance (#{available_balance} available, #{points} required)")
    end
  end

  def line_items_present
    if line_items.empty?
      errors.add(:line_items, "must have at least one item")
    end
  end

  def shipping_address_complete
    return unless user&.address
    address = user.address

    if address.line1.blank?
      errors.add(:base, "shipping address line 1 is required")
    end

    if address.city.blank?
      errors.add(:base, "shipping city is required")
    end

    if address.zip_code.blank?
      errors.add(:base, "shipping zip code is required")
    end
  end

  def user_can_place_order
    return unless user
    unless user.active_status?
      errors.add(:user, "must be active to place orders")
    end
  end

  def brand_consistency
    return unless brand && user&.store
    if brand != user.store.brand
      errors.add(:brand, "must match your store's brand")
    end
  end

  # Audit logging methods
  def log_status_changes
    old_status = status_before_last_save
    new_status = status
    current_user_email = Current.user&.email || "system"

    Rails.logger.info "Order #{id} status changed from #{old_status} to #{new_status} by #{current_user_email}"

    # Log significant status changes
    if ["approved", "declined", "shipped"].include?(new_status)
      Rails.logger.info "Order #{id} reached milestone status: #{new_status}"
    end

    # Log to audit trail if needed
    if defined?(AuditLog)
      AuditLog.create(
        auditable: self,
        action: "status_change",
        old_value: old_status,
        new_value: new_status,
        user_email: current_user_email,
        metadata: {
          points: points,
          line_items_count: line_items.count,
          user_id: user_id
        }
      )
    end
  end

  def log_points_changes
    old_points = points_before_last_save
    new_points = points
    current_user_email = Current.user&.email || "system"

    Rails.logger.info "Order #{id} points changed from #{old_points} to #{new_points} by #{current_user_email}"

    # Alert on significant point changes
    if old_points && (new_points - old_points).abs > 200
      Rails.logger.warn "Significant point change detected for Order #{id}: #{old_points} -> #{new_points}"
    end
  end
end
