# == Schema Information
#
# Table name: products
#
#  id               :bigint           not null, primary key
#  active           :boolean          default(TRUE)
#  description      :text
#  gift_card        :boolean          default(FALSE)
#  gift_card_value  :integer          default(0)
#  image_data       :jsonb
#  line_items_count :integer          default(0), not null
#  msrp             :decimal(10, 2)
#  name             :string
#  points_earned    :integer
#  points_needed    :integer
#  sales_count      :integer          default(0), not null
#  sku              :string
#  upc              :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  category_id      :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sales_count  (sales_count)
#

class Product < ApplicationRecord
  FILTER_FIELDS = %w[category_name category_brand_name country_name].freeze
  SORT_TO_GROUP = {category_name: "category_name", category_brand_name: "category_brand_name", name: "category_brand_name"}.freeze

  include MeiliSearch::Rails
  include HasBarcode
  extend Pagy::Meilisearch

  belongs_to :category, counter_cache: true
  has_one :brand, through: :category

  has_many :line_items
  has_many :orders, through: :line_items
  has_many :sales

  has_many :prices, dependent: :destroy

  has_and_belongs_to_many :promotions

  normalizes :upc, with: ->(upc) { upc.delete("^0-9") }
  normalizes :sku, with: ->(sku) { sku.delete("^0-9") }

  validates :name, :description, :sku, :category, :upc, presence: true
  validates :upc, upc: true
  validates :sku, uniqueness: true

  has_one_attached :image
  has_barcode :barcode, outputter: :svg, type: Barby::EAN13, value: proc { |p| p.upc[0...12] }

  accepts_nested_attributes_for :prices

  delegate :brand_name, to: :category, prefix: true

  scope :meilisearch_import, -> { includes(category: [:brand], prices: [:country]) }

  meilisearch if: :active do
    attribute :name, :sku, :points_needed, :upc

    attribute :category_name do
      category.name
    end

    attribute :category_brand_name do
      category.brand.name
    end

    attribute :brand_id do
      brand.id
    end

    attribute :country_name do
      country_names
    end

    filterable_attributes [:category_name, :category_brand_name, :brand_id, :country_name]
    sortable_attributes [:category_name, :category_brand_name, :name]
  end

  def country_names
    prices.joins(:country).pluck(:name).uniq
  end

  def points_needed_for_country(id)
    record = prices.detect do |price|
      price.country.id == id
    end

    record.points_needed.presence || 0
  end

  def points_earned_for_country(id)
    record = prices.detect do |price|
      price.country.id == id
    end

    record.points_earned.presence || 0
  end

  def msrp_for_country(id)
    record = prices.detect do |price|
      price.country.id == id
    end

    record.msrp.presence || 0
  end

  def name_with_sku
    "#{name} (#{sku})"
  end

  def points(id)
    "#{points_earned_for_country(id)}/#{points_needed_for_country(id)}"
  end

  def image_path
    image.attached? ? image : "image-missing.svg"
  end

  def image_select_path
    image.attached? ? Rails.application.routes.url_helpers.rails_blob_path(image, only_path: true) : ActionController::Base.helpers.asset_path("image-missing.svg")
  end
end
