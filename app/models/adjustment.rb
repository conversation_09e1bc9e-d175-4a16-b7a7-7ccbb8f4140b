# == Schema Information
#
# Table name: adjustments
#
#  id         :bigint           not null, primary key
#  kind       :integer          default("debit")
#  notes      :text             not null
#  points     :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_adjustments_on_admin_id  (admin_id)
#  index_adjustments_on_user_id   (user_id)
#
class Adjustment < ApplicationRecord
  after_create_commit :update_points
  after_create_commit :add_activity

  enum :kind, debit: 0, credit: 1

  belongs_to :user
  belongs_to :admin, class_name: "User"

  has_one :activity, as: :transactable, dependent: :destroy

  validates :notes, :user, :admin, presence: true
  validates :points, comparison: {less_than_or_equal_to: ->(a) { a.user.wallet.available_points }}, if: -> { debit? && user.present? }

  delegate :name, to: :user, prefix: true
  delegate :name, to: :admin, prefix: true

  def approved?
    true
  end

  def declined?
    false
  end

  private

  def add_activity
    create_activity(wallet: user.wallet, status: "approved", transactable: self, kind: kind)
  end

  def update_points
    case kind
    when "credit"
      user.wallet.credit!(points)
    when "debit"
      user.wallet.debit!(points)
    end
  end
end
