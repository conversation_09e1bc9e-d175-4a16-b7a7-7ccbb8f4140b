# == Schema Information
#
# Table name: store_chains
#
#  id           :bigint           not null, primary key
#  name         :string
#  stores_count :integer          default(0)
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
class StoreChain < ApplicationRecord
  has_many :stores
  has_many :users, through: :stores
  has_many :promotion_locations, as: :promotable, dependent: :destroy
  has_many :promotions, through: :promotion_locations

  validates :name, presence: true

  scope :order_by_name, -> { order(:name) }

  def name_with_city
    name
  end

  def self.group_name
    "Store Chain"
  end
end
