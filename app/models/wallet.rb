# == Schema Information
#
# Table name: wallets
#
#  id              :bigint           not null, primary key
#  current_balance :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_wallets_on_user_id  (user_id)
#
class Wallet < ApplicationRecord
  belongs_to :user
  has_many :activities, -> { order("created_at DESC") }

  self.ignored_columns = ["available_balance"]

  after_create_commit :update_starting_balance
  after_update_commit :broadcast_later

  has_paper_trail

  def credit!(amount)
    self.current_balance += amount
    save
  end

  def debit!(amount)
    self.current_balance -= amount
    save
  end

  def available_points
    current_balance - (Current.cart&.subtotal || 0) - user.orders.where(status: "pending").sum(:points)
  end

  private

  def update_starting_balance
    if user.points_earned.present? && user.points_earned > 0
      adjustment = Adjustment.new
      adjustment.points = user.points_earned
      adjustment.user = user
      adjustment.notes = "Initial point balance from old site"
      adjustment.kind = "credit"
      adjustment.admin = user
      adjustment.save!

      activity = activities.build
      activity.transactable = adjustment
      activity.kind = "credit"
      activity.status = "approved"
      activity.save!

      self.current_balance = user.points_earned
      save
    end
  end

  def broadcast_later
    broadcast_update_later_to(:user, user, target: "current_balance", html: current_balance) if current_balance_changed?
    broadcast_update_later_to(:user, user, target: "available_balance", html: available_points)
  end
end
