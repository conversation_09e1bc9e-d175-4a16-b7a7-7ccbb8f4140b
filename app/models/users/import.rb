require "csv"

class Users::Import
  include ActiveModel::Model

  attr_accessor :file, :imported_count

  def process!
    users = []
    CSV.foreach(file.path, headers: true, header_converters: :symbol) do |row|
      data = row.to_hash
      existing_user = User.find_by_email(data[:email])
      next if existing_user.present?
      state = State.find_by_abbreviation(data[:state])
      store = Store.find_by_old_store_id(data[:old_store_id])
      user = User.new(data.slice(:first_name, :last_name, :street, :city, :zip, :phone_number, :points_earned, :email, :old_user_id))
      user.state = state
      user.store = store
      user.password = "WIfFXwJS%D"
      user.password_confirmation = "WIfFXwJS%D"
      user.status = "active"
      user.time_zone = "Eastern Time (US & Canada)"
      user.skip_confirmation!
      if user.valid?
        users << user
      end
    end

    result = User.import(users)

    @imported_count = result.ids.size
  end

  def save
    process!
    errors.none?
  end
end
