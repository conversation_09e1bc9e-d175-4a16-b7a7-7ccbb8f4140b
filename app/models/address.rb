# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string
#  city             :string           not null
#  latitude         :float
#  line1            :string           not null
#  line2            :string
#  longitude        :float
#  zip_code         :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable             (addressable_type,addressable_id)
#  index_addresses_on_country_id              (country_id)
#  index_addresses_on_latitude_and_longitude  (latitude,longitude)
#  index_addresses_on_state_id                (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#
class Address < ApplicationRecord
  belongs_to :state, -> { includes :region }
  belongs_to :country
  belongs_to :addressable, polymorphic: true

  geocoded_by :full_address

  after_validation :geocode

  normalizes :city, with: ->(city) { city.titleize }

  validates :city, :line1, :zip_code, presence: true

  def full_address
    [line1, line2, city, state.name, country.name].compact.join(", ")
  end
end
