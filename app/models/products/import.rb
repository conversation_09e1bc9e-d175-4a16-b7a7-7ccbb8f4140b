require "csv"

class Products::Import
  include ActiveModel::Model

  attr_accessor :file, :imported_count

  def process!
    products = []
    CSV.foreach(file.path, headers: true, header_converters: :symbol) do |row|
      data = row.to_hash
      category = Category.find_by_old_category_id(data[:old_category_id])
      product = Product.new(data.slice(:name, :description, :msrp, :points_needed, :points_earned, :sku))
      product.category = category
      products << product
    end

    result = Product.import(products)
    @imported_count = result.ids.size
  end

  def save
    process!
    errors.none?
  end
end
