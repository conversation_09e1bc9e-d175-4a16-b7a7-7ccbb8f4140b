# == Schema Information
#
# Table name: categories
#
#  id              :bigint           not null, primary key
#  description     :text
#  name            :string
#  products_count  :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  brand_id        :bigint           not null
#  old_category_id :integer
#
# Indexes
#
#  index_categories_on_brand_id  (brand_id)
#
class Category < ApplicationRecord
  belongs_to :brand

  has_many :products

  validates :name, presence: true

  normalizes :name, with: ->(name) { name.strip }

  delegate :name, to: :brand, prefix: true

  def photo?
    brand.name == PHOTO
  end

  def optics?
    brand.name == SPORTS_OPTICS
  end

  after_create_commit -> { broadcast_append_later_to :categories }
  after_update_commit -> { broadcast_replace_later_to self }
  after_destroy_commit -> { broadcast_remove_to :categories }
end
