# == Schema Information
#
# Table name: states
#
#  id           :bigint           not null, primary key
#  abbreviation :string
#  name         :string
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  country_id   :bigint
#  region_id    :bigint           not null
#
# Indexes
#
#  index_states_on_abbreviation  (abbreviation) UNIQUE
#  index_states_on_country_id    (country_id)
#  index_states_on_region_id     (region_id)
#
class State < ApplicationRecord
  belongs_to :region
  belongs_to :country

  has_many :addresses
  has_many :users, through: :addresses, source: :addressable, source_type: "User"

  validates :name, :abbreviation, :region, presence: true
  validates :abbreviation, uniqueness: true
end
