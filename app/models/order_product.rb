# == Schema Information
#
# Table name: order_products
#
#  id         :bigint           not null, primary key
#  gift_card  :boolean
#  points     :integer
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  order_id   :bigint           not null
#  product_id :bigint           not null
#
# Indexes
#
#  index_order_products_on_order_id    (order_id)
#  index_order_products_on_product_id  (product_id)
#
class OrderProduct < ApplicationRecord
  belongs_to :order
  belongs_to :product

  validates :order, :product, presence: true

  delegate :name, to: :product
end
