# == Schema Information
#
# Table name: brands
#
#  id                   :bigint           not null, primary key
#  approved_sales_count :integer          default(0), not null
#  description          :text
#  name                 :string           not null
#  optics_sales_count   :integer
#  orders_count         :integer          default(0), not null
#  photo_sales_count    :integer
#  sales_count          :integer          default(0), not null
#  users_count          :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_brands_on_name         (name)
#  index_brands_on_sales_count  (sales_count)
#
class Brand < ApplicationRecord
  has_many :stores
  has_many :categories
  has_many :users, through: :stores
  has_many :messages, as: :messageable
  has_many :sales
  has_many :orders

  validates :name, presence: true, uniqueness: true

  scope :photo, -> { where(name: PHOTO) }
  scope :optics, -> { where(name: SPORTS_OPTICS) }

  scope :admins, -> { joins(:users).where(users: {role: :admin}) }
  scope :order_by_name, -> { order(:name) }

  def name_with_city
    name
  end

  def self.group_name
    "Brand"
  end
end
