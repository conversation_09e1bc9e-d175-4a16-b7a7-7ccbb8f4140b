# == Schema Information
#
# Table name: countries
#
#  id           :bigint           not null, primary key
#  abbreviation :string           not null
#  currency     :string
#  name         :string           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
class Country < ApplicationRecord
  has_many :states
  has_many :addresses
  has_many :prices

  scope :available, ->(product) { where.not(id: Price.where(product: product).select(:country_id)) }
end
