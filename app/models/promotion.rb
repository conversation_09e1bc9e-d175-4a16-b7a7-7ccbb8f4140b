# == Schema Information
#
# Table name: promotions
#
#  id         :bigint           not null, primary key
#  ends_at    :datetime
#  multiplier :integer
#  name       :string
#  starts_at  :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class Promotion < ApplicationRecord
  LOCATION_CLASSES = [Store, StoreChain]

  has_and_belongs_to_many :products
  has_many :promotion_locations
  has_many :stores, through: :promotion_locations, source: :promotable, source_type: "Store"
  has_many :store_chains, through: :promotion_locations, source: :promotable, source_type: "StoreChain"

  scope :all_stores, -> { union(joins(:stores).to_sql, joins(store_chains: :stores).to_sql) }

  validates :name, :starts_at, :ends_at, :multiplier, :products, presence: true
  validates :multiplier, numericality: {greater_than: 1}
  validates :starts_at, timeliness: {on_or_after: -> { Time.zone.current.iso8601 }, type: :date}
  validates :ends_at, timeliness: {after: :starts_at, type: :date}
  validates :stores, presence: true, if: proc { |p| p.store_chains.empty? }
  validates :store_chains, presence: true, if: proc { |p| p.stores.empty? }

  def location_sgids=(sgids)
    array_sgids = Array.wrap(sgids).reject(&:empty?)
    locations = GlobalID::Locator.locate_many_signed(array_sgids, only: LOCATION_CLASSES)
    grouped = locations.group_by(&:class)

    LOCATION_CLASSES.each do |klass|
      public_send(:"#{klass.name.underscore.pluralize}=", grouped[klass] || [])
    end
  end

  def location_sgids
    promotion_locations.map { |l| l.promotable.to_sgid.to_s }
  end

  def active?
    (starts_at..ends_at).cover? Time.current.iso8601
  end

  def dates=(dates)
    starts, ends = dates.split(" to ")
    self.starts_at = starts
    self.ends_at = ends
  end
end
