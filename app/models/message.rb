# == Schema Information
#
# Table name: messages
#
#  id               :bigint           not null, primary key
#  messageable_type :string
#  read             :boolean          default(FALSE)
#  read_at          :datetime
#  send_on          :datetime
#  sent_at          :datetime
#  subject          :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  messageable_id   :bigint
#
# Indexes
#
#  index_messages_on_messageable  (messageable_type,messageable_id)
#
class Message < ApplicationRecord
  belongs_to :messageable, polymorphic: true, optional: true
  has_and_belongs_to_many :users

  has_rich_text :body

  validates :subject, presence: true
  validate :body_cant_be_empty

  scope :sent, -> { where.not(sent_at: nil) }
  scope :pending, -> { where(sent_at: nil) }

  def recipient_sgid
    messageable&.to_sgid
  end

  def recipient_sgid=(sgid)
    self.messageable = GlobalID::Locator.locate_signed(sgid)
  end

  def read_count
    Notification.messages(self).read.size
  end

  def user
    nil
  end

  private

  def body_cant_be_empty
    errors.add(:body, "can't be empty") if body.blank?
  end
end
