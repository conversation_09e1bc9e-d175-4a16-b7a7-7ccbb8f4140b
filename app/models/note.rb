# == Schema Information
#
# Table name: notes
#
#  id            :bigint           not null, primary key
#  noteable_type :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  noteable_id   :bigint
#  user_id       :bigint
#
# Indexes
#
#  index_notes_on_noteable  (noteable_type,noteable_id)
#  index_notes_on_user_id   (user_id)
#
class Note < ApplicationRecord
  belongs_to :user
  belongs_to :noteable, polymorphic: true

  has_rich_text :content
end
