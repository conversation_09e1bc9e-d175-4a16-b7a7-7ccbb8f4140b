require "csv"

class Stores::Import
  include ActiveModel::Model

  attr_accessor :file, :brand_id, :imported_count

  def process!
    brand = Brand.find(brand_id)

    stores = []
    CSV.foreach(file.path, headers: true, header_converters: :symbol) do |row|
      data = row.to_hash
      state = State.find_by_abbreviation(data[:state])
      store = Store.new(data.slice(:name, :street, :city, :zip, :phone_number, :account_number, :old_store_id))
      store.state = state
      store.verified = true
      store.brand = brand
      stores << store
    end

    result = Store.import(stores)
    @imported_count = result.ids.size
  end

  def save
    process!
    errors.none?
  end
end
