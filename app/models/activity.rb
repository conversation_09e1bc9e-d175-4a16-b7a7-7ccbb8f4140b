# == Schema Information
#
# Table name: activities
#
#  id                :bigint           not null, primary key
#  kind              :integer          default("debit"), not null
#  status            :integer          default("pending"), not null
#  transactable_type :string           not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  transactable_id   :bigint           not null
#  wallet_id         :bigint           not null
#
# Indexes
#
#  index_activities_on_transactable  (transactable_type,transactable_id)
#  index_activities_on_wallet_id     (wallet_id)
#
class Activity < ApplicationRecord
  enum :kind, debit: 0, credit: 1
  enum :status, pending: 0, approved: 1, declined: 2

  belongs_to :wallet
  belongs_to :transactable, polymorphic: true

  validates :wallet, :transactable, presence: true

  has_paper_trail

  after_create_commit -> { broadcast_prepend_later_to [transactable.user, :activities] }
end
