class ApplicationRecord < ActiveRecord::Base
  self.abstract_class = true

  PHOTO = "Photo".freeze
  SPORTS_OPTICS = "Sports Optics".freeze

  attr_reader :sortable_columns

  def self.sort_by_params(column, direction)
    sortable_column = sortable_columns.include?(column) ? column : "created_at"
    order(sortable_column => direction)
  end

  def self.sortable_columns
    @sortable_columns ||= columns.map(&:name) - ["id"]
  end
end
