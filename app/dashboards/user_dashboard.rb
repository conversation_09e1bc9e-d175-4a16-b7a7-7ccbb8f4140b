require "administrate/base_dashboard"

class UserDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    address: Field::HasOne,
    adjustments: Field::HasMany,
    admin_brand: Field::BelongsTo,
    admin_cart: Field::HasOne,
    admin_region: Field::BelongsTo,
    approved: Field::Boolean,
    approved_at: Field::Date,
    avatar_attachment: Field::HasOne,
    avatar_blob: Field::HasOne,
    avatar_data: Field::String.with_options(searchable: false),
    city: Field::String,
    confirmation_sent_at: Field::DateTime,
    confirmation_token: Field::String,
    confirmed_at: Field::DateTime,
    current_sign_in_at: Field::DateTime,
    current_sign_in_ip: Field::String,
    email: Field::String,
    email_frequency: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    encrypted_password: Field::String,
    failed_email: Field::<PERSON><PERSON><PERSON>,
    first_name_ciphertext: Field::Text,
    last_name: Field::String,
    last_seen: Field::DateTime,
    last_sign_in_at: Field::DateTime,
    last_sign_in_ip: Field::String,
    notifications: Field::HasMany,
    old_user_id: Field::Number,
    orders: Field::HasMany,
    phone_number: Field::String,
    points_earned: Field::Number,
    region: Field::HasOne,
    remember_created_at: Field::DateTime,
    reset_password_sent_at: Field::DateTime,
    reset_password_token: Field::String,
    role: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    sales: Field::HasMany,
    settings: Field::String.with_options(searchable: false),
    sign_in_count: Field::Number,
    slug: Field::String,
    state: Field::BelongsTo,
    status: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    store: Field::BelongsTo,
    store_zip: Field::String,
    street_ciphertext: Field::Text,
    time_zone: Field::String,
    unconfirmed_email: Field::String,
    username: Field::String,
    versions: Field::HasMany,
    visits: Field::HasMany,
    wallet: Field::HasOne,
    zip_ciphertext: Field::Text,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    last_name
    email
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    address
    adjustments
    admin_brand
    admin_cart
    admin_region
    approved
    approved_at
    avatar_attachment
    avatar_blob
    avatar_data
    city
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    email
    email_frequency
    encrypted_password
    failed_email
    first_name_ciphertext
    last_name
    last_seen
    last_sign_in_at
    last_sign_in_ip
    notifications
    old_user_id
    orders
    phone_number
    points_earned
    region
    remember_created_at
    reset_password_sent_at
    reset_password_token
    role
    sales
    settings
    sign_in_count
    slug
    state
    status
    store
    store_zip
    street_ciphertext
    time_zone
    unconfirmed_email
    username
    versions
    visits
    wallet
    zip_ciphertext
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    address
    adjustments
    admin_brand
    admin_cart
    admin_region
    approved
    approved_at
    avatar_attachment
    avatar_blob
    avatar_data
    city
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    email
    email_frequency
    encrypted_password
    failed_email
    first_name_ciphertext
    last_name
    last_seen
    last_sign_in_at
    last_sign_in_ip
    notifications
    old_user_id
    orders
    phone_number
    points_earned
    region
    remember_created_at
    reset_password_sent_at
    reset_password_token
    role
    sales
    settings
    sign_in_count
    slug
    state
    status
    store
    store_zip
    street_ciphertext
    time_zone
    unconfirmed_email
    username
    versions
    visits
    wallet
    zip_ciphertext
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how users are displayed
  # across all pages of the admin dashboard.
  #
  def display_resource(user)
    "#{user.name} (#{user.email})"
  end
end
