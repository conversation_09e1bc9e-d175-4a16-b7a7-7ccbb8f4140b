require "administrate/base_dashboard"

class SaleDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    activity: Field::HasOne,
    admin: Field::BelongsTo,
    ahoy_visit: Field::BelongsTo,
    approved_at: Field::DateTime,
    brand: Field::BelongsTo,
    notations: Field::Has<PERSON>any,
    notes: Field::Text,
    origin: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    points: Field::Number,
    product: Field::BelongsTo,
    promotion: Field::BelongsTo,
    region: Field::BelongsTo,
    serial_number: Field::String,
    sold_at: Field::DateTime,
    state: Field::HasOne,
    status: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    user: Field::BelongsTo,
    versions: Field::HasMany,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    sold_at
    user
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    sold_at
    user
    brand
    admin
    approved_at
    origin
    points
    product
    region
    serial_number
    status
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    activity
    admin
    ahoy_visit
    approved_at
    brand
    notations
    notes
    origin
    points
    product
    promotion
    region
    serial_number
    sold_at
    state
    status
    user
    versions
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how sales are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(sale)
  #   "Sale ##{sale.id}"
  # end
end
