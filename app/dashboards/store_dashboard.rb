require "administrate/base_dashboard"

class StoreDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    account_channel_id: Field::Number,
    account_number: Field::String,
    account_type_id: Field::Number,
    address: Field::HasOne,
    brand: Field::BelongsTo,
    city: Field::String,
    messages: Field::<PERSON><PERSON>any,
    name: Field::String,
    old_store_id: Field::Number,
    phone_number: Field::String,
    promotion_locations: Field::HasMany,
    promotions: Field::HasMany,
    region: Field::HasOne,
    state: Field::HasOne,
    state_id: Field::Number,
    status: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    store_chain: Field::BelongsTo,
    street: Field::String,
    unit: Field::String,
    users: Field::Has<PERSON>any,
    users_count: Field::Number,
    verified: Field::Boolean,
    zip: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    name
    brand
    users_count
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    name
    brand
    phone_number
    region
    status
    store_chain
    users
    users_count
    verified
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    name
    brand
    phone_number
    status
    store_chain
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how stores are displayed
  # across all pages of the admin dashboard.
  #
  def display_resource(store)
    store.name
  end
end
