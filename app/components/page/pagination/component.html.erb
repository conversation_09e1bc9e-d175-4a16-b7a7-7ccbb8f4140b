<% a = pagy_anchor(pagy) %>
<div class="flex justify-between flex-1 sm:hidden">
  <% if pagy.prev %>
    <%== a.(pagy.prev, "Previous", aria_label: 'previous', classes: 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50') %>
  <% end %>
  <% if pagy.next %>
    <%== a.(pagy.next, "Next", aria_label:'next', classes: 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50') %>
  <% end %>
</div>
<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
  <% if !calendar %>
    <div>
      <p class="text-sm text-gray-700">
        Showing
        <span class="font-medium"> <%= pagy.from %></span> to
        <span class="font-medium"> <%= pagy.to %></span> of
        <span class="font-medium"> <%= pagy.count %></span> results
      </p>
    </div>
  <% end %>
  <div>
    <nav aria-label="Pagination" class="relative z-0 inline-flex -space-x-px rounded-md shadow-xs">
      <% if pagy.prev %>
        <%== a.(pagy.prev, icon(name: "chevron-left", class: "h-4 w-4"), classes: 'relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50') %>
      <% end %>
      <% pagy.series.each do |item| %>
        <% if item.is_a?(Integer) %>
          <%== a.(item, classes: 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50') %>
        <% elsif item.is_a?(String) %>
          <span class="relative z-10 inline-flex items-center px-4 py-2 text-sm font-medium border text-zeiss-600 border-zeiss-500 bg-zeiss-50"><%= item %></span>
        <% elsif item == :gap %>
          <span class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">&hellip;</span>
        <% end %>
      <% end %>
      <% if pagy.next %>
        <%== a.(@pagy.next, icon(name: "chevron-right", class: "w-4 h-4"), classes: 'relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50') %>
      <% end %>
    </nav>
  </div>
</div>
