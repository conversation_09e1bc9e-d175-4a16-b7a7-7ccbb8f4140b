<div class="relative inline-block" x-data x-menu>
  <div>
    <button x-menu:button class="flex items-center text-gray-400 rounded-full hover:text-gray-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" type="button">
      <span class="sr-only">Open menu</span>
      <%= button || icon(name: "ellipsis-vertical", class: "h-5 w-5") %>
    </button>
  </div>
  <div
    x-menu:items
    class="absolute right-0 z-20 w-56 mt-2 transition origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden"
    x-cloak
    x-transition:enter="ease-out duration-100"
    x-transition:enter-end="opacity-100 scale-100"
    x-transition:enter-start="opacity-0 scale-95"
    x-transition:leave="ease-in duration-75"
    x-transition:leave-end="opacity-0 scale-95"
    x-transition:leave-start="opacity-100 scale-100">
    <%= header if header? %>
    <div class="py-1" role="none">
      <% items.each do |item| %>
        <%= item %>
      <% end %>
    </div>
  </div>
</div>
