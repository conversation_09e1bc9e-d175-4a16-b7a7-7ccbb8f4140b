<div x-data="{filterOpen: false}">
  <% filters.each do |filter| %>
    <%= filter %>
  <% end %>
  <div class="fixed inset-0 z-30 flex sm:hidden" role="dialog" aria-modal="true" x-show="filterOpen" x-cloak>
    <div classs="fixed inset-0 bg-black bg-opacity-25 transition-opacity ease-linear" :aria-hidden="filterOpen" x-show="filterOpen" x-transition:enter="duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" x-cloak></div>
    <div id="mobile" class="relative flex flex-col w-full h-full max-w-xs py-4 pb-6 ml-auto overflow-y-auto transition ease-in-out bg-white shadow-xl" x-show="filterOpen" x-transition:enter="duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full" x-cloak @click.outside="filterOpen = false">
      <div class="flex items-center justify-between px-4">
        <h2 class="text-lg font-medium text-gray-900">Filters</h2>
        <button type="button" class="flex items-center justify-center w-10 h-10 p-2 -mr-2 text-gray-400 bg-white rounded-md hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-zeiss-500" @click="filterOpen = false">
          <span class="sr-only">Close menu</span>
          <%= icon name: "times", weight: "solid", class: "h-6 w-6" %>
        </button>
      </div>
    </div>
  </div>
  <div class="max-w-3xl px-4 mx-auto text-center sm:px-6 lg:max-w-7xl lg:px-8">
    <section class="py-4" aria-labelledby="filter-heading">
      <h2 class="sr-only" id="filter-heading"><%= name %> filters</h2>
      <div class="flex items-center justify-between">
        <div class="relative inline-block text-left" x-data="{sortOpen: false}">
          <% if sort_options %>
            <div>
              <button type="button" class="inline-flex justify-center text-sm font-medium text-gray-700 group hover:text-gray-900" id="mobile-menu-button" :aria-expanded="sortOpen" aria-haspopup="true" @click="sortOpen = !sortOpen">
                Sort
                <%= icon name: "chevron-down", class: "shrink-0 -mr-1 ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" %>
              </button>
            </div>
            <div class="absolute left-0 z-10 w-40 mt-2 transition ease-out origin-top-left bg-white rounded-md shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-hidden" role="menu" aria-orientation="vertical" aria-labelledby="mobile-menu-button" tabindex="-1" x-cloak x-show="sortOpen" x-transition:enter="duration-100" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="duration-75" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95" @click.outside="sortOpen = false">
              <div class="py-1" role="none">
                <% sort_options.each do |option| %>
                  <%= link_to option[:label].titleize, request.params.merge(sort: option[:option]), class: "block px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100", role: "menuitem", tabindex: "-1" %>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        <button type="button" class="inline-block text-sm font-medium text-gray-700 hover:text-gray-900 sm:hidden" @click="filterOpen = true">
          Filters
        </button>
        <div id="desktop" class="hidden sm:flex sm:items-baseline sm:space-x-8">
        </div>
      </div>
    </section>
  </div>
</div>
