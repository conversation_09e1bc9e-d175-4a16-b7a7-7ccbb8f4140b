<template x-teleport="#desktop">
  <div :id="$id('single-menu')" class="relative inline-block text-left" x-id="['single-menu']" x-data="{menuOpen: false}">
    <div>
      <button type="button" class="inline-flex items-center justify-center text-sm font-medium text-gray-700 group hover:text-gray-900" :aria-expanded="menuOpen" @click="menuOpen = !menuOpen">
        <span><%= name %></span>
        <%= icon name: "chevron-down", class: "shrink-0 -mr-1 ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" %>
      </button>
    </div>
    <div class="absolute right-0 z-10 w-56 mt-2 transition origin-top-right bg-white rounded-md shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-hidden" x-show="menuOpen" @click.outside="menuOpen = false" x-cloak x-transition:enter="ease-out duration-100" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-75" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95" x-id="['menu-item']">
      <div class="py-1">
        <% options.each do |option| %>
          <%= link_to option[:title], option[:url], class: "block px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100", role: "menuitem", tabindex: "-1", ":id": "$id('menu-item')" %>
        <% end %>
      </div>
    </div>
  </div>
</template>
<template x-teleport="#mobile">
  <div class="px-4 py-6 border-t border-gray-200" x-data="{optionOpen: false}" x-id="['filter-section']">
    <h3 class="flow-root -mx-2 -my-3">
      <button type="button" class="flex items-center justify-between w-full px-2 py-3 text-sm text-gray-400 bg-white" :aria-controls="$id('filter-section')" :aria-expanded="optionOpen" @click="optionOpen = !optionOpen">
        <span class="font-medium text-gray-900"><%= name %></span>
        <%= icon name: "chevron-down", class: "h-5 w-5", ":class": "optionOpen ? 'rotate-180' : 'rotate-0'" %>
      </button>
    </h3>
    <div class="pt-6" :id="$id('filter-section')" x-show="optionOpen" x-cloak>
      <div class="space-y-2">
        <% options.each do |option| %>
          <%= link_to option[:title], option[:url], class: "block px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100", role: "menuitem", tabindex: "-1", ":id": "$id('menu-item')" %>
        <% end %>
      </div>
    </div>
  </div>
</template>
