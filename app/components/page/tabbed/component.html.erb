<div x-data="tabs(<%= (tabs.each_with_index.map{|t,i| {id:i,title:t.title}}).to_json %>, '<%= header.title.downcase %>_tab')" class="py-6">
  <%= header %>
  <main class="my-8">
    <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
      <% tabs.each_with_index do |tab, i| %>
        <div class="flex flex-col" x-cloak="<%= tab.cloak %>" x-show="activeTab === <%= i %>">
          <%= tab %>
        </div>
      <% end %>
    </div>
  </main>
</div>
