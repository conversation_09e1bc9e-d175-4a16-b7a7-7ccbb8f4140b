class Page::StatusBadge < ApplicationComponent
  option :status, Types::Coercible::Symbol

  @@classes = %w[mt-2 flex items-center text-sm text-gray-500]

  def call
    @classes = @@classes.dup

    tag.div class: @classes << color do
      concat(icon(name: "traffic-light-#{icon_name}", weight: :duotone, class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400", style: "--fa-secondary-color: var(--status-#{icon_color});"))
      concat(status.to_s.titleize)
    end
  end

  private

  def color
    case status
    when :pending
      %w[text-yellow-800]
    when :approved
      %w[text-green-800]
    when :declined
      %w[text-red-800]
    when :processed
      %w[text-orange-800]
    end
  end

  def icon_name
    case status
    when :pending
      "slow"
    when :approved
      "go"
    when :declined
      "stop"
    when :processed
      "go"
    end
  end

  def icon_color
    case status
    when :pending
      "yellow"
    when :approved
      "green"
    when :declined
      "red"
    when :processed
      "green"
    end
  end
end
