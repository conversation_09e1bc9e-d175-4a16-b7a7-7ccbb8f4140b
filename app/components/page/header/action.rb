class Page::Header::Action < ApplicationComponent
  option :title, Dry::Types["strict.string"]
  option :primary, Dry::Types["strict.bool"], default: proc { false }
  option :url, Dry::Types["strict.string"]
  option :visible, Dry::Types["strict.bool"], default: proc { true }
  option :delete, Dry::Types["strict.bool"], default: proc { false }

  def before_render
    @classes = primary ? %w[border-transparent text-white bg-zeiss-600 hover:bg-zeiss-700 shadow-zeiss-500/50] : %w[border-gray-300 text-gray-700 bg-white hover:bg-gray-50]
    @classes << %w[inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500]
  end

  def call
    delete ? link_to(title, url, class: @classes, data: {turbo_method: :delete, turbo_confirm: "Are you sure?"}) : link_to(title, url, class: @classes)
  end

  def render?
    visible
  end
end
