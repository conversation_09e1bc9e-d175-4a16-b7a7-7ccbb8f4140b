<div class="mt-3">
  <div class="sm:hidden">
    <label class="sr-only" for="tabs">Select a tab</label>
    <select @change="activeTab = Number($el.value)" class="block w-full py-2 pl-3 pr-10 text-base border-gray-300 rounded-md focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" id="tabs" name="tabs" x-model="activeTab">
      <template hidden="hidden" x-for="tab in tabs">
        <option :value="tab.id" x-text="tab.title"></option>
      </template>
    </select>
  </div>
  <div class="hidden sm:block">
    <div class="border-b border-gray-200">
      <nav class="flex -mb-px space-x-4">
        <template hidden="hidden" x-for="tab in tabs">
          <%= link_to "#", "@click.prevent": "activeTab = tab.id", class: "whitespace-nowrap pb-2 px-1 border-b-2 border-transparent font-medium text-sm", ":aria-current": "activeTab === tab.id", ":class": "activeTab === tab.id ? activeClasses : inactiveClasses" do %>
            <template hidden="hidden" x-if="tab.icon">
              <i :class="`fa-${tab.icon}`" class="mr-2 fa-fw fa-thin"></i>
            </template>
            <span x-text="tab.title"></span>
          <% end %>
        </template>
      </nav>
    </div>
  </div>
</div>
