<div x-data="{value: <%= user.active_status? %>}">
  <%= form_with model: user do |form| %>
    <%= form.hidden_field :status, ":value": "value ? 'inactive' : 'active'" %>
    <%= form.button type: :submit, class: "bg-gray-200 relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500", role: "switch", ":aria-checked": "value", ":class": "value ? 'bg-green-600' : 'bg-red-600'" do %>
      <span class="sr-only">User status</span>
      <span aria-hidden="true" class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow-sm pointer-events-none ring-0" :class="value ? 'translate-x-5' : 'translate-x-0'"></span>
    <% end %>
  <% end %>
</div>
