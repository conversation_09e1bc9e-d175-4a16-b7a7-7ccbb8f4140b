class Page::Button::Component < ApplicationComponent
  option :title, ::Types::Coercible::String
  option :icon_name, ::Types::Strict::String, optional: true
  option :size, ::Types::Coercible::String.enum("small", "medium", "large"), default: proc { "medium" }, optional: true
  option :click, ::Types::Strict::String, optional: true
  option :primary, ::Types::Bool, default: proc { true }, optional: true

  def before_render
    @classes = %w[relative inline-flex items-center font-medium border rounded-md focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500]
    sm = %w[text-xs px-2.5 py-1.5]
    md = %w[text-sm px-4 py-2]
    lg = %w[text-base px-6 py-3]
    color = primary ? %w[border-transparent text-white bg-zeiss-600 hover:bg-zeiss-700 shadow-zeiss-500/50] : %w[border-gray-300 text-gray-700 bg-white shadow-sm hover:bg-gray-50]

    @classes << color

    @classes << case size
    when "small"
      sm
    when "medium"
      md
    when "large"
      lg
    end
  end
end
