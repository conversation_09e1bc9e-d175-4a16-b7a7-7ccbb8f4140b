class Sidebar::Link < ApplicationComponent
  delegate :allowed_to?, to: :helpers

  @@classes = {
    common: {
      link: %w[group flex items-center px-2 py-2 text-base font-medium rounded-md],
      icon: %w[mr-4 shrink-0 h-6 w-6]
    },
    active: {
      link: %w[bg-gray-100 text-gray-900],
      icon: %w[fill-gray-900]
    },
    inactive: {
      link: %w[hover:bg-gray-50 hover:text-gray-900 text-gray-600],
      icon: %w[fill-gray-400 group-hover:fill-gray-500]
    }
  }

  option :name, ::Types::String
  option :url, ::Types::String
  option :link_icon, ::Types::String
  option :visible, ::Types::Symbol

  def before_render
    if request.path == url
      @link_classes = classes.dig(:common, :link) + classes.dig(:active, :link)
      @icon_classes = classes.dig(:common, :icon) + classes.dig(:active, :icon)
    else
      @link_classes = classes.dig(:common, :link) + classes.dig(:inactive, :link)
      @icon_classes = classes.dig(:common, :icon) + classes.dig(:inactive, :icon)
    end
  end

  def classes
    @@classes
  end

  def render?
    allowed_to?(:index?, visible)
  end
end
