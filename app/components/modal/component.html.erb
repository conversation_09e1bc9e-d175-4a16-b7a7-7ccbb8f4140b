<%= tag.div "x-data": ("{#{variable}: false}" if button?) do %>
  <%= button if button? %>
  <template x-teleport="body">
    <div class="fixed inset-0 z-20 overflow-y-auto" x-dialog x-model="<%= variable %>" x-cloak x-on:closedialog="$dialog.close()" id="<%= id %>">
      <div class="flex items-end justify-center min-h-full p-4 text-center sm:items-center sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500/75" x-dialog:overlay x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" x-cloak></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="relative px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all bg-white rounded-lg shadow-xl sm:p-6 sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" x-dialog:panel x-cloak x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
          <%= content %>
        </div>
      </div>
    </div>
  </template>
<% end %>
