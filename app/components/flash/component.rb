class Flash::Component < ApplicationComponent
  ICONS = {
    notice: "circle-info",
    alert: "circle-exclamation",
    error: "octagon-exclamation",
    info: "info",
    warning: "shield-exclamation",
    success: "circle-check"
  }

  COLORS = {
    notice: {background: "bg-blue-600", icon: "bg-blue-700"},
    alert: {background: "bg-red-600", icon: "bg-red-700"},
    error: {background: "bg-red-600", icon: "bg-red-700"},
    info: {background: "bg-blue-600", icon: "bg-blue-700"},
    warning: {background: "bg-yellow-600", icon: "bg-yellow-700"},
    success: {background: "bg-green-600", icon: "bg-green-700"}
  }

  option :type, Dry::Types["coercible.string"]
  option :message, Dry::Types["strict.string"]

  option :flash_icon, default: proc { |value| (value || ICONS[type.to_sym]).to_s }
  option :background_color, default: proc { |value| (value || COLORS.dig(type.to_sym, :background)).to_s }
  option :icon_color, default: proc { |value| (value || COLORS.dig(type.to_sym, :icon)).to_s }
end
