class Notifications::Item < ApplicationComponent
  def initialize(item:)
    @item = item
    @notification = @item.to_notification
  end

  def notification_type
    @notification.class.name.demodulize.underscore
  end

  def notification_data
    return @notification.params[:sale] if sale?
    return @notification.params[:order] if order?
    return @notification.params[:message] if message?
  end

  def notification_icon
    @notification.read? ? "envelope-open" : "envelope"
  end

  private

  def sale?
    @notification.params.has_key?(:sale)
  end

  def order?
    @notification.params.has_key?(:order)
  end

  def message?
    @notification.params.has_key?(:message)
  end
end
