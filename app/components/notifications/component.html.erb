<div class="relative inline-block text-left" x-data="{noteOpen: false, notes: <%= @notifications.any? %>}">
  <button class="p-1 text-gray-400 bg-white rounded-full hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" id="notifications-menu" aria-haspopup="true" :aria-expanded="noteOpen" @click.prevent="noteOpen = true">
    <span class="sr-only">View notifications</span>
    <div class="relative inline-block">
      <%= icon name: "bell", weight: :thin, class: "h-6 w-6" %>
      <span class="absolute bottom-0 right-0 block w-2 h-2 rounded-full bg-zeiss-500 ring-2 ring-white" x-show="notes" x-cloak></span>
    </div>
  </button>
  <div aria-labelledby="notifications-title" aria-modal="true" class="fixed inset-0 z-10 overflow-hidden" role="dialog" x-cloak x-show="noteOpen" x-trap.noscroll="noteOpen">
    <div class="absolute inset-0 overflow-hidden">
      <div aria-hidden="true" class="absolute inset-0 transition-opacity ease-in-out bg-gray-500 bg-opacity-75" x-cloak x-show="noteOpen" x-transition:enter="duration-500" x-transition:enter-end="opacity-100" x-transition:enter-start="opacity-0" x-transition:leave="duration-500" x-transition:leave-end="opacity-0" x-transition:leave-start="opacity-100">
      </div>
      <div class="fixed inset-y-0 right-0 flex max-w-full pl-10">
        <div @click.outside="noteOpen = false" class="w-screen max-w-md transition ease-in-out" x-cloak x-show="noteOpen" x-transition:enter="duration-500 sm:duration-700" x-transition:enter-end="translate-x-0" x-transition:enter-start="translate-x-full" x-transition:leave="duration-500 sm:duration-700" x-transition:leave-end="translate-x-full" x-transition:leave-start="translate-x-0">
          <div class="flex flex-col h-full bg-white divide-y divide-gray-200 shadow-xl">
            <div class="flex flex-col flex-1 min-h-0 py-6 overflow-y-scroll">
              <div class="px-4 sm:px-6">
                <div class="flex items-start justify-between">
                  <h2 class="text-lg font-medium text-gray-900" id="notifications-title">Notifications</h2>
                  <div class="flex items-center ml-3 h-7">
                    <button @click.prevent="noteOpen = false" class="text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-zeiss-500">
                      <span class="sr-only">Close panel</span>
                      <%= icon name: "times", weight: :solid, class: "h-6 w-6" %>
                    </button></div>
                </div>
              </div>
              <div class="relative flex-1 px-4 mt-6 sm:px-6">
                <div class="flow-root mt-6">
                  <ul class="-my-5 divide-y divide-gray-200" role="list">
                    <%= render Notifications::Item.with_collection @notifications %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
