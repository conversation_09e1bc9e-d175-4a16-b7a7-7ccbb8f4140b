<li class="py-4">
  <div class="flex items-center space-x-4">
    <div class="shrink-0">
      <%= icon name: notification_icon, class: "fa-fw fa-3x h-8 w-8 text-gray-400" %>
    </div>
    <div class="flex-1 min-w-0">
      <p class="text-sm font-medium text-gray-900 truncate">
        <%= t(".#{notification_type}_message") %>
      </p>
      <p class="text-sm text-gray-500 truncate">
        <%= t(".#{notification_type}_description", name: notification_data&.user&.name || "System") %>
      </p>
    </div>
    <div>
      <%= link_to "View", [:read, @item], class: "inline-flex items-center shadow-xs px-2.5 py-0.5 border border-gray-300 text-sm leading-5 font-medium rounded-full text-gray-700 bg-white hover:bg-gray-50" %>
    </div>
  </div>
</li>
