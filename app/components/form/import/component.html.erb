<%= form_with model: resource, url: url do |f| %>
  <%= render "shared/error_messages", resource: f.object %>
  <div class="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
    <div class="sm:flex sm:items-start">
      <div class="flex items-center justify-center w-12 h-12 mx-auto rounded-full shrink-0 bg-zeiss-100 sm:mx-0 sm:h-10 sm:w-10">
        <%= icon(name: "file-import", class: "w-6 h-6 text-zeiss-600") %>
      </div>
      <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
        <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
          Import
        </h3>
        <div class="flex flex-col mt-2 space-y-4">
          <% if uses_brand %>
            <div>
              <%= f.label :brand_id, class: "block text-sm font-medium text-gray-700" %>
              <%= f.collection_select :brand_id, Brand.all, :id, :name, {}, class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
            </div>
          <% end %>
          <div class="flex justify-center max-w-lg px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <%= icon(name: "file-arrow-up", class: "h-12 w-12 mx-auto text-gray-400") %>
              <div class="flex text-sm text-gray-600">
                <%= f.label :file, class: "relative font-medium bg-white rounded-md cursor-pointer text-zeiss-600 hover:text-zeiss-500 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-zeiss-500" do %>
                  <span>Upload a file</span>
                  <%= f.file_field :file, class: "sr-only", accept: "csv" %>
                <% end %>
              </div>
              <p class="text-xs text-gray-500">
                CSV up to 10MB
              </p>
            </div>
          </div>
          <p class="prose-sm text-gray-500">
            The file should contain a header row with the following columns:
          </p>
          <div class="max-w-sm overflow-x-auto rounded-lg bg-gray-50">
            <div class="px-4 py-5 sm:p-6">
              <pre class="text-sm text-gray-600"><%= columns %></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
    <%= f.submit "Import", class: "inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-zeiss-600 border border-transparent rounded-md shadow-xs hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 sm:ml-3 sm:w-auto sm:text-sm" %>
    <button type="button" class="inline-flex justify-center w-full px-4 py-2 mt-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="modalOpen = false">
      Cancel
    </button>
  </div>
<% end %>
