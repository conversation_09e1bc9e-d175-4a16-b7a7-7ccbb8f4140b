<% content = capture do %>
  <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
    <dt>
      <div class="absolute p-3 text-center rounded-md <%= color_classes %>">
        <%= icon name: icon_name, class: "h-6 w-6 text-white" %>
      </div>
      <p class="ml-16 text-sm font-medium text-gray-500 truncate"><%= title %></p>
      <% if subtitle.present? %>
        <p class="ml-16 text-xs text-gray-400 truncate"><%= subtitle %></p>
      <% end %>
    </dt>
    <dd class="flex items-baseline pb-2 ml-16">
      <p class="text-2xl font-semibold text-gray-900">
        <%= formatted_value %>
      </p>
      <% if change.present? %>
        <p class="ml-2 text-sm font-medium <%= change_classes %>">
          <%= change %>
        </p>
      <% end %>
    </dd>
  </div>
<% end %>

<% if link_to.present? %>
  <%= link_to link_to, class: "block hover:bg-gray-50 transition-colors duration-200" do %>
    <%= content %>
  <% end %>
<% else %>
  <%= content %>
<% end %>
