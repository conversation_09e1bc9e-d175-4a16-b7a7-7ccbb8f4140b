class Dashboard::QuickActionComponent < ApplicationComponent
  option :title, type: Types::String
  option :icon, type: Types::String
  option :url, type: Types::String
  option :description, type: Types::String, optional: true
  option :color, type: Types::String, default: proc { "gray" }
  option :badge, type: Types::String, optional: true
  option :badge_color, type: Types::String, default: proc { "red" }

  private

  def icon_classes
    case color
    when "blue"
      "text-blue-400"
    when "green"
      "text-green-400"
    when "yellow"
      "text-yellow-400"
    when "red"
      "text-red-400"
    when "purple"
      "text-purple-400"
    when "indigo"
      "text-indigo-400"
    else
      "text-gray-400"
    end
  end

  def badge_classes
    case badge_color
    when "red"
      "bg-red-100 text-red-800"
    when "yellow"
      "bg-yellow-100 text-yellow-800"
    when "green"
      "bg-green-100 text-green-800"
    when "blue"
      "bg-blue-100 text-blue-800"
    else
      "bg-gray-100 text-gray-800"
    end
  end
end
