<div class="sticky top-0 z-20 flex h-16 bg-white shrink-0">
  <button @click="sidebarOpen = true" class="px-4 text-gray-500 border-r border-gray-200 focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-zeiss-500 md:hidden">
    <span class="sr-only">Open sidebar</span>
    <%= icon name: "bars", weight: :solid, class: "h-6 w-6" %>
  </button>
  <div class="flex justify-between flex-1 px-4">
    <div class="flex flex-1">
      <form action="/search" class="flex w-full md:ml-0" method="get">
        <label class="sr-only">Search</label>
        <div class="relative w-full text-gray-400 focus-within:text-gray-600">
          <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
            <%= icon name: "magnifying-glass", weight: :thin, class: "w-6 h-6" %>
          </div>
          <input class="block w-full h-full py-2 pl-8 pr-3 text-gray-900 placeholder-gray-500 border-transparent focus:outline-hidden focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm" name="q" placeholder="Search" type="search" />
        </div>
      </form>
    </div>
    <div class="flex items-center ml-4 space-x-3 md:ml-6">
      <%# render Notifications::Component.new %>
      <%= render Searchbar::Button.with_collection [
        {text: "View points", icon: "wallet", url: user_path(current_user), visible: true, "x-tooltip": "#{current_user.wallet&.available_points}"},
        {text: "View cart", icon: "cart-shopping", url: cart_path(Current.cart.presence || 0), visible: (Current.cart.present? && Current.cart.line_items.any? ), count: Current.cart&.line_items&.size}
      ] %>
      <%= render Page::Menu::Component.new do |menu| %>
        <% menu.button do %>
          <%= image_tag current_user.avatar_path, class: "h-8 w-8 rounded-full" %>
        <% end %>
        <% menu.header do %>
          <div class="px-4 py-3 border-b border-gray-200">
            <p class="text-sm">Signed in as</p>
            <p class="text-sm font-medium text-gray-900 truncate"><%= current_user.name %></p>
          </div>
        <% end %>
        <% menu.with_item_link(name: "Profile", url: user_path(current_user)) %>
        <% menu.with_item_link(name: "Inbox", url: messages_path) %>
        <% menu.with_item_link(name: "Sidekiq", url: sidekiq_web_path, visible: current_user.super_admin?) %>
        <% menu.with_item_link(name: "Blazer", url: blazer_path, visible: current_user.super_admin?) %>
        <% menu.with_item_link(name: "Maintenance", url: maintenance_tasks_path, visible: current_user.super_admin?) %>s
        <% menu.with_item_link(name: "Sign out", url: destroy_user_session_path, data: {turbo_method: :delete}) %>
      <% end %>
    </div>
  </div>
</div>
