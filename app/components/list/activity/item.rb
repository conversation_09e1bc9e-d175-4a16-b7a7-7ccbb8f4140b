class List::Activity::Item < ApplicationComponent
  delegate :local_time_ago, to: :helpers

  option :transaction
  option :type, Dry::Types["strict.string"]
  option :date
  option :kind, Dry::Types["coercible.string"]
  option :declined, Types::Bool

  def icon_name
    case type
    when "Sale"
      "receipt"
    when "Order"
      "cart-shopping"
    when "Adjustment"
      "arrow-down-arrow-up"
    end
  end

  def icon_color
    case kind
    when "credit"
      "bg-emerald-400"
    when "debit"
      "bg-rose-400"
    end
  end

  def message
    case type
    when "Sale"
      "#{transaction.user.name} sold <span class='truncate'>#{transaction.product&.name || 'Unknown Product'}</span> for #{transaction.points} points".html_safe
    when "Order"
      "#{transaction.user.name} placed an order worth #{transaction.points} points"
    when "Adjustment"
      "#{transaction.admin.name} adjusted #{transaction.user.name}'s account by #{transaction.points} points"
    end
  end
end
