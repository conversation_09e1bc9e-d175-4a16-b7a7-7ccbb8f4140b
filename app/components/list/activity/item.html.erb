<li class="group">
  <div class="relative pb-8">
    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 group-last:hidden" aria-hidden="true"></span>
    <div class="relative flex space-x-3">
      <div>
        <span class="flex items-center justify-center w-8 h-8 <%= icon_color %> rounded-full ring-8 ring-white">
          <%= icon(name: icon_name, class: "h-5 w-5 text-white fill-current") %>
        </span>
      </div>
      <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
        <div>
          <p class="max-w-xl text-sm text-gray-500 <%= "line-through" if declined %>"><%= message %></p>
        </div>
        <div class="flex items-center space-x-2 text-sm text-right text-gray-500 whitespace-nowrap">
          <%= local_time_ago(date) %>
          <%= render Modal::Component.new name: "Details" do |modal| %>
            <% modal.with_button title: "Details", size: "small", primary: false, click: "modalOpen = true" %>
            <%= render "#{type.downcase.pluralize}/details", item: transaction %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</li>
