<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
  <% if title.present? || subtitle.present? || export_enabled %>
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-start">
      <div>
        <% if title.present? %>
          <h3 class="text-lg font-semibold text-gray-900"><%= title %></h3>
        <% end %>
        <% if subtitle.present? %>
          <p class="text-sm text-gray-600 mt-1"><%= subtitle %></p>
        <% end %>
      </div>
      
      <% if export_enabled %>
        <div class="flex items-center space-x-2">
          <% if refresh_url.present? %>
            <button 
              type="button"
              onclick="refreshChart('<%= chart_container_id %>', '<%= refresh_url %>')"
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
            >
              <%= icon name: "refresh-cw", class: "h-3 w-3 mr-1" %>
              Refresh
            </button>
          <% end %>
          
          <div class="relative" x-data="{ open: false }">
            <button 
              @click="open = !open"
              type="button"
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
            >
              <%= icon name: "download", class: "h-3 w-3 mr-1" %>
              Export
              <%= icon name: "chevron-down", class: "h-3 w-3 ml-1" %>
            </button>
            
            <div 
              x-show="open" 
              @click.away="open = false"
              x-transition:enter="transition ease-out duration-100"
              x-transition:enter-start="transform opacity-0 scale-95"
              x-transition:enter-end="transform opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-75"
              x-transition:leave-start="transform opacity-100 scale-100"
              x-transition:leave-end="transform opacity-0 scale-95"
              class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
            >
              <div class="py-1">
                <% export_options.each do |option| %>
                  <button 
                    onclick="exportChart('<%= chart_container_id %>', '<%= option[:format] %>')"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <%= option[:label] %>
                  </button>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% end %>

  <div class="p-6">
    <% if loading %>
      <div class="flex items-center justify-center" style="height: <%= height %>">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-zeiss-500"></div>
        <span class="ml-2 text-gray-600">Loading chart data...</span>
      </div>
    <% elsif !has_data? %>
      <div class="flex items-center justify-center text-gray-500" style="height: <%= height %>">
        <%= icon name: "bar-chart-3", class: "h-8 w-8 mr-2" %>
        <span><%= empty_message %></span>
      </div>
    <% else %>
      <div 
        id="<%= chart_container_id %>" 
        style="height: <%= height %>; width: <%= width %>"
        <% if auto_refresh && refresh_url.present? %>
          data-auto-refresh="true"
          data-refresh-url="<%= refresh_url %>"
          data-refresh-interval="<%= refresh_interval %>"
        <% end %>
      >
        <%= content %>
      </div>
    <% end %>
  </div>
</div>
