class User::RoleBadge < ApplicationComponent
  option :user, model: User

  @@classes = %w[px-2 py-1 text-xs font-medium rounded-full]

  def role_color
    if user.super_admin?
      %w[text-purple-800 bg-purple-100]
    elsif user.admin?
      if user.admin_brand.present?
        if user.admin_region.present?
          %w[text-indigo-800 bg-indigo-100]
        else
          %w[text-blue-800 bg-blue-100]
        end
      else
        %w[text-yellow-800 bg-yellow-100]
      end
    else
      %w[text-green-800 bg-green-100]
    end
  end

  def call
    @classes = @@classes.dup
    tag.span class: @classes << role_color do
      [user.admin_region&.name, user.admin_brand&.name, user.role.titleize].compact.join(" ")
    end
  end
end
