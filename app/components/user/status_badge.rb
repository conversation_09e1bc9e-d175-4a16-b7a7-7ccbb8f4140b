class User::StatusBadge < ApplicationComponent
  option :user, model: User

  @@classes = %w[px-2 py-1 text-xs font-medium rounded-full]

  def status_color
    if user.inactive_status?
      %w[text-yellow-800 bg-yellow-100]
    elsif user.new_status?
      %w[text-orange-800 bg-orange-100]
    elsif user.active_status?
      %w[text-green-800 bg-green-100]
    end
  end

  def call
    @classes = @@classes.dup
    tag.span class: @classes << status_color do
      user.status.titleize
    end
  end
end
