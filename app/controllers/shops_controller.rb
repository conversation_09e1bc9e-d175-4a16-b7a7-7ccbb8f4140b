class ShopsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def show
    if Current.cart.nil?
      Current.cart = Cart.create
      session[:cart_id] = Current.cart.id
    end

    products = Product.pagy_search(params[:q], filter: filters(params, Product::FILTER_FIELDS, {country_name: Current.country.name}))
    products[0] = products[0].includes(:prices).joins(:prices).where(prices: {country: Current.country}).with_attached_image
    @pagy, @products = pagy_meilisearch(products, items: 20)
  end
end
