class RealTimeAnalyticsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :initialize_analytics_service

  def dashboard
    @live_metrics = @analytics_service.live_metrics
    @sales_stream = @analytics_service.sales_stream_data
    @user_activity = @analytics_service.user_activity_stream
    @system_health = @analytics_service.system_health_metrics

    respond_to do |format|
      format.html
      format.json { render json: dashboard_data }
    end
  end

  def live_metrics
    respond_to do |format|
      format.json { render json: @analytics_service.live_metrics }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "live-metrics",
          partial: "real_time_analytics/live_metrics",
          locals: { metrics: @analytics_service.live_metrics }
        )
      end
    end
  end

  def sales_stream
    respond_to do |format|
      format.json { render json: @analytics_service.sales_stream_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "sales-stream",
          partial: "real_time_analytics/sales_stream",
          locals: { data: @analytics_service.sales_stream_data }
        )
      end
    end
  end

  def user_activity
    return head :forbidden unless current_user.admin? || current_user.super_admin?

    respond_to do |format|
      format.json { render json: @analytics_service.user_activity_stream }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "user-activity",
          partial: "real_time_analytics/user_activity",
          locals: { activity: @analytics_service.user_activity_stream }
        )
      end
    end
  end

  def system_health
    return head :forbidden unless current_user.super_admin?

    respond_to do |format|
      format.json { render json: @analytics_service.system_health_metrics }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "system-health",
          partial: "real_time_analytics/system_health",
          locals: { health: @analytics_service.system_health_metrics }
        )
      end
    end
  end

  def refresh_all
    # Trigger refresh of all components
    @analytics_service.broadcast_updates

    respond_to do |format|
      format.json { render json: { status: "refreshed", timestamp: Time.current } }
      format.turbo_stream do
        render turbo_stream: [
          turbo_stream.replace(
            "live-metrics",
            partial: "real_time_analytics/live_metrics",
            locals: { metrics: @analytics_service.live_metrics }
          ),
          turbo_stream.replace(
            "sales-stream",
            partial: "real_time_analytics/sales_stream",
            locals: { data: @analytics_service.sales_stream_data }
          )
        ]
      end
    end
  end

  private

  def initialize_analytics_service
    @analytics_service = RealTimeAnalyticsService.new(current_user)
  end

  def dashboard_data
    {
      live_metrics: @live_metrics,
      sales_stream: @sales_stream,
      user_activity: @user_activity,
      system_health: @system_health,
      timestamp: Time.current.iso8601,
      user_permissions: {
        can_view_users: current_user.admin? || current_user.super_admin?,
        can_view_system: current_user.super_admin?
      }
    }
  end
end
