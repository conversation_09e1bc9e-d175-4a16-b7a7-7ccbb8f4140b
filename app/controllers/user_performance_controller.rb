class UserPerformanceController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_target_user
  before_action :check_permissions
  before_action :set_date_range

  def show
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @performance_data = @performance_service.comprehensive_performance_report

    respond_to do |format|
      format.html
      format.json { render json: @performance_data }
    end
  end

  def overview
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @overview_data = @performance_service.performance_overview

    respond_to do |format|
      format.json { render json: @overview_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "performance-overview",
          partial: "user_performance/overview",
          locals: { data: @overview_data, user: @target_user }
        )
      end
    end
  end

  def activity_tracking
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @activity_data = @performance_service.activity_tracking_data

    respond_to do |format|
      format.json { render json: @activity_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "activity-tracking",
          partial: "user_performance/activity_tracking",
          locals: { data: @activity_data, user: @target_user }
        )
      end
    end
  end

  def goal_tracking
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @goals_data = @performance_service.goal_tracking_data

    respond_to do |format|
      format.json { render json: @goals_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "goal-tracking",
          partial: "user_performance/goal_tracking",
          locals: { data: @goals_data, user: @target_user }
        )
      end
    end
  end

  def performance_trends
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @trends_data = @performance_service.performance_trends_data

    respond_to do |format|
      format.json { render json: @trends_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "performance-trends",
          partial: "user_performance/performance_trends",
          locals: { data: @trends_data, user: @target_user }
        )
      end
    end
  end

  def comparative_analysis
    return head :forbidden unless current_user.admin? || current_user.super_admin?

    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @comparison_data = @performance_service.comparative_analysis_data

    respond_to do |format|
      format.json { render json: @comparison_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "comparative-analysis",
          partial: "user_performance/comparative_analysis",
          locals: { data: @comparison_data, user: @target_user }
        )
      end
    end
  end

  def achievements
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @achievements_data = @performance_service.achievements_data

    respond_to do |format|
      format.json { render json: @achievements_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "achievements",
          partial: "user_performance/achievements",
          locals: { data: @achievements_data, user: @target_user }
        )
      end
    end
  end

  def recommendations
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @recommendations_data = @performance_service.generate_recommendations

    respond_to do |format|
      format.json { render json: @recommendations_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "recommendations",
          partial: "user_performance/recommendations",
          locals: { recommendations: @recommendations_data, user: @target_user }
        )
      end
    end
  end

  def detailed_metrics
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @detailed_data = @performance_service.detailed_metrics_data

    respond_to do |format|
      format.json { render json: @detailed_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "detailed-metrics",
          partial: "user_performance/detailed_metrics",
          locals: { data: @detailed_data, user: @target_user }
        )
      end
    end
  end

  def export
    @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
    @performance_data = @performance_service.comprehensive_performance_report

    respond_to do |format|
      format.xlsx do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        filename = "user-performance-#{@target_user.id}-#{timestamp}.xlsx"
        response.headers["Content-Disposition"] = "attachment; filename=#{filename}"
      end
      format.csv do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        filename = "user-performance-#{@target_user.id}-#{timestamp}.csv"
        response.headers["Content-Disposition"] = "attachment; filename=#{filename}"
      end
      format.pdf do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        filename = "user-performance-#{@target_user.id}-#{timestamp}.pdf"
        response.headers["Content-Disposition"] = "attachment; filename=#{filename}"
      end
    end
  end

  def update_goals
    return head :forbidden unless can_update_goals?

    goal_params = params.require(:goals).permit(:monthly_sales, :monthly_points, :quarterly_sales)
    
    # In a real implementation, this would update user goals in the database
    # For now, we'll store in session or cache
    Rails.cache.write("user_goals_#{@target_user.id}", goal_params, expires_in: 1.year)

    respond_to do |format|
      format.json { render json: { status: 'success', message: 'Goals updated successfully' } }
      format.turbo_stream do
        # Refresh the goal tracking section
        @performance_service = UserPerformanceService.new(current_user, @target_user, @date_range)
        @goals_data = @performance_service.goal_tracking_data
        
        render turbo_stream: turbo_stream.replace(
          "goal-tracking",
          partial: "user_performance/goal_tracking",
          locals: { data: @goals_data, user: @target_user }
        )
      end
    end
  end

  private

  def set_target_user
    if params[:user_id].present?
      @target_user = User.find(params[:user_id])
    else
      @target_user = current_user
    end
  end

  def check_permissions
    # Users can view their own performance
    return if @target_user == current_user
    
    # Admins can view any user's performance
    return if current_user.admin? || current_user.super_admin?
    
    # Region admins can view users in their region
    return if current_user.region_admin? && @target_user.region == current_user.region
    
    # Brand admins can view users in their brand
    return if current_user.brand_admin? && @target_user.store&.brand == current_user.admin_brand
    
    # Otherwise, deny access
    head :forbidden
  end

  def can_update_goals?
    # Users can update their own goals
    return true if @target_user == current_user
    
    # Admins can update any user's goals
    return true if current_user.admin? || current_user.super_admin?
    
    # Region/Brand admins can update goals for their users
    return true if current_user.region_admin? && @target_user.region == current_user.region
    return true if current_user.brand_admin? && @target_user.store&.brand == current_user.admin_brand
    
    false
  end

  def set_date_range
    @date_range = params[:date_range] || "30d"
  end
end
