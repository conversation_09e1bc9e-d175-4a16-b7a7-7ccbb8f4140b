class StaticController < ApplicationController
  layout "static"

  def not_found
    render status: 404, layout: false
  end

  def internal_server_error
    render status: 500, layout: false
  end

  private

  def resource_name
    :user
  end
  helper_method :resource_name

  def resource
    @resource ||= User.new
  end
  helper_method :resource

  def devise_mapping
    @devise_mapping ||= Devise.mappings[:user]
  end
  helper_method :devise_mapping

  def resource_class
    User
  end
  helper_method :resource_class
end
