class Users::OrdersController < ApplicationController
  def new
    user = User.friendly.find(params[:user_id])
    if user.admin_cart.blank?
      user.admin_cart = Cart.new(user: user)
    end

    @order = Order.new
    @order.user = user

    @q = Product.includes(:category).pagy_search(params[:q], filter: filters(params, Product::FILTER_FIELDS))
    @pagy, @products = pagy_meilisearch(@q, items: params[:limit].to_i)

    @categories = Category.all
  end

  def create
    order = Order.new(order_params)
    user = User.friendly.find(params[:user_id])

    if user.admin_cart.cart_products.blank?
      redirect_to orders_path, notice: "No products ordered." and return
    end

    order.points = user.admin_cart.total_points
    order.user = user
    order.status = "approved"
    order.admin = current_user
    order.brand = user.store.brand
    order.region = user.region
    order.approved_at = Time.now

    order.save!

    activity = user.wallet.activities.create
    activity.transactable = order
    activity.kind = "debit"
    activity.status = "approved"
    activity.save!

    user.admin_cart.cart_products.each do |cart_item|
      order_item = order.order_products.build
      order_item.product = cart_item.product
      order_item.quantity = cart_item.quantity
      order_item.points = cart_item.points
      order_item.save!
    end

    user.admin_cart.cart_products.destroy_all

    user.wallet.current_balance -= order.points
    user.wallet.available_balance -= order.points
    user.wallet.save

    redirect_to user_points_path(user), notice: "Order created!"
  end
end
