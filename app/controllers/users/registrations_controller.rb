class Users::RegistrationsController < Devise::RegistrationsController
  layout "static", only: [:new, :create]

  before_action :load_resources, only: [:new, :create]

  def new
    build_resource

    resource.build_address

    respond_with resource
  end

  private

  def load_resources
    @stores = Store.includes(address: [:state, :country]).search(params[:store_name], sort: ["name:asc"])
    @states = State.where(country: Current.country).order(:name)
  end
end
