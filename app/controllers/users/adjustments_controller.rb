class Users::AdjustmentsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def create
    @adjustment = Adjustment.new(adjustment_params.with_defaults(admin: current_user))
    @user = User.friendly.find(params[:user_id])

    @adjustment.user = @user

    if @adjustment.save
      respond_to do |format|
        format.html { redirect_to user_path(@user), notice: "Adjustment was successfully created." }
        format.turbo_stream do
          current = turbo_stream.update("current_balance", @user.wallet.current_balance)
          available = turbo_stream.update("available_balance", @user.wallet.available_points)
          render turbo_stream: [current, available]
        end
      end
    else
      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: turbo_stream.update("adjustment_errors", partial: "shared/error_messages", locals: {resource: @adjustment})
        end
      end
    end
  end

  private

  def adjustment_params
    params.require(:adjustment).permit(:notes, :kind, :points)
  end
end
