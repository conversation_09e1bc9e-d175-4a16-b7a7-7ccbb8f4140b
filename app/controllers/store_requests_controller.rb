class StoreRequestsController < ApplicationController
  before_action :authenticate_user_with_sign_up!, except: [:new, :create, :pending]
  before_action :set_store_request, only: [:show, :edit, :update, :destroy]

  def index
    @pagy, @requests = pagy(StoreRequest.order(:created_at).where(status: "pending"))

    @requests.load
  end

  def show
    @stores = Store.includes(:users, :brand).search(@request.store_name)
  end

  def new
    @request = StoreRequest.new
    render layout: "account"
  end

  def create
    @request = StoreRequest.new(store_request_params)

    if @request.save
      redirect_to root_path, notice: "Thank you for letting us know. The managers will review your request and follow up with the next steps."
    else
      render :new, status: :unprocessable_entity, layout: "account"
    end
  end

  def update
    @request.assign_attributes(store_request_params)

    if @request.save
      redirect_to @request, notice: "Store request was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @request.destroy
      redirect_to store_requests_path, notice: "Store request was successfully destroyed."
    end
  end

  def pending
    @pending_requests = StoreRequest.where(status: :pending).size

    respond_to do |format|
      format.turbo_stream {
        render turbo_stream: turbo_stream.replace(:store_request_badge, partial: "pending", locals: {pending_requests: @pending_requests})
      }
    end
  end

  private

  def set_store_request
    @request = StoreRequest.find(params[:id])
    authorize! @request
  end

  def store_request_params
    params.require(:store_request).permit(:name, :email, :store_name, :street, :city, :state_id, :zip, :manager_name, :status, :brand_id)
  end
end
