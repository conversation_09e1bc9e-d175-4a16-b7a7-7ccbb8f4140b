require "csv"

class UsersController < ApplicationController
  include ActionView::RecordIdentifier

  before_action :authenticate_user_with_sign_up!
  before_action :set_user, except: [:index, :count, :stats, :import, :stop_impersonating]
  before_action :set_limit, only: :index

  def index
    users = User.includes(:wallet, :store).pagy_search(params[:q], filter: authorized_scope(filters(params, User::FILTER_FIELDS, {country_name: Current.country.name}), type: :index), sort: ["#{params[:sort]}:asc", "last_name:asc"])
    @pagy, @users = pagy_meilisearch(users, items: params[:limit].to_i)
    @grouped_users = @users.group_by { |u| u.public_send(User::SORT_TO_GROUP[params[:sort].to_sym]) }

    @import = Users::Import.new

    respond_to do |format|
      format.xlsx {
        @users = User.where.not(role: "super_admin").includes(:store, :wallet, address: :state)
        @users.load
        response.headers["Content-Disposition"] = 'attachment; filename="users.xlsx"'
      }
      format.html
      format.csv do
        @users = User.includes(:state)

        response.headers["Content-Type"] = "text/csv"
        response.headers["Content-Disposition"] = "attachment; filename=users.csv"
      end
    end
  end

  def update
    if @user.update(user_params)
      redirect_to @user, notice: "User was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def show
    @adjustment = Adjustment.new
  end

  def import
    @import = Users::Import.new(user_import_params)

    if @import.save
      redirect_to users_path, notice: "#{@import.imported_count} users imported successfully."
    else
      render :index, notice: "Import failed.", status: :unprocessable_entity
    end
  end

  def impersonate
    redirect_to root_path unless current_user.super_admin? || current_user.admin?
    impersonate_user(@user)
    redirect_back_or_to root_path
  end

  def stop_impersonating
    stop_impersonating_user
    redirect_to root_path
  end

  def count
    @users_by_region = User.joins(address: [state: :region]).group(region_or_state_group(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @users_by_region }
    end
  end

  def stats
    @total_users = Brand.joins(users: :address).where(brand_where(current_user.admin_brand)).where("addresses.country_id = ?", Current.country.id).size
    @users_in_the_last_month = Brand.joins(users: :address).where(brand_where(current_user.admin_brand)).where("users.created_at >= ?", 1.month.ago).where("addresses.country_id = ?", Current.country.id).size

    render layout: false
  end

  def ledger
    @transactions = [*@user.sales.where(status: "approved"), *@user.orders.where(status: ["approved", "processed"]), *@user.adjustments]

    respond_to do |format|
      format.xlsx {
        response.headers["Content-Disposition"] = "attachment; filename=#{@user.name.downcase.underscore}-#{Time.zone.now.strftime("%Y%m%d%H%M")}.xlsx"
      }
    end
  end

  def audit
    transactions = [*@user.sales, *@user.orders, *@user.adjustments]

    point_account = @user.wallet

    point_account.activities.destroy_all
    point_account.current_balance = 0

    transactions.sort_by(&:created_at).each do |transaction|
      status = ((transaction.is_a?(Order) && transaction.processed?) || transaction.is_a?(Adjustment)) ? "approved" : transaction.status
      kind = if transaction.is_a?(Adjustment)
        transaction.kind
      else
        (transaction.is_a?(Sale) ? "credit" : "debit")
      end

      point_account.activities.create(kind: kind, status: status, transactable: transaction)

      if status == "approved"
        case kind
        when "debit"
          point_account.debit!(transaction.points)
        when "credit"
          point_account.credit!(transaction.points)
        end
      end
    end

    redirect_to @user, notice: "Audit complete!"
  end

  private

  def set_user
    @user = User.friendly.includes(store: [address: :state], wallet: :activities, address: [:state]).find(params[:id])
    authorize! @user
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :approved, :phone_number, :store_id, :role, :email, :admin_brand_id, :admin_region_id, :status, :password, :password_confirmation, address_attributes: [:line1, :line2, :city, :state_id, :zip_code, :id])
  end

  def user_import_params
    params.require(:users_import).permit(:file)
  end

  def set_limit
    params[:sort] ||= "status"
    params[:limit] ||= 20
  end
end
