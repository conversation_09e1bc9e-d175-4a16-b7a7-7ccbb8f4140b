# {
#  "errors": {
#   { "row": 1, "col": 1, "message": "Required" }
#  },
#  "updates",
#  "added",
# }
class Products::UpdateController < ApplicationController
  def validate
    # data.each do |item|
    #  @product = Product.find(item[:id])
    #  if @product.valid?
    #  else
    #   return_data = {}
    #   @product.errors.attribute_names.each_with_index do |attribute, index|
    #     case attribute
    #     when :name
    #       return_data[:errors] = {row: index + 1, col: 1}
  end
end
