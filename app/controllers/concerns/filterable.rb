module Filterable
  extend ActiveSupport::Concern

  included do
    helper_method :filters
  end

  def filters(params, filter_list, defaults = {})
    params = defaults.with_indifferent_access.merge(params.permit(filter_list))

    filter = []
    (params.keys & filter_list).each do |key|
      filter << case params[key]
      when /^\d+ to \d+$/
        "#{key} #{params[key].upcase}"
      else
        "#{key}='#{params[key]}'"
      end
    end
    filter
  end
end
