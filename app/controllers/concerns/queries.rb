module Queries
  extend ActiveSupport::Concern

  included do
    helper_method :brand_where
    helper_method :region_where
  end

  private

  def brand_where(brand)
    "brands.id = #{brand.id}" unless brand.blank?
  end

  def region_where(region)
    "regions.id = #{region.id}" unless region.blank?
  end

  def region_or_state_group(region)
    region.present? ? "states.name" : "regions.name"
  end
end
