# Add rorvswild
class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception

  around_action :prosopite, if: -> { Rails.env.local? }

  include Pagy::Backend
  include PagyCalendar
  include Queries
  include Filterable

  impersonates :user, method: :current_user, with: ->(id) { User.friendly.find(id) }

  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_paper_trail_whodunnit
  before_action :set_time_zone, if: :user_signed_in?
  before_action :set_cart, if: -> { session[:cart_id].present? }
  before_action :set_country

  protected

  def configure_permitted_parameters
    extra_keys = [:first_name, :last_name, :phone_number, :store_id, :email, :time_zone, address_attributes: [:line1, :line2, :city, :state_id, :zip_code, :country_id, :id]]
    update_keys = extra_keys + [:role, :avatar, :approved]
    devise_parameter_sanitizer.permit(:sign_up, keys: extra_keys)
    devise_parameter_sanitizer.permit(:account_update, keys: update_keys)
  end

  def authenticate_user_with_sign_up!
    unless user_signed_in?
      store_location_for(:user, request.fullpath)
      redirect_to new_user_registration_path, alert: "You must have an active account to access this page."
    end
  end

  rescue_from ActionPolicy::Unauthorized, with: :user_not_authorized

  def user_for_paper_trail
    user_signed_in? ? true_user.id : "Guest"
  end

  private

  def set_time_zone
    Time.zone = current_user.time_zone
  end

  def user_not_authorized
    flash[:alert] = "You do not have the appropriate permissions to view this page."
    redirect_back_or_to root_path, allow_other_host: false
  end

  def prosopite
    Prosopite.scan
    yield
    Prosopite.finish
  end

  def set_cart
    cart = Cart.find_by(id: session[:cart_id])
    if cart.present?
      Current.cart = cart
    end
  end

  def set_country
    Current.country = (user_signed_in? && current_user.address) ? current_user.address.country : Country.find_by(abbreviation: request.location.country_code.presence || "US")
  end
end
