class CategoriesController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_category, only: [:show, :edit, :update, :destroy]

  def index
    @pagy, @categories = pagy(authorized_scope(Category.all).includes(:brand).order(:name))

    @categories.load
  end

  def show
    @pagy, @products = pagy(Product.where(category_id: params[:id]).with_attached_image)
  end

  def new
    @category = Category.new
    authorize! @category
  end

  def create
    @category = Category.new(category_params)

    authorize! @category

    if @category.save
      redirect_to @category, notice: "Category was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @category.update(category_params)
      redirect_to @category, notice: "Category was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @category.destroy
    redirect_to categories_url, notice: "Category was successfully destroyed."
  end

  private

  def set_category
    @category = Category.find(params[:id])
    authorize! @category
  end

  def category_params
    params.require(:category).permit(:name, :description, :brand_id)
  end

  def category_import_params
    params.require(:categories_import).permit(:file, :brand_id)
  end
end
