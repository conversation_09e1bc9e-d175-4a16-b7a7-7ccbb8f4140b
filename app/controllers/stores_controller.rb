require "csv"

class StoresController < ApplicationController
  before_action :authenticate_user_with_sign_up!, except: [:new, :create]
  before_action :set_store, only: [:show, :edit, :update, :destroy]
  before_action :set_limit, only: :index

  layout :set_layout, only: [:new, :create]

  def index
    stores = Store.includes(:brand, address: [state: [:region]]).pagy_search(params[:q], filter: authorized_scope(filters(params, Store::FILTER_FIELDS, {country_name: Current.country.name}), type: :index), sort: ["#{params[:sort]}:asc", "name:asc"])
    @pagy, @stores = pagy_meilisearch(stores, items: params[:limit].to_i)
    @grouped_stores = @stores.group_by { |u| u.public_send(Store::SORT_TO_GROUP[params[:sort].to_sym]) }

    @import = Stores::Import.new

    respond_to do |format|
      format.html
      format.xlsx do
        @stores = Store.includes(address: [:state])
        response.headers["Content-Disposition"] = 'attachment; filename="stores.xlsx"'
      end
      format.csv do
        @stores = Store.includes(:brand, address: [:state])

        response.headers["Content-Type"] = "text/csv"
        response.headers["Content-Disposition"] = "attachment; filename=stores.csv"
      end
    end
  end

  def new
    @store = Store.new
    @store.build_address

    request.variant = determine_variant
  end

  def create
    @store = Store.new(store_params)

    if @store.save
      if user_signed_in?
        redirect_to @store, notice: "Store was successfully created."
      else
        redirect_to root_path
      end
    else
      request.variant = determine_variant
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @store.update(store_params)
      redirect_to @store, notice: "Store was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @store.destroy
      redirect_to stores_path, notice: "Store was successfully destroyed.", status: :see_other
    end
  end

  def import
    if params[:stores_import][:file].present?
      @import = Stores::Import.new(store_import_params)

      if @import.save
        redirect_to stores_path, notice: "#{@import.imported_count} stores imported successfully."
      else
        render :index, notice: "Import failed.", status: :unprocessable_entity
      end
    else
      redirect_to stores_path, notice: "Please upload csv file.", status: :unprocessable_entity
    end
  end

  def count
    @stores_by_region = Store.joins(state: :region).group(region_or_state_group(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @stores_by_region }
    end
  end

  def stats
    @total_stores = Brand.joins(stores: :address).where(brand_where(current_user.admin_brand)).where("addresses.country_id = ?", Current.country.id).size
    @stores_in_the_last_month = Brand.joins(stores: :address).where(brand_where(current_user.admin_brand)).where("stores.created_at >=  ?", 1.month.ago).where("addresses.country_id = ?", Current.country.id).size

    render layout: false
  end

  private

  def set_store
    @store = Store.includes(promotions: :products, address: [:state], users: [address: [:state]]).find(params[:id])
    authorize! @store
  end

  def store_params
    params.require(:store).permit(:name, :phone_number, :verified, :account_number, :brand_id, :status, address_attributes: [:id, :line1, :line2, :city, :state_id, :zip_code, :country_id])
  end

  def store_import_params
    params.require(:stores_import).permit(:file, :brand_id)
  end

  def set_limit
    params[:sort] ||= "brand_name"
    params[:limit] ||= 20
  end

  def set_layout
    user_signed_in? ? "application" : "static"
  end

  def determine_variant
    variant = nil
    variant = :request if !user_signed_in?
    variant
  end
end
