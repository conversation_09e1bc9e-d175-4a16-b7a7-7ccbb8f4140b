class FinancialReportsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :check_financial_access
  before_action :set_date_range

  def index
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @financial_data = @financial_service.comprehensive_financial_report

    respond_to do |format|
      format.html
      format.json { render json: @financial_data }
    end
  end

  def revenue_tracking
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @revenue_data = @financial_service.revenue_tracking_data

    respond_to do |format|
      format.json { render json: @revenue_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "revenue-tracking",
          partial: "financial_reports/revenue_tracking",
          locals: { data: @revenue_data }
        )
      end
    end
  end

  def points_economics
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @points_data = @financial_service.points_economics_data

    respond_to do |format|
      format.json { render json: @points_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "points-economics",
          partial: "financial_reports/points_economics",
          locals: { data: @points_data }
        )
      end
    end
  end

  def cost_analysis
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @cost_data = @financial_service.cost_analysis_data

    respond_to do |format|
      format.json { render json: @cost_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "cost-analysis",
          partial: "financial_reports/cost_analysis",
          locals: { data: @cost_data }
        )
      end
    end
  end

  def profitability_analysis
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @profitability_data = @financial_service.profitability_analysis_data

    respond_to do |format|
      format.json { render json: @profitability_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "profitability-analysis",
          partial: "financial_reports/profitability_analysis",
          locals: { data: @profitability_data }
        )
      end
    end
  end

  def budget_tracking
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @budget_data = @financial_service.budget_tracking_data

    respond_to do |format|
      format.json { render json: @budget_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "budget-tracking",
          partial: "financial_reports/budget_tracking",
          locals: { data: @budget_data }
        )
      end
    end
  end

  def roi_analysis
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @roi_data = @financial_service.roi_analysis_data

    respond_to do |format|
      format.json { render json: @roi_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "roi-analysis",
          partial: "financial_reports/roi_analysis",
          locals: { data: @roi_data }
        )
      end
    end
  end

  def financial_forecasting
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @forecast_data = @financial_service.financial_forecasting_data

    respond_to do |format|
      format.json { render json: @forecast_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "financial-forecasting",
          partial: "financial_reports/financial_forecasting",
          locals: { data: @forecast_data }
        )
      end
    end
  end

  def key_metrics
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @metrics_data = @financial_service.key_financial_metrics

    respond_to do |format|
      format.json { render json: @metrics_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "key-metrics",
          partial: "financial_reports/key_metrics",
          locals: { data: @metrics_data }
        )
      end
    end
  end

  def insights
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @insights_data = @financial_service.generate_financial_insights

    respond_to do |format|
      format.json { render json: @insights_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "financial-insights",
          partial: "financial_reports/insights",
          locals: { insights: @insights_data }
        )
      end
    end
  end

  def export
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @financial_data = @financial_service.comprehensive_financial_report

    respond_to do |format|
      format.xlsx do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=financial-report-#{timestamp}.xlsx"
      end
      format.csv do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=financial-report-#{timestamp}.csv"
      end
      format.pdf do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=financial-report-#{timestamp}.pdf"
      end
    end
  end

  def dashboard
    @financial_service = FinancialAnalyticsService.new(current_user, @date_range)
    @dashboard_data = {
      key_metrics: @financial_service.key_financial_metrics,
      revenue_summary: @financial_service.revenue_tracking_data,
      cost_summary: @financial_service.cost_analysis_data,
      points_summary: @financial_service.points_economics_data,
      insights: @financial_service.generate_financial_insights
    }

    respond_to do |format|
      format.html { render :dashboard }
      format.json { render json: @dashboard_data }
    end
  end

  private

  def check_financial_access
    # Only allow access to users with financial permissions
    unless current_user.super_admin? || current_user.admin? || has_financial_access?
      redirect_to reports_path, alert: "You don't have permission to access financial reports."
    end
  end

  def has_financial_access?
    # Check if user has specific financial access permissions
    # This could be a role-based check or a specific permission
    current_user.role.in?(['finance_admin', 'cfo']) || 
    current_user.permissions&.include?('view_financial_reports')
  end

  def set_date_range
    @date_range = params[:date_range] || "30d"
  end
end
