class Account::NotificationsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def update
    if current_user.update(notification_params)
      redirect_to root_path, notice: "Notification settings were successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def notification_params
    params.require(:user).permit(:mail_user_new, :mail_order_new, :mail_order_updated, :mail_store_request_new, :mail_sale_new, :mail_sale_updated, :mail_promotion, :sms_promotion)
  end
end
