class Shops::ProductsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def show
    @product = Product.find(params[:id])
    @promotions = case current_user.role
    when "admin" || "super_admin"
      @product.promotions
    when "regular_user"
      Promotion.joins(:products).left_joins(:stores, store_chains: :stores)
        .where(stores: current_user.store)
        .or(Promotion.where(store_chains: {stores: current_user.store}))
        .where("date(?) between starts_at and ends_at", Time.zone.now.utc)
        .where(products: @product).distinct
    end
  end
end
