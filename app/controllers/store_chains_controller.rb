class StoreChainsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_store_chain, only: [:show, :edit, :update, :destroy]

  def index
    @chains = StoreChain.all
  end

  def new
    @store_chain = StoreChain.new
    authorize! @store_chain
  end

  def create
    @store_chain = StoreChain.new(store_chain_params)
    authorize! @store_chain

    if @store_chain.save
      redirect_to store_chains_path, success: "Store Chain created successfully."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @store_chain.assign_attributes(store_chain_params)

    if @store_chain.save
      redirect_to @store_chain, success: "Store Chain updated successfully."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @store_chain.destroy
      redirect_to store_chains_path, success: "Store Chain deleted successfully.", status: 303
    end
  end

  private

  def set_store_chain
    @store_chain = StoreChain.find(params[:id])
    authorize! @store_chain
  end

  def store_chain_params
    params.require(:store_chain).permit(:name, store_ids: [])
  end
end
