# Product controller
require "csv"

class ProductsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_product, only: [:edit, :update, :destroy, :sale]
  before_action :set_limit, only: :index

  def index
    products = Product.pagy_search(params[:q], filter: authorized_scope(filters(params, Product::FILTER_FIELDS, {country_name: Current.country.name}), type: :index), sort: ["#{params[:sort]}:asc", "name:asc"])
    products[0] = products[0].includes(category: :brand, prices: [:country]).with_attached_image
    @pagy, @products = pagy_meilisearch(products, items: params[:limit].to_i)
    @grouped_products = @products.group_by { |p| p.public_send(Product::SORT_TO_GROUP[params[:sort].to_sym]) }

    @import = Products::Import.new

    respond_to do |format|
      format.xlsx {
        @products = Product.all

        response.headers["Content-Disposition"] = 'attachment; filename="all_products.xlsx"'
      }
      format.json {
        render json: @products.map { |p| {id: p.id, name: p.name_with_sku} }
      }
      format.html
      format.csv do
        @products = Product.all

        response.headers["Content-Type"] = "text/csv"
        response.headers["Content-Disposition"] = "attachment; filename=products.csv"
      end
    end
  end

  def new
    @product = Product.new
  end

  def show
    @product = Product.includes(prices: [:country]).find_by(id: params[:id]) || Product.find_by!(upc: params[:id])
    authorize! @product

    respond_to do |format|
      format.html
      format.json { render json: @product }
    end
  end

  def sale
    store_promotions = Promotion.joins(:products, :stores).left_joins(store_chains: :stores)
      .where(products: @product)
      .where(stores: current_user.store)
      .where("? between starts_at and ends_at", Time.zone.now)
    chain_promotions = Promotion.joins(:products, :stores).left_joins(store_chains: :stores)
      .where(products: @product)
      .where(store_chains: {stores: current_user.store})
      .where("? between starts_at and ends_at", Time.zone.now)
    @promotion = store_promotions.merge(chain_promotions).first
    respond_to do |format|
      format.turbo_stream { render turbo_stream: turbo_stream.replace("sale_product", partial: "sale_product", locals: {product: @product, promotion: @promotion}) }
    end
  end

  def create
    @product = Product.new(product_params)

    if @product.save
      redirect_to @product, notice: "Product was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @product.update(product_params)
      redirect_to @product, notice: "Product was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @product.destroy
    redirect_to products_url, notice: "Product was successfully destroyed."
  end

  def import
    @import = Products::Import.new(products_import_params)

    if @import.save
      redirect_to products_path, notice: "#{@import.imported_count} products imported successfully."
    else
      render :index, notice: "Import failed.", status: :unprocessable_entity
    end
  end

  def search
    @products = Product.ms_search(params[:q])
    @user = User.friendly.find(params[:user_id])

    respond_to do |format|
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace("search_results", partial: "search_results", locals: {products: @products})
      end
    end
  end

  private

  def set_product
    @product = Product.includes(:promotions).find(params[:id])
    authorize! @product
  end

  def product_params
    params.require(:product).permit(:name, :description, :msrp, :points_earned, :points_needed, :sku, :active, :category_id, :image, :upc, :gift_card, :gift_card_value)
  end

  def products_import_params
    params.require(:products_import).permit(:file)
  end

  def set_limit
    params[:sort] ||= "category_brand_name"
    params[:limit] ||= 20
  end
end
