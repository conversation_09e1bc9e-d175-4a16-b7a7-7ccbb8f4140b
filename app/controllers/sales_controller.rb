class SalesController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_sale, only: [:show, :edit, :update, :destroy]
  before_action :set_limit, only: :index

  def index
    sales = Sale.includes(:user, :product).pagy_search(params[:q], filter: authorized_scope(filters(params, Sale::FILTER_FIELDS, {country_name: Current.country.name}), type: :index), sort: ["created_at:desc"])
    @pagy, @sales = pagy_meilisearch(sales, limit: params[:limit].to_i)

    respond_to do |format|
      format.xlsx {
        @sales = Sale.order("created_at DESC")
        response.headers["Content-Disposition"] = 'attachment; filename="sales.xlsx"'
      }
      format.html
    end
  end

  def new
    @sale = Sale.new
    authorize! @sale

    @products = Product.joins(:prices).where(prices: {country: Current.country}).with_attached_image.to_json(only: [:id, :name, :sku], methods: [:image_select_path])
  end

  def create
    @sale = Sale.new(sale_params.with_defaults(user: current_user, brand: current_user.store.brand, region: current_user.store.region))
    authorize! @sale

    if @sale.save
      redirect_to @sale, notice: "Sale was successfully created."
    else
      @products = Product.joins(:prices).where(prices: {country: Current.country}).with_attached_image.to_json(only: [:id, :name, :sku], methods: [:image_select_path])
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @sale.update(sale_params.with_defaults(admin: current_user))
      redirect_to @sale, notice: "Sale was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @sale.destroy
      redirect_to sales_path, notice: "Sale was successfully destroyed."
    end
  end

  def pending_count
    @pending_sales = Sale.joins(:brand, :region, user: [address: :state]).where(status: :pending).group(region_or_state_group(current_user.admin_region)).where(brand_where(current_user.admin_brand)).where(region_where(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @pending_sales }
      format.turbo_stream { render turbo_stream: turbo_stream.replace(:sales_badge, partial: "pending", locals: {pending_sales: @pending_sales}) }
    end
  end

  def region_count
    @sales_by_region = Sale.joins(:brand, :region, user: [address: :state]).group(region_or_state_group(current_user.admin_region)).where(brand_where(current_user.admin_brand)).where(region_where(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @sales_by_region }
    end
  end

  def stats
    @total_sales = Brand.joins(stores: [:address, users: :sales]).where(brand_where(current_user.admin_brand)).where("addresses.country_id = ?", Current.country.id).size
    @sales_in_the_last_month = Brand.joins(stores: [:address, users: :sales]).where(brand_where(current_user.admin_brand)).where("sales.created_at >=  ?", 1.month.ago).where("addresses.country_id = ?", Current.country.id).size

    render layout: false
  end

  private

  def set_sale
    @sale = Sale.includes(:product, :user).find(params[:id])
    authorize! @sale
  end

  def sale_params
    params.require(:sale).permit(:serial_number, :sold_at, :notes, :product_id, :status, :origin, :promotion_id, :receipt, notations_attributes: [:user_id, :content])
  end

  def set_limit
    params[:limit] ||= 20
  end
end
