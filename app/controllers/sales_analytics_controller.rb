class SalesAnalyticsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_date_range

  def index
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @analytics_data = @analytics_service.comprehensive_analytics

    respond_to do |format|
      format.html
      format.json { render json: @analytics_data }
    end
  end

  def overview
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @overview_data = @analytics_service.sales_overview

    respond_to do |format|
      format.json { render json: @overview_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "sales-overview",
          partial: "sales_analytics/overview",
          locals: { data: @overview_data }
        )
      end
    end
  end

  def trends
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @trends_data = @analytics_service.sales_trends

    respond_to do |format|
      format.json { render json: @trends_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "sales-trends",
          partial: "sales_analytics/trends",
          locals: { data: @trends_data }
        )
      end
    end
  end

  def forecasting
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @forecast_data = @analytics_service.sales_forecasting

    respond_to do |format|
      format.json { render json: @forecast_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "sales-forecasting",
          partial: "sales_analytics/forecasting",
          locals: { data: @forecast_data }
        )
      end
    end
  end

  def performance_comparison
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @comparison_data = @analytics_service.performance_comparison

    respond_to do |format|
      format.json { render json: @comparison_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "performance-comparison",
          partial: "sales_analytics/performance_comparison",
          locals: { data: @comparison_data }
        )
      end
    end
  end

  def product_analysis
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @product_data = @analytics_service.product_performance_analysis

    respond_to do |format|
      format.json { render json: @product_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "product-analysis",
          partial: "sales_analytics/product_analysis",
          locals: { data: @product_data }
        )
      end
    end
  end

  def regional_analysis
    return head :forbidden unless current_user.admin? || current_user.super_admin?

    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @regional_data = @analytics_service.regional_performance_analysis

    respond_to do |format|
      format.json { render json: @regional_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "regional-analysis",
          partial: "sales_analytics/regional_analysis",
          locals: { data: @regional_data }
        )
      end
    end
  end

  def user_performance
    return head :forbidden unless current_user.admin? || current_user.super_admin?

    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @user_data = @analytics_service.user_performance_analysis

    respond_to do |format|
      format.json { render json: @user_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "user-performance",
          partial: "sales_analytics/user_performance",
          locals: { data: @user_data }
        )
      end
    end
  end

  def seasonal_analysis
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @seasonal_data = @analytics_service.seasonal_analysis

    respond_to do |format|
      format.json { render json: @seasonal_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "seasonal-analysis",
          partial: "sales_analytics/seasonal_analysis",
          locals: { data: @seasonal_data }
        )
      end
    end
  end

  def insights
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @insights_data = @analytics_service.generate_insights

    respond_to do |format|
      format.json { render json: @insights_data }
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "analytics-insights",
          partial: "sales_analytics/insights",
          locals: { insights: @insights_data }
        )
      end
    end
  end

  def export
    @analytics_service = SalesAnalyticsService.new(current_user, @date_range)
    @analytics_data = @analytics_service.comprehensive_analytics

    respond_to do |format|
      format.xlsx do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=sales-analytics-#{timestamp}.xlsx"
      end
      format.csv do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=sales-analytics-#{timestamp}.csv"
      end
      format.pdf do
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename=sales-analytics-#{timestamp}.pdf"
      end
    end
  end

  private

  def set_date_range
    @date_range = params[:date_range] || "30d"
    @start_date, @end_date = parse_date_range(@date_range)
  end

  def parse_date_range(range)
    case range
    when "7d"
      [7.days.ago, Date.current]
    when "30d"
      [30.days.ago, Date.current]
    when "90d"
      [90.days.ago, Date.current]
    when "1y"
      [1.year.ago, Date.current]
    when "custom"
      [Date.parse(params[:start_date]), Date.parse(params[:end_date])]
    else
      [30.days.ago, Date.current]
    end
  end
end
