class ReportsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_date_range, only: [:analytics, :sales_analytics, :user_analytics]

  def index
    @available_reports = available_reports_for_user
  end

  def analytics
    @date_range = params[:date_range] || "30d"

    respond_to do |format|
      format.html
      format.json { render json: analytics_data }
    end
  end

  def sales_analytics
    respond_to do |format|
      format.json { render json: sales_analytics_data }
    end
  end

  def user_analytics
    respond_to do |format|
      format.json { render json: user_analytics_data }
    end
  end

  def login
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @users = User.includes(:store).where(last_sign_in_at: start..finish).distinct

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = user-login-#{timestamp}.xlsx"
      }
      format.csv {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = user-login-#{timestamp}.csv"
      }
      format.pdf {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = user-login-#{timestamp}.pdf"
      }
    end
  end

  def sales
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @sales = Sale.includes(:product, :user, :brand, :region).where(sold_at: start..finish)

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = sales-#{timestamp}.xlsx"
      }
      format.csv {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = sales-#{timestamp}.csv"
      }
      format.pdf {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = sales-#{timestamp}.pdf"
      }
    end
  end

  def orders
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @orders = Order.includes(:line_items, :user, :brand).where(created_at: start..finish)
    @top_products = Product.joins(:order).from("orders").from("products").where("orders.created_at": start..finish).top(:name)

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = orders-#{timestamp}.xlsx"
      }
      format.csv {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = orders-#{timestamp}.csv"
      }
      format.pdf {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = orders-#{timestamp}.pdf"
      }
    end
  end

  private

  def set_date_range
    @start_date, @end_date = case params[:date_range]
    when "7d"
      [7.days.ago, Date.current]
    when "30d"
      [30.days.ago, Date.current]
    when "90d"
      [90.days.ago, Date.current]
    when "1y"
      [1.year.ago, Date.current]
    when "custom"
      [Date.parse(params[:start_date]), Date.parse(params[:end_date])]
    else
      [30.days.ago, Date.current]
    end
  end

  def available_reports_for_user
    reports = [
      {
        category: "User Activity",
        reports: [
          {name: "Login Activity", key: "login", description: "User login and activity reports"},
          {name: "User Performance", key: "user_performance", description: "Individual user performance metrics"}
        ]
      },
      {
        category: "Sales & Orders",
        reports: [
          {name: "Sales Activity", key: "sales", description: "Detailed sales reports and analytics"},
          {name: "Order Activity", key: "orders", description: "Order processing and fulfillment reports"},
          {name: "Sales Analytics", key: "sales_analytics", description: "Advanced sales trend analysis"}
        ]
      }
    ]

    if current_user.admin? || current_user.super_admin?
      reports << {
        category: "Administrative",
        reports: [
          {name: "System Analytics", key: "system_analytics", description: "System-wide performance metrics"},
          {name: "Regional Performance", key: "regional_performance", description: "Performance by region"},
          {name: "Brand Analytics", key: "brand_analytics", description: "Brand-specific analytics"}
        ]
      }
    end

    reports
  end

  def analytics_data
    {
      sales_trend: sales_trend_data,
      user_activity: user_activity_data,
      order_metrics: order_metrics_data,
      performance_summary: performance_summary_data
    }
  end

  def sales_trend_data
    scope = user_scoped_sales.where(created_at: @start_date..@end_date)
    scope.group_by_day(:created_at).count
  end

  def order_metrics_data
    scope = user_scoped_orders.where(created_at: @start_date..@end_date)
    {
      daily_orders: scope.group_by_day(:created_at).count,
      status_breakdown: scope.group(:status).count,
      total_value: scope.sum(&:total_value)
    }
  end

  def performance_summary_data
    {
      total_sales: user_scoped_sales.where(created_at: @start_date..@end_date).count,
      total_orders: user_scoped_orders.where(created_at: @start_date..@end_date).count,
      conversion_rate: calculate_conversion_rate,
      average_order_value: calculate_average_order_value
    }
  end

  def user_scoped_orders
    return Order.all if current_user.super_admin?
    return Order.joins(:user).where(users: {store: {brand: current_user.admin_brand}}) if current_user.brand_admin?
    return Order.joins(:user).where(users: {region: current_user.region}) if current_user.region_admin?
    return Order.where(user: current_user) if current_user.regular_user?

    Order.all
  end

  def calculate_conversion_rate
    sales_count = user_scoped_sales.where(created_at: @start_date..@end_date).count
    orders_count = user_scoped_orders.where(created_at: @start_date..@end_date).count

    return 0 if orders_count == 0
    ((sales_count.to_f / orders_count) * 100).round(2)
  end

  def calculate_average_order_value
    orders = user_scoped_orders.where(created_at: @start_date..@end_date)
    return 0 if orders.count == 0

    total_value = orders.sum(&:total_value)
    (total_value / orders.count).round(2)
  end

  def sales_analytics_data
    scope = user_scoped_sales.where(created_at: @start_date..@end_date)

    {
      daily_sales: scope.group_by_day(:created_at).count,
      status_breakdown: scope.group(:status).count,
      top_products: scope.joins(:product).group("products.name").count.sort_by(&:last).last(10).reverse,
      regional_breakdown: scope.joins(:region).group("regions.name").count,
      monthly_trend: scope.group_by_month(:created_at).count
    }
  end

  def user_activity_data
    return {} unless current_user.admin? || current_user.super_admin?

    {
      daily_logins: User.where(last_sign_in_at: @start_date..@end_date).group_by_day(:last_sign_in_at).count,
      new_registrations: User.where(created_at: @start_date..@end_date).group_by_day(:created_at).count,
      active_users: User.where(last_sign_in_at: @start_date..@end_date).count,
      user_growth: User.group_by_month(:created_at).count
    }
  end

  def user_scoped_sales
    return Sale.all if current_user.super_admin?
    return Sale.where(brand: current_user.admin_brand) if current_user.brand_admin?
    return Sale.where(region: current_user.region) if current_user.region_admin?
    return Sale.where(user: current_user) if current_user.regular_user?

    Sale.all
  end
end
