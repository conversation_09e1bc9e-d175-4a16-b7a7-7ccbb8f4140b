class ReportsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def login
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @users = User.includes(:store).where(last_sign_in_at: start..finish).distinct

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = user-login-#{timestamp}.xlsx"
      }
    end
  end

  def sales
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @sales = Sale.includes(:product).where(sold_at: start..finish)

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = sales-#{timestamp}.xlsx"
      }
    end
  end

  def orders
    start, finish = params.require(:target).match(/(.+) to (.+)/).captures
    @orders = Order.includes(:line_items).where(created_at: start..finish)

    @top_products = Product.joins(:order).from("orders").from("products").where("orders.created_at": start..finish).top(:name)

    respond_to do |format|
      format.xlsx {
        timestamp = Time.current.strftime("%Y%m%d%H%M")
        response.headers["Content-Disposition"] = "attachment; filename = orders-#{timestamp}.xlsx"
      }
    end
  end
end
