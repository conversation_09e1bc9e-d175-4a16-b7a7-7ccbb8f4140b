class OrdersController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_order, only: [:show, :edit, :update, :destroy]
  before_action :set_limit, only: [:index, :new]

  def index
    orders = Order.includes(:user).pagy_search(params[:q], filter: authorized_scope(filters(params, Order::FILTER_FIELDS, {country_name: Current.country.name}), type: :index), sort: ["created_at:desc"])
    @pagy, @orders = pagy_meilisearch(orders, items: params[:limit].to_i)

    respond_to do |format|
      format.xlsx {
        @orders = Order.includes(:admin, user: [:store]).all
        response.headers["Content-Disposition"] = 'attachment; filename="orders.xlsx"'
      }
      format.html
    end
  end

  def create
    @order = Order.new(order_params.with_defaults(brand: current_user.store.brand, region: current_user.store.region, points: Current.cart.subtotal, user_id: current_user.id))

    Current.cart.line_items.each do |item|
      @order.line_items << item
      item.cart_id = nil
    end

    if @order.save
      Cart.destroy(session[:cart_id])
      ahoy.track "Order created", {order_id: @order.id, user_id: @order.user.id}
      redirect_to @order
    end
  end

  def update
    if @order.update(order_params.with_defaults(admin: current_user))
      redirect_to @order, notice: "Order was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def pending_count
    @pending_orders = Order.joins(:brand, :region, user: [address: :state]).where(status: :pending).group(region_or_state_group(current_user.admin_region)).where(brand_where(current_user.admin_brand)).where(region_where(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @pending_orders }
    end
  end

  def region_count
    @orders_by_region = Order.joins(:brand, :region, user: [address: :state]).group(region_or_state_group(current_user.admin_region)).where(brand_where(current_user.admin_brand)).where(region_where(current_user.admin_region)).where("addresses.country_id = ?", Current.country.id).size

    respond_to do |format|
      format.json { render json: @orders_by_region }
    end
  end

  def stats
    @total_orders = Brand.joins(stores: [:address, users: :orders]).where(brand_where(current_user.admin_brand)).where("addresses.country_id = ?", Current.country.id).size
    @orders_in_the_last_month = Brand.joins(stores: [:address, users: :orders]).where(brand_where(current_user.admin_brand)).where("orders.created_at >=  ?", 1.month.ago).where("addresses.country_id = ?", Current.country.id).size

    render layout: false
  end

  def destroy
    @order.destroy
    redirect_to orders_url, notice: "Order was successfully destroyed."
  end

  private

  def set_order
    @order = Order.find(params[:id])
    authorize! @order
  end

  def order_params
    params.require(:order).permit(:notes, :user_id, :status, :sap_id, :declined_at, :ship_to, notations_attributes: [:user_id, :content])
  end

  def set_limit
    params[:limit] ||= 20
  end
end
