class LineItemsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :load_line_item, except: [:create]

  def create
    cart = Current.cart
    product = Product.find(params[:product_id])

    if cart.products.include?(product)
      @line_item = cart.line_items.find_by(product_id: product)
      @line_item.quantity += 1
    else
      @line_item = LineItem.new(cart: cart, product: product)
    end

    if @line_item.save
      redirect_to cart
    end
  end

  def destroy
    @line_item.destroy
    redirect_to Current.cart
  end

  def add
    @line_item.quantity += 1

    if @line_item.save
      redirect_to Current.cart
    end
  end

  def remove
    if @line_item.quantity > 1
      @line_item.quantity -= 1
    end

    if @line_item.save
      redirect_to Current.cart
    end
  end

  private

  def load_line_item
    @line_item = LineItem.find(params[:id])
  end
end
