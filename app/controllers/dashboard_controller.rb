class DashboardController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :verify_point_account
  before_action :set_variant

  def index
    # This method will render the appropriate dashboard variant
    # based on the user's role, set in set_variant

    # Preload data for super admin dashboard to avoid N+1 queries
    if current_user.super_admin?
      prepare_super_admin_data
    end
  end

  def help
    @help = Help.new(help_params)

    HelpMailer.with(name: @help.name, email: @help.email, message: @help.message, help_type: @help.help_type).enquiry.deliver_later
    redirect_to root_path, notice: "Your enquiry has been sent. We will get back to you shortly."
  end

  def help_form
    @help = Help.new
    @help.name = current_user.name
    @help.email = current_user.email
  end

  private

  def help_params
    params.require(:help).permit(:name, :email, :message, :help_type)
  end

  def prepare_super_admin_data
    # Precompute brand statistics to avoid N+1 queries
    @brand_stats = compute_brand_statistics
    @user_growth_stats = compute_user_growth_statistics
  end

  def compute_brand_statistics
    # Get all brands with their basic info
    brands = Brand.order(:name)

    # Compute store counts per brand in a single query
    store_counts = Store.group(:brand_id).count

    # Compute user counts per brand in a single query (through stores)
    user_counts = User.joins(:store).group("stores.brand_id").count

    # Compute monthly sales counts per brand in a single query
    current_month_start = Date.current.beginning_of_month
    current_month_end = Date.current.end_of_month
    monthly_sales_counts = Sale.where(created_at: current_month_start..current_month_end)
      .group(:brand_id).count

    # Compute approved points per brand in a single query
    approved_points = Sale.approved.group(:brand_id).sum(:points)

    # Build the statistics hash
    brands.map do |brand|
      {
        brand: brand,
        stores_count: store_counts[brand.id] || 0,
        users_count: user_counts[brand.id] || 0,
        monthly_sales_count: monthly_sales_counts[brand.id] || 0,
        approved_points: approved_points[brand.id] || 0
      }
    end
  end

  def compute_user_growth_statistics
    # Compute user growth for the last 6 months in a single query
    stats = []
    (0..5).each do |months_ago|
      month_start = months_ago.months.ago.beginning_of_month
      stats << {
        month_start: month_start,
        month_name: month_start.strftime("%B %Y")
      }
    end

    # Get all user counts for the date ranges
    user_counts_by_month = {}

    stats.each do |stat|
      month_start = stat[:month_start]
      month_end = month_start.end_of_month
      user_counts_by_month[month_start] = User.where(created_at: month_start..month_end).count
    end

    # Add counts to stats
    stats.each do |stat|
      stat[:user_count] = user_counts_by_month[stat[:month_start]] || 0
    end

    stats
  end

  def verify_point_account
    if user_signed_in? && current_user.wallet.nil?
      current_user.create_wallet
    end
  end

  def set_variant
    request.variant = case current_user.role
    when "regular_user"
      :regular
    when "region_admin"
      :region_admin
    when "optics_admin"
      :brand_admin
    when "photo_admin"
      :brand_admin
    when "admin"
      :admin
    when "super_admin"
      :super_admin
    else
      :admin # fallback
    end
  end
end
