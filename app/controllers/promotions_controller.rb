class PromotionsController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_promotion, only: [:show, :destroy]

  def index
    @promotions = Promotion.all
  end

  def new
    @promotion = Promotion.new
    authorize! @promotion
  end

  def create
    @promotion = Promotion.new(promotion_params)

    if @promotion.save
      respond_to do |format|
        format.html { redirect_to promotions_path }
      end
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @promotion.destroy
    redirect_to promotions_path, status: :see_other
  end

  private

  def set_promotion
    @promotion = Promotion.includes(:stores, :products).find(params[:id])
    authorize! @promotion
  end

  def promotion_params
    params.require(:promotion).permit(:name, :multiplier, :dates, location_sgids: [], product_ids: [])
  end
end
