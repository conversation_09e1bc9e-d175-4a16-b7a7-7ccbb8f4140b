class CartsController < ApplicationController
  before_action :authenticate_user_with_sign_up!

  def show
    @cart = Current.cart
  end

  def destroy
    @cart = Current.cart
    @cart.destroy
    session[:cart_id] = nil
    redirect_to root_path
  end

  private

  def order_params
    params.require(:order).permit(:ship_to)
  end

  def set_variant
    if current_user.admin? || current_user.super_admin?
      request.variant = :admin
    end
  end
end
