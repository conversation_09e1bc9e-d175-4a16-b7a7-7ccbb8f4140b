class PricesController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :load_product

  def new
    @price = Price.new
    @countries = Country.available(@product)
  end

  def create
    @price = Price.new(price_params.with_defaults(product: @product))

    if @price.save
      respond_to do |format|
        format.html { redirect_to @product }
        format.turbo_stream { render turbo_stream: turbo_stream.close_dialog("price_dialog") }
      end
    else
      @countries = Country.available(@product)
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @price = Price.find(params[:id])
  end

  def update
    @price = Price.find(params[:id])

    if @price.update(price_params)
      respond_to do |format|
        format.html { redirect_to @product }
        format.turbo_stream { render turbo_stream: turbo_stream.close_dialog("price_dialog") }
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def price_params
    params.require(:price).permit(:msrp, :points_needed, :points_earned, :country_id)
  end

  def load_product
    @product = Product.find(params[:product_id])
    authorize! @product
  end
end
