class MessagesController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :set_message, only: [:show]

  def index
    @sent_pagy, @sent_messages = pagy(Message.sent, page_param: :sent_page)
    @pending_pagy, @pending_messages = pagy(Message.pending, page_param: :pending_page)
  end

  def new
    @message = Message.new
  end

  def create
    @message = Message.new(message_params)
    if @message.send_on.blank?
      @message.sent_at = Date.current
    end

    if @message.save
      if @message.send_on.blank?
        NewMessageNotification.with(message: @message).deliver_later(@message.messageable.present? ? @message.messageable.users : User.regular_users)
      end

      redirect_to messages_path, notice: "Message sent"
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def set_message
    @message = Message.find(params[:id])
  end

  def message_params
    params.require(:message).permit(:subject, :body, :recipient_sgid, :send_on)
  end
end
