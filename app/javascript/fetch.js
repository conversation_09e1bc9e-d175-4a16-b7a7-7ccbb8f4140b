import { FetchRequest } from "@rails/request.js";

export default function (Alpine) {
  Alpine.magic("fetch", (el) => (data) => {
    let method = el.dataset.fetchMethod;

    switch (method) {
      case "get":
        new FetchRequest(method, data, {
          responseKind: "turbo-stream",
        }).perform();
        break;
      case "post":
      case "patch":
        let values = JSON.parse(data);
        new FetchRequest(method, el.dataset.fetchUrl, {
          responseKind: "turbo-stream",
          body: {
            ...values,
          },
        }).perform();
        break;
    }
  });
}
