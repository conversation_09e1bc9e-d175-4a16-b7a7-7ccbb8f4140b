// ZeissPoints Application JavaScript - Built with ESBuild

// Import CSS dependencies
import "tippy.js/dist/tippy.css";
import "trix/dist/trix.css";

// Import JavaScript libraries
import Alpine from "alpinejs";
import persist from "@alpinejs/persist";
import focus from "@alpinejs/focus";
import mask from "@alpinejs/mask";
import ui from "@alpinejs/ui";
import "@hotwired/turbo-rails";
import "chartkick/chart.js";
import "trix";
import ahoy from "ahoy.js";
import LocalTime from "local-time";
import "./controllers";
import components from "./components";
import "./stream_actions";
import * as ActiveStorage from "@rails/activestorage";

import tabs from "./tabs";
import fetch from "./fetch";
import timeout from "./timeout";

ahoy.configure({ cookies: false });

Alpine.plugin(persist);
Alpine.plugin(timeout);
Alpine.plugin(focus);
Alpine.plugin(mask);
Alpine.plugin(ui);
Alpine.plugin(fetch);
Alpine.plugin(components);
Alpine.data("tabs", tabs);
Alpine.start();

LocalTime.start();

ActiveStorage.start();
