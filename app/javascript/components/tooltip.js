import tippy from "tippy.js";

export default function (Alpine) {
  Alpine.directive("tooltip", (el, { expression, modifiers }) => {
    if (modifiers.includes("overflow")) {
      if (isOverflown(el)) {
        tippy(el, { content: expression });
      }
    } else {
      tippy(el, { content: expression });
    }
  });
}

function isOverflown({ clientWidth, clientHeight, scrollWidth, scrollHeight }) {
  return scrollHeight > clientHeight || scrollWidth > clientWidth;
}
