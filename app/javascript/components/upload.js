import { DirectUpload } from "@rails/activestorage";
import Dropzone from "dropzone";
import Croppie from "croppie";

export default function (Alpine) {
  Alpine.directive("upload", (el, { value }) => {
    switch (value) {
      case "dropzone":
        handleDropzone(el, Alpine);
        break;
      case "cropper":
        handleCropper(el, Alpine);
        break;
      case "input":
        handleInput(el, Alpine);
        break;
      case "button":
        handleButton(el, Alpine);
        break;
      case "drop-panel":
        handleDropPanel(el, Alpine);
        break;
      case "crop-panel":
        handleCropPanel(el, Alpine);
        break;
      default:
        handleRoot(el, Alpine);
    }
  });
}

function handleRoot(el, Alpine) {
  Alpine.bind(el, {
    "x-data"() {
      return {
        __inputEl: undefined,
        __cropper: undefined,
        __dropOpen: true,
        __cropOpen: false,
        __filename: undefined,
        __cropImage(file) {
          this.__dropOpen = false;
          this.__cropOpen = true;

          this.__cropper.bind({
            url: URL.createObjectURL(file),
          });
        },
        __uploadFile(file) {
          let upload = new DirectUpload(
            new File([file], this.__filename),
            this.uploadUrl
          );

          upload.create((error, blob) => {
            if (error) {
            } else {
              let hidden = document.createElement("input");
              let element = document.getElementById("product_image");
              hidden.type = "hidden";
              hidden.name = this.$data.__inputEl.name;
              hidden.value = blob.signed_id;
              element.parentNode.insertBefore(hidden, element.nextSibling);
            }
          });
        },
        __cropClick() {
          this.__cropper.result({ type: "blob" }).then(
            function (blob) {
              let image = document.getElementById("product_image");
              image.src = URL.createObjectURL(blob);
              this.$data.__uploadFile(blob);
            }.bind(this)
          );
          this.$data.modalOpen = false;
        },
      };
    },
  });
}

function handleDropzone(el, Alpine) {
  Alpine.bind(el, {
    "x-init"() {
      this.$el.__dropzone = new Dropzone(this.$el, {
        autoQueue: false,
        url: this.$data.uploadUrl,
        acceptedFile: this.$data.acceptedFiles,
        ...(this.$refs.preview && { previewsContainer: this.$refs.preview }),
        ...(this.$refs.previewTemplate && {
          previewTemplate: this.$refs.previewTemplate.innerHTML,
        }),
        disablePreviews: this.$data.disablePreviews,
      });

      this.$el.__dropzone.on("addedfile", (file) => {
        this.$data.__filename = file.name;
        if (this.$data.__cropper) {
          this.$data.__cropImage(file);
        } else {
          this.$data.__uploadFile(file);
        }
      });
    },
  });
}

function handleInput(el, Alpine) {
  Alpine.bind(el, {
    "x-init"() {
      this.$data.__inputEl = this.$el;
      this.$el.setAttribute("accept", this.$data.acceptedFiles);
    },
    "@changed"($event) {
      let file = $event.target.files[0];
      this.$data.__filename = file.name;
      if (this.$data.__cropper) {
        this.$data.__cropImage(file);
      } else {
        this.$data.__uploadFile(file);
      }
    },
  });
}

function handleCropper(el, Alpine) {
  Alpine.bind(el, {
    "x-init"() {
      this.$data.__cropper = new Croppie(this.$el, {
        enableResize: true,
        enableExif: true,
        showZoomer: false,
        boundary: { width: 400, height: 200 },
      });
    },
  });
}

function handleButton(el, Alpine) {
  Alpine.bind(el, {
    "x-init"() {
      if (
        this.$el.tagName.toLowerCase() === "button" &&
        !this.$el.hasAttribute("type")
      )
        this.$el.type = "button";
    },
    "@click"() {
      this.$data.__cropClick();
    },
  });
}

function handleCropPanel(el, Alpine) {
  Alpine.bind(el, {
    "x-show"() {
      return this.$data.__cropOpen;
    },
  });
}

function handleDropPanel(el, Alpine) {
  Alpine.bind(el, {
    "x-show"() {
      return this.$data.__dropOpen;
    },
  });
}
