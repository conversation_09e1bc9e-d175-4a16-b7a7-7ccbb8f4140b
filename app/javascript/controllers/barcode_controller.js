import { Controller } from "@hotwired/stimulus";
import Quagga from "quagga";
import { FetchRequest } from "@rails/request.js";
import _ from "underscore";

export default class extends Controller {
  static values = { endPoint: String };

  connect() {
    Quagga.init(
      {
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: this.element,
          constraints: {
            width: 640,
            height: 480,
          },
        },
        decoder: {
          readers: ["upc_reader"],
          multiple: false,
        },
        locate: true,
        locator: {
          patchSize: "large",
        },
      },
      (err) => {
        if (err) {
          console.log(err);
          return;
        }
        Quagga.start();
      }
    );

    Quagga.onDetected(_.throttle(this.onDetected.bind(this), 1000));
  }

  async onDetected(result) {
    const code = result.codeResult.code;
    console.log(`Barcode detected: ${code}`);
    const request = new FetchRequest("get", `/products/${code}`, {
      responseKind: "json",
    });

    const scanned = new Event("scanned");

    const response = await request.perform();

    if (response.ok) {
      console.log("Product found");
      const data = await response.json;
      const id = data.id;
      const product_select = document.querySelector("#sale_product_id");
      document.querySelector("#product_missing").classList.add("hidden");
      product_select.slim.set(id);
    } else {
      console.log("Product not found");
      document.querySelector("#product_missing").classList.remove("hidden");
    }

    this.element.dispatchEvent(scanned);
  }
}
