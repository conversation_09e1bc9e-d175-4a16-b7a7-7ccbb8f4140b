class SalePolicy < ApplicationPolicy
  # Anyone can create a sale but only region admins or higher can update. View is restricted to your own sales unless an admin.
  def index?
    true
  end

  def create?
    true
  end

  def show?
    user.admin? || record.user = user
  end

  scope_for :index do |data|
    data << "user_id=#{user.id}" if user.regular_user?
    data << "brand_id=#{user.admin_brand.id}" if user.admin_brand.present?
    data << "region_id=#{user.admin_region.id}" if user.admin_region.present?
    data
  end
end
