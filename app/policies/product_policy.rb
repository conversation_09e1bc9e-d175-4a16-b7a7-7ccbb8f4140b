class ProductPolicy < ApplicationPolicy
  # Restrict to optics or photo admins for create, update and delete. Anyone can view.
  def index?
    user.admin?
  end

  def show?
    true
  end

  def update?
    user.admin? && record.category.brand == user.admin_brand
  end

  def add_to_cart?
    true
  end

  def sale?
    true
  end

  def create?
    user.admin?
  end

  scope_for :index do |data|
    data << "brand_id=#{user.store.brand.id}" if user.regular_user?
    data
  end

  relation_scope(:sale) do |scope|
    scope.joins(:category).where(category: {brand: user.store.brand}).where.not(gift_card: true).order(:name)
  end

  #  class Scope < Scope
  #    def resolve
  #      if user.regular_user?
  #        scope.joins(:category).where(category: {brand: user.store.brand})
  #      else
  #        scope.includes(category: :brand).all.with_attached_image
  #      end
  #    end
  #  end
end
