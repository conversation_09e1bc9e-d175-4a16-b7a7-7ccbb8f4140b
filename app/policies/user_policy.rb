class UserPolicy < ApplicationPolicy
  # Anyone can sign up but a user can edit their own profile and an admin can edit any profile

  def index?
    user.admin?
  end

  def show?
    user.admin? || user == record
  end

  def update?
    user.admin? || user == record
  end

  def impersonate?
    user.admin?
  end

  def activate?
    user.admin?
  end

  def export?
    user.admin?
  end

  scope_for :index do |data|
    data << "brand_id=#{user.admin_brand.id}" if user.admin_brand.present?
    data << "region_id=#{user.admin_region.id}" if user.admin_region.present?
    data
  end

  #  class Scope < Scope
  #    def resolve
  #      if user.admin?
  #        scope.includes(:admin_brand, :point_account, :admin_region).joins(store: [:brand, state: :region]).where(role: :regular_user).where(brand_where(user.admin_brand)).where(region_where(user.admin_region))
  #      else
  #        scope.includes(:admin_brand, :point_account, :admin_region, store: [:brand, state: :region]).all
  #      end
  #    end
  #  end
end
