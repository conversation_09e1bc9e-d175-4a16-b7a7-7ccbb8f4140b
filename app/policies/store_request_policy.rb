class StoreRequestPolicy < ApplicationPolicy
  # Anyone can request a new store but only an admin can update
  def index?
    user.admin?
  end
  
  def update?
    user.admin?
  end

  #  class Scope < Scope
  #    def resolve
  #      if user.admin?
  #        scope.joins(:brand, :region).where(region_where(user.admin_region)).where(brand_where(user.admin_brand)).where(status: "pending").order(:created_at)
  #      else
  #        scope.all
  #      end
  #    end
  #  end
end
