class RegionPolicy < ApplicationPolicy
  def index?
    user.admin?
  end

  # Restrict to photo and optics admins for create, update and delete. View is anyone
  #  class Scope < Scope
  #    def resolve
  #      if user.admin?
  #        scope.where(region_where(user.admin_region))
  #      elsif user.regular_user?
  #        scope.where(users: {id: user.id})
  #      else
  #        scope.all
  #      end
  #    end
  #  end
end
