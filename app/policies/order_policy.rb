class OrderPolicy < ApplicationPolicy
  # Anybody can create and view an order but you need to be region or higher to edit and approve or decline
  def index?
    true
  end

  def create?
    true
  end

  def show?
    user.admin? || record.user = user
  end

  scope_for :index do |data|
    data << "brand_id=#{user.admin_brand.id}" if user.admin_brand.present?
    data << "region_id=#{user.admin_region.id}" if user.admin_region.present?
    data << "user_id=#{user.id}" if user.regular_user?
    data
  end

  #  class Scope < Scope
  #    def resolve
  #      if user.regular_user?
  #        scope.where(user_id: user.id).order(:created_at)
  #      elsif user.admin?
  #        scope.joins(:brand, :region).where(brand_where(user.admin_brand)).where(region_where(user.admin_region))
  #      else
  #        scope.all
  #      end
  #    end
  #  end
end
