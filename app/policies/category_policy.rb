class CategoryPolicy < ApplicationPolicy
  def index?
    user.admin?
  end

  def create?
    user.admin?
  end

  def show?
    user.admin?
  end

  relation_scope do |relation|
    next relation if user.super_admin?
    relation.joins(:brand).where(brand: user.admin_brand)
  end

  # Restrict to photo and optics admins and higher
  #  class Scope < Scope
  #    def resolve
  #      if user.admin?
  #        scope.joins(:brand).where(brand: user.admin_brand)
  #      else
  #        scope.all
  #      end
  #    end
  #  end
end
