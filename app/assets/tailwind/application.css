@import "tailwindcss";

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/aspect-ratio';
@plugin '@tailwindcss/typography';

@custom-variant dark (@media (prefers-color-scheme: dark));

@theme {
  --color-zeiss-50: #f3f4f9;
  --color-zeiss-100: #e8e9f4;
  --color-zeiss-200: #c4c7e2;
  --color-zeiss-300: #a1a5d1;
  --color-zeiss-400: #5b62af;
  --color-zeiss-500: #141e8c;
  --color-zeiss-600: #121b7e;
  --color-zeiss-700: #0f1769;
  --color-zeiss-800: #0c1254;
  --color-zeiss-900: #0a0f45;

  --font-family-sans: "Inter", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

[x-cloak] {
  display: none !important;
}

:root {
  --status-red: var(--color-red-500);
  --status-green: var(--color-green-500);
  --status-yellow: var(--color-yellow-500);
}

.fa-secondary {
  fill: var(--fa-secondary-color);
}

.fa-primary {
  fill: currentColor;
}

/* Define color channels for CSS variables to work with opacity modifiers */
@layer base {
  :root {
    /* Define RGB channels for zeiss colors */
    --color-zeiss-50-rgb: 243 244 249;
    --color-zeiss-100-rgb: 232 233 244;
    --color-zeiss-200-rgb: 196 199 226;
    --color-zeiss-300-rgb: 161 165 209;
    --color-zeiss-400-rgb: 91 98 175;
    --color-zeiss-500-rgb: 20 30 140;
    --color-zeiss-600-rgb: 18 27 126;
    --color-zeiss-700-rgb: 15 23 105;
    --color-zeiss-800-rgb: 12 18 84;
    --color-zeiss-900-rgb: 10 15 69;

    /* Define RGB channels for status colors */
    --status-red-rgb: var(--color-red-500-rgb);
    --status-green-rgb: var(--color-green-500-rgb);
    --status-yellow-rgb: var(--color-yellow-500-rgb);
  }
}
