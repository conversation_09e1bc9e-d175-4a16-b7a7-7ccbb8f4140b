require "csv"

class GiftcardService < ApplicationService
  option :start, optional: true
  option :finish, optional: true

  def call
    begins = start || 1.day.ago.beginning_of_day
    ends = finish || 1.day.ago.end_of_day
    orders = Order.includes(:line_items, user: [store: :state]).where(line_items: {gift_card: true}).where(approved_at: begins..ends).where(status: ["approved", "processed"])

    return nil if orders.empty?

    filename = "WWR-ZS3PG-#{Time.current.strftime("%Y%m%d%H%M")}.csv"
    file = Rails.root.join("tmp", filename)

    CSV.open(file, "wb", col_sep: "|") do |csv|
      headers = ["FirstName", "LastName", "Company", "Street1", "Street2", "City", "State", "Zip", "Country", "Phone", "Email", "Amount", "OfferCode", "Message"]
      csv << headers

      orders.each do |order|
        street, city, state, zip = order.home? ? [order.user.street, order.user.city, order.user.state.name, order.user.zip] : [order.user.store.street, order.user.store.city, order.user.store.state.name, order.user.store.zip]

        order.line_items.each do |product|
          if product.gift_card?
            csv << [order.user.first_name, order.user.last_name, order.user.store.name, street, nil, city, state, zip, "US", order.user.store.phone_number || nil, order.user.email, product.product.gift_card_value, "ZS3-10000", nil]
          end
        end
      end
    end

    fulfillment = Fulfillment.create(begins_at: begins, ends_at: ends, order_count: orders.size)
    fulfillment.csv.attach(io: File.open(file), filename: filename, content_type: "text/csv")

    [fulfillment, filename]
  end
end
