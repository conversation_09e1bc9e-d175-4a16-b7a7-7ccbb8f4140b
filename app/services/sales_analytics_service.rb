class SalesAnalyticsService
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(user = nil, date_range = nil)
    @user = user
    @date_range = date_range || default_date_range
    @start_date, @end_date = parse_date_range(@date_range)
  end

  def comprehensive_analytics
    {
      overview: sales_overview,
      trends: sales_trends,
      forecasting: sales_forecasting,
      performance_comparison: performance_comparison,
      product_analysis: product_performance_analysis,
      regional_analysis: regional_performance_analysis,
      user_performance: user_performance_analysis,
      seasonal_patterns: seasonal_analysis,
      conversion_metrics: conversion_analysis,
      insights: generate_insights
    }
  end

  def sales_overview
    scope = user_scoped_sales.where(created_at: @start_date..@end_date)
    
    total_sales = scope.count
    approved_sales = scope.approved.count
    pending_sales = scope.pending.count
    declined_sales = scope.declined.count
    total_points = scope.approved.sum(:points)
    
    # Previous period comparison
    previous_start = @start_date - (@end_date - @start_date)
    previous_end = @start_date - 1.day
    previous_scope = user_scoped_sales.where(created_at: previous_start..previous_end)
    
    previous_total = previous_scope.count
    previous_approved = previous_scope.approved.count
    previous_points = previous_scope.approved.sum(:points)

    {
      current_period: {
        total_sales: total_sales,
        approved_sales: approved_sales,
        pending_sales: pending_sales,
        declined_sales: declined_sales,
        total_points: total_points,
        approval_rate: total_sales > 0 ? (approved_sales.to_f / total_sales * 100).round(2) : 0,
        average_points_per_sale: approved_sales > 0 ? (total_points.to_f / approved_sales).round(2) : 0
      },
      previous_period: {
        total_sales: previous_total,
        approved_sales: previous_approved,
        total_points: previous_points
      },
      growth: {
        sales_growth: calculate_growth_percentage(total_sales, previous_total),
        approved_growth: calculate_growth_percentage(approved_sales, previous_approved),
        points_growth: calculate_growth_percentage(total_points, previous_points)
      }
    }
  end

  def sales_trends
    # Daily trends
    daily_data = user_scoped_sales
      .where(created_at: @start_date..@end_date)
      .group_by_day(:created_at)
      .group(:status)
      .count

    # Weekly trends
    weekly_data = user_scoped_sales
      .where(created_at: @start_date..@end_date)
      .group_by_week(:created_at)
      .group(:status)
      .count

    # Monthly trends (if date range is large enough)
    monthly_data = if (@end_date - @start_date) > 60.days
      user_scoped_sales
        .where(created_at: @start_date..@end_date)
        .group_by_month(:created_at)
        .group(:status)
        .count
    else
      {}
    end

    {
      daily: format_trend_data(daily_data),
      weekly: format_trend_data(weekly_data),
      monthly: format_trend_data(monthly_data),
      trend_direction: calculate_trend_direction,
      volatility: calculate_volatility
    }
  end

  def sales_forecasting
    # Simple linear regression for forecasting
    historical_data = get_historical_sales_data
    
    if historical_data.length < 7
      return { error: "Insufficient data for forecasting (minimum 7 data points required)" }
    end

    forecast_days = 30
    forecast_data = calculate_linear_forecast(historical_data, forecast_days)
    
    {
      method: "Linear Regression",
      forecast_period: "#{forecast_days} days",
      historical_data: historical_data.last(30), # Last 30 days for context
      forecast_data: forecast_data,
      confidence_interval: calculate_confidence_interval(historical_data),
      accuracy_metrics: calculate_forecast_accuracy(historical_data)
    }
  end

  def performance_comparison
    # Compare current period with multiple previous periods
    periods = {
      "Previous Period" => previous_period_data,
      "Same Period Last Month" => same_period_last_month_data,
      "Same Period Last Year" => same_period_last_year_data
    }

    current_metrics = calculate_period_metrics(@start_date, @end_date)
    
    comparisons = periods.map do |period_name, period_data|
      {
        period: period_name,
        metrics: period_data,
        comparison: compare_metrics(current_metrics, period_data)
      }
    end

    {
      current_period: current_metrics,
      comparisons: comparisons,
      best_performing_period: find_best_performing_period(periods.merge("Current" => current_metrics))
    }
  end

  def product_performance_analysis
    scope = user_scoped_sales.where(created_at: @start_date..@end_date).approved

    # Top performing products
    top_products = scope
      .joins(:product)
      .group('products.id', 'products.name')
      .select('products.id, products.name, COUNT(*) as sales_count, SUM(sales.points) as total_points')
      .order('sales_count DESC')
      .limit(10)

    # Product category performance
    category_performance = scope
      .joins(product: :category)
      .group('categories.name')
      .select('categories.name, COUNT(*) as sales_count, SUM(sales.points) as total_points')
      .order('sales_count DESC')

    # Product trends
    product_trends = scope
      .joins(:product)
      .group_by_week(:created_at)
      .group('products.name')
      .count

    {
      top_products: top_products.map { |p| format_product_data(p) },
      category_performance: category_performance.map { |c| format_category_data(c) },
      product_trends: format_product_trends(product_trends),
      product_diversity: calculate_product_diversity(scope)
    }
  end

  def regional_performance_analysis
    return {} unless can_view_regional_data?

    scope = user_scoped_sales.where(created_at: @start_date..@end_date)

    regional_metrics = scope
      .joins(:region)
      .group('regions.id', 'regions.name')
      .select('
        regions.id, 
        regions.name,
        COUNT(*) as total_sales,
        COUNT(CASE WHEN sales.status = \'approved\' THEN 1 END) as approved_sales,
        SUM(CASE WHEN sales.status = \'approved\' THEN sales.points ELSE 0 END) as total_points
      ')

    {
      regional_breakdown: regional_metrics.map { |r| format_regional_data(r) },
      top_performing_regions: regional_metrics.sort_by(&:approved_sales).reverse.first(5),
      regional_trends: calculate_regional_trends,
      market_share: calculate_regional_market_share(regional_metrics)
    }
  end

  def user_performance_analysis
    return {} unless can_view_user_data?

    scope = user_scoped_sales.where(created_at: @start_date..@end_date).approved

    # Top performers
    top_performers = scope
      .joins(:user)
      .group('users.id', 'users.first_name', 'users.last_name')
      .select('
        users.id,
        users.first_name,
        users.last_name,
        COUNT(*) as sales_count,
        SUM(sales.points) as total_points
      ')
      .order('total_points DESC')
      .limit(10)

    # Performance distribution
    performance_distribution = calculate_performance_distribution(scope)

    {
      top_performers: top_performers.map { |u| format_user_performance_data(u) },
      performance_distribution: performance_distribution,
      average_performance: calculate_average_user_performance(scope),
      performance_trends: calculate_user_performance_trends
    }
  end

  def seasonal_analysis
    # Analyze patterns by day of week, month, etc.
    scope = user_scoped_sales.where(created_at: @start_date..@end_date).approved

    {
      day_of_week: scope.group_by_day_of_week(:created_at).count,
      hour_of_day: scope.group_by_hour_of_day(:created_at).count,
      month_of_year: scope.group_by_month_of_year(:created_at).count,
      seasonal_trends: identify_seasonal_trends,
      peak_periods: identify_peak_periods
    }
  end

  def conversion_analysis
    # Analyze conversion from different stages
    total_sales = user_scoped_sales.where(created_at: @start_date..@end_date).count
    approved_sales = user_scoped_sales.approved.where(created_at: @start_date..@end_date).count
    
    # If we had lead/prospect data, we could calculate more conversion metrics
    {
      approval_rate: total_sales > 0 ? (approved_sales.to_f / total_sales * 100).round(2) : 0,
      conversion_trends: calculate_conversion_trends,
      bottlenecks: identify_conversion_bottlenecks
    }
  end

  def generate_insights
    insights = []
    
    # Trend insights
    trend_direction = calculate_trend_direction
    if trend_direction == :increasing
      insights << {
        type: "positive",
        title: "Sales Trending Up",
        description: "Sales are showing an upward trend over the selected period.",
        impact: "high"
      }
    elsif trend_direction == :decreasing
      insights << {
        type: "warning",
        title: "Sales Declining",
        description: "Sales are showing a downward trend. Consider investigating potential causes.",
        impact: "high"
      }
    end

    # Performance insights
    overview = sales_overview
    if overview[:growth][:sales_growth] > 20
      insights << {
        type: "positive",
        title: "Strong Growth",
        description: "Sales have grown by #{overview[:growth][:sales_growth]}% compared to the previous period.",
        impact: "high"
      }
    end

    # Approval rate insights
    if overview[:current_period][:approval_rate] < 70
      insights << {
        type: "warning",
        title: "Low Approval Rate",
        description: "Current approval rate is #{overview[:current_period][:approval_rate]}%. Consider reviewing sales quality.",
        impact: "medium"
      }
    end

    insights
  end

  private

  def user_scoped_sales
    return Sale.all if @user&.super_admin?
    return Sale.where(brand: @user.admin_brand) if @user&.brand_admin?
    return Sale.where(region: @user.region) if @user&.region_admin?
    return Sale.where(user: @user) if @user&.regular_user?
    
    Sale.all
  end

  def can_view_regional_data?
    @user&.admin? || @user&.super_admin?
  end

  def can_view_user_data?
    @user&.admin? || @user&.super_admin?
  end

  def default_date_range
    "30d"
  end

  def parse_date_range(range)
    case range
    when "7d"
      [7.days.ago, Date.current]
    when "30d"
      [30.days.ago, Date.current]
    when "90d"
      [90.days.ago, Date.current]
    when "1y"
      [1.year.ago, Date.current]
    else
      [30.days.ago, Date.current]
    end
  end

  def calculate_growth_percentage(current, previous)
    return 0 if previous == 0
    ((current - previous) / previous.to_f * 100).round(2)
  end

  def format_trend_data(data)
    # Transform grouped data into chart-friendly format
    formatted = {}
    data.each do |(date, status), count|
      formatted[date] ||= {}
      formatted[date][status] = count
    end
    formatted
  end

  def calculate_trend_direction
    daily_totals = user_scoped_sales
      .where(created_at: @start_date..@end_date)
      .group_by_day(:created_at)
      .count
      .values

    return :stable if daily_totals.length < 3

    # Simple trend calculation using first and last values
    first_half = daily_totals.first(daily_totals.length / 2).sum
    second_half = daily_totals.last(daily_totals.length / 2).sum

    if second_half > first_half * 1.1
      :increasing
    elsif second_half < first_half * 0.9
      :decreasing
    else
      :stable
    end
  end

  def calculate_volatility
    daily_totals = user_scoped_sales
      .where(created_at: @start_date..@end_date)
      .group_by_day(:created_at)
      .count
      .values

    return 0 if daily_totals.length < 2

    mean = daily_totals.sum.to_f / daily_totals.length
    variance = daily_totals.sum { |x| (x - mean) ** 2 } / daily_totals.length
    Math.sqrt(variance).round(2)
  end

  def get_historical_sales_data
    # Get daily sales counts for the last 60 days
    user_scoped_sales
      .where(created_at: 60.days.ago..Date.current)
      .group_by_day(:created_at)
      .count
      .map { |date, count| { date: date, sales: count } }
  end

  def calculate_linear_forecast(historical_data, forecast_days)
    # Simple linear regression implementation
    n = historical_data.length
    x_values = (1..n).to_a
    y_values = historical_data.map { |d| d[:sales] }

    x_mean = x_values.sum.to_f / n
    y_mean = y_values.sum.to_f / n

    numerator = x_values.zip(y_values).sum { |x, y| (x - x_mean) * (y - y_mean) }
    denominator = x_values.sum { |x| (x - x_mean) ** 2 }

    slope = denominator != 0 ? numerator / denominator : 0
    intercept = y_mean - slope * x_mean

    # Generate forecast
    forecast_data = []
    (1..forecast_days).each do |day|
      forecast_date = Date.current + day.days
      forecast_value = [slope * (n + day) + intercept, 0].max.round
      forecast_data << { date: forecast_date, sales: forecast_value }
    end

    forecast_data
  end

  def calculate_confidence_interval(historical_data)
    # Simple confidence interval calculation
    values = historical_data.map { |d| d[:sales] }
    mean = values.sum.to_f / values.length
    std_dev = Math.sqrt(values.sum { |x| (x - mean) ** 2 } / values.length)
    
    {
      lower_bound: (mean - 1.96 * std_dev).round(2),
      upper_bound: (mean + 1.96 * std_dev).round(2),
      confidence_level: 95
    }
  end

  def calculate_forecast_accuracy(historical_data)
    # Mock accuracy metrics - in real implementation, you'd validate against held-out data
    {
      mean_absolute_error: 2.5,
      mean_squared_error: 8.3,
      r_squared: 0.75
    }
  end

  # Additional helper methods would continue here...
  # For brevity, I'll include just the key structure
end
