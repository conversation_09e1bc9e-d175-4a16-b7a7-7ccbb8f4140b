class RealTimeAnalyticsService
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(user = nil)
    @user = user
    @cache_duration = 30.seconds
  end

  def live_metrics
    Rails.cache.fetch("live_metrics_#{cache_key}", expires_in: @cache_duration) do
      calculate_live_metrics
    end
  end

  def sales_stream_data
    Rails.cache.fetch("sales_stream_#{cache_key}", expires_in: @cache_duration) do
      calculate_sales_stream_data
    end
  end

  def user_activity_stream
    Rails.cache.fetch("user_activity_#{cache_key}", expires_in: @cache_duration) do
      calculate_user_activity_stream
    end
  end

  def system_health_metrics
    Rails.cache.fetch("system_health_#{cache_key}", expires_in: @cache_duration) do
      calculate_system_health_metrics
    end
  end

  def broadcast_updates
    # Broadcast live updates to connected clients
    ActionCable.server.broadcast("analytics_channel", {
      type: "metrics_update",
      data: {
        live_metrics: live_metrics,
        sales_stream: sales_stream_data,
        user_activity: user_activity_stream,
        system_health: system_health_metrics,
        timestamp: Time.current.iso8601
      }
    })
  end

  private

  def cache_key
    "#{@user&.id || "global"}_#{@user&.role || "guest"}"
  end

  def calculate_live_metrics
    now = Time.current
    today_start = now.beginning_of_day
    yesterday_start = 1.day.ago.beginning_of_day
    yesterday_end = 1.day.ago.end_of_day

    # Scope data based on user permissions
    sales_scope = user_scoped_sales
    orders_scope = user_scoped_orders
    users_scope = user_scoped_users

    # Today's metrics
    today_sales = sales_scope.where(created_at: today_start..now).count
    today_orders = orders_scope.where(created_at: today_start..now).count
    today_points = sales_scope.approved.where(created_at: today_start..now).sum(:points)

    # Yesterday's metrics for comparison
    yesterday_sales = sales_scope.where(created_at: yesterday_start..yesterday_end).count
    yesterday_orders = orders_scope.where(created_at: yesterday_start..yesterday_end).count
    yesterday_points = sales_scope.approved.where(created_at: yesterday_start..yesterday_end).sum(:points)

    # Calculate changes
    sales_change = calculate_percentage_change(today_sales, yesterday_sales)
    orders_change = calculate_percentage_change(today_orders, yesterday_orders)
    points_change = calculate_percentage_change(today_points, yesterday_points)

    {
      sales: {
        current: today_sales,
        previous: yesterday_sales,
        change: sales_change,
        trend: trend_direction(sales_change)
      },
      orders: {
        current: today_orders,
        previous: yesterday_orders,
        change: orders_change,
        trend: trend_direction(orders_change)
      },
      points: {
        current: today_points,
        previous: yesterday_points,
        change: points_change,
        trend: trend_direction(points_change)
      },
      active_users: calculate_active_users,
      pending_approvals: calculate_pending_approvals,
      last_updated: now.strftime("%H:%M:%S")
    }
  end

  def calculate_sales_stream_data
    # Last 24 hours of sales data in hourly buckets
    end_time = Time.current
    start_time = 24.hours.ago

    hourly_sales = user_scoped_sales
      .where(created_at: start_time..end_time)
      .group_by_hour(:created_at)
      .count

    # Fill in missing hours with 0
    hours = []
    (0..23).each do |hour|
      hour_time = start_time + hour.hours
      hours << {
        time: hour_time.strftime("%H:00"),
        timestamp: hour_time.to_i,
        sales: hourly_sales[hour_time] || 0
      }
    end

    {
      hours: hours,
      total: hourly_sales.values.sum,
      peak_hour: hours.max_by { |h| h[:sales] },
      average: (hourly_sales.values.sum / 24.0).round(2)
    }
  end

  def calculate_user_activity_stream
    return {} unless can_view_user_data?

    # Recent user activities
    recent_logins = User.where(last_sign_in_at: 1.hour.ago..Time.current)
      .order(last_sign_in_at: :desc)
      .limit(10)

    recent_registrations = User.where(created_at: 24.hours.ago..Time.current)
      .order(created_at: :desc)
      .limit(5)

    {
      recent_logins: recent_logins.map do |user|
        {
          id: user.id,
          name: user.name,
          email: user.email,
          time: time_ago_in_words(user.last_sign_in_at),
          role: user.role.humanize
        }
      end,
      recent_registrations: recent_registrations.map do |user|
        {
          id: user.id,
          name: user.name,
          email: user.email,
          time: time_ago_in_words(user.created_at),
          store: user.store&.name
        }
      end,
      online_users: calculate_online_users
    }
  end

  def calculate_system_health_metrics
    {
      database: {
        status: database_health_status,
        response_time: measure_database_response_time
      },
      cache: {
        status: cache_health_status,
        hit_rate: calculate_cache_hit_rate
      },
      queue: {
        status: queue_health_status,
        pending_jobs: pending_jobs_count
      },
      memory: {
        usage: memory_usage_percentage,
        status: memory_status
      }
    }
  end

  def user_scoped_sales
    return Sale.all if @user&.super_admin?
    return Sale.where(brand: @user.admin_brand) if @user&.brand_admin?
    return Sale.where(region: @user.region) if @user&.region_admin?
    return Sale.where(user: @user) if @user&.regular_user?

    Sale.all
  end

  def user_scoped_orders
    return Order.all if @user&.super_admin?
    return Order.joins(:user).where(users: {store: {brand: @user.admin_brand}}) if @user&.brand_admin?
    return Order.joins(:user).where(users: {region: @user.region}) if @user&.region_admin?
    return Order.where(user: @user) if @user&.regular_user?

    Order.all
  end

  def user_scoped_users
    return User.all if @user&.super_admin?
    return User.joins(:store).where(stores: {brand: @user.admin_brand}) if @user&.brand_admin?
    return User.where(region: @user.region) if @user&.region_admin?
    return User.where(id: @user.id) if @user&.regular_user?

    User.all
  end

  def can_view_user_data?
    @user&.admin? || @user&.super_admin?
  end

  def calculate_percentage_change(current, previous)
    return 0 if previous == 0
    ((current - previous) / previous.to_f * 100).round(1)
  end

  def trend_direction(change)
    return "up" if change > 0
    return "down" if change < 0
    "stable"
  end

  def calculate_active_users
    user_scoped_users.where(last_sign_in_at: 1.hour.ago..Time.current).count
  end

  def calculate_pending_approvals
    pending_sales = user_scoped_sales.pending.count
    pending_orders = user_scoped_orders.pending.count
    pending_sales + pending_orders
  end

  def calculate_online_users
    # Users who have been active in the last 5 minutes
    user_scoped_users.where(last_sign_in_at: 5.minutes.ago..Time.current).count
  end

  # System health check methods
  def database_health_status
    ActiveRecord::Base.connection.execute("SELECT 1")
    "healthy"
  rescue
    "error"
  end

  def measure_database_response_time
    start_time = Time.current
    ActiveRecord::Base.connection.execute("SELECT 1")
    ((Time.current - start_time) * 1000).round(2) # milliseconds
  end

  def cache_health_status
    Rails.cache.write("health_check", "ok")
    (Rails.cache.read("health_check") == "ok") ? "healthy" : "error"
  rescue
    "error"
  end

  def calculate_cache_hit_rate
    # This would need to be implemented based on your cache backend
    # For now, return a mock value
    85.5
  end

  def queue_health_status
    (pending_jobs_count < 1000) ? "healthy" : "warning"
  rescue
    "error"
  end

  def pending_jobs_count
    # This depends on your background job system (Sidekiq, etc.)

    Sidekiq::Queue.new.size
  rescue
    0
  end

  def memory_usage_percentage
    # Mock implementation - would need actual system monitoring
    rand(40..80)
  end

  def memory_status
    usage = memory_usage_percentage
    return "healthy" if usage < 70
    return "warning" if usage < 85
    "critical"
  end
end
