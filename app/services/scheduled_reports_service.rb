class ScheduledReportsService
  def initialize
    @logger = Rails.logger
  end

  def generate_scheduled_reports
    @logger.info "Starting scheduled report generation"
    
    # Get all scheduled reports that are due
    due_reports = get_due_reports
    
    due_reports.each do |scheduled_report|
      begin
        generate_and_deliver_report(scheduled_report)
        update_last_run(scheduled_report)
      rescue => e
        @logger.error "Failed to generate scheduled report #{scheduled_report[:id]}: #{e.message}"
        handle_report_error(scheduled_report, e)
      end
    end
    
    @logger.info "Completed scheduled report generation. Processed #{due_reports.count} reports"
  end

  def create_scheduled_report(params)
    scheduled_report = {
      id: SecureRandom.uuid,
      user_id: params[:user_id],
      report_type: params[:report_type],
      frequency: params[:frequency], # daily, weekly, monthly, quarterly
      format: params[:format] || 'xlsx',
      filters: params[:filters] || {},
      email_recipients: params[:email_recipients] || [],
      include_charts: params[:include_charts] || false,
      created_at: Time.current,
      last_run_at: nil,
      next_run_at: calculate_next_run_time(params[:frequency]),
      active: true
    }

    # Store in database or cache
    store_scheduled_report(scheduled_report)
    
    scheduled_report
  end

  def update_scheduled_report(id, params)
    scheduled_report = get_scheduled_report(id)
    return nil unless scheduled_report

    scheduled_report.merge!(params.slice(:frequency, :format, :filters, :email_recipients, :include_charts, :active))
    scheduled_report[:next_run_at] = calculate_next_run_time(scheduled_report[:frequency]) if params[:frequency]
    
    store_scheduled_report(scheduled_report)
    scheduled_report
  end

  def delete_scheduled_report(id)
    # Remove from storage
    remove_scheduled_report(id)
  end

  def get_user_scheduled_reports(user_id)
    # Get all scheduled reports for a user
    get_all_scheduled_reports.select { |report| report[:user_id] == user_id }
  end

  private

  def get_due_reports
    current_time = Time.current
    get_all_scheduled_reports.select do |report|
      report[:active] && 
      report[:next_run_at] && 
      report[:next_run_at] <= current_time
    end
  end

  def generate_and_deliver_report(scheduled_report)
    user = User.find(scheduled_report[:user_id])
    
    # Generate the report data
    data = fetch_report_data(scheduled_report[:report_type], scheduled_report[:filters], user)
    
    # Export the report
    export_service = ReportExportService.new(
      scheduled_report[:report_type],
      data,
      {
        format: scheduled_report[:format],
        include_charts: scheduled_report[:include_charts],
        user: user,
        date_range: scheduled_report[:filters][:date_range]
      }
    )
    
    exported_data = export_service.export
    filename = export_service.filename
    
    # Send via email
    if scheduled_report[:email_recipients].any?
      send_report_email(scheduled_report, exported_data, filename, user)
    end
    
    # Store in user's report history
    store_report_history(scheduled_report, filename, user)
  end

  def fetch_report_data(report_type, filters, user)
    case report_type
    when 'sales'
      fetch_sales_data(filters, user)
    when 'orders'
      fetch_orders_data(filters, user)
    when 'users'
      fetch_users_data(filters, user)
    when 'performance'
      fetch_performance_data(filters, user)
    else
      raise ArgumentError, "Unknown report type: #{report_type}"
    end
  end

  def fetch_sales_data(filters, user)
    scope = user_scoped_sales(user)
    
    if filters[:date_range]
      start_date, end_date = parse_date_range(filters[:date_range])
      scope = scope.where(created_at: start_date..end_date)
    else
      # Default to last month
      scope = scope.where(created_at: 1.month.ago.beginning_of_month..1.month.ago.end_of_month)
    end
    
    scope = scope.where(status: filters[:status]) if filters[:status].present?
    scope = scope.where(region_id: filters[:region_id]) if filters[:region_id].present?
    scope = scope.where(brand_id: filters[:brand_id]) if filters[:brand_id].present?
    
    scope.includes(:user, :product, :brand, :region, :admin)
  end

  def fetch_orders_data(filters, user)
    scope = user_scoped_orders(user)
    
    if filters[:date_range]
      start_date, end_date = parse_date_range(filters[:date_range])
      scope = scope.where(created_at: start_date..end_date)
    else
      scope = scope.where(created_at: 1.month.ago.beginning_of_month..1.month.ago.end_of_month)
    end
    
    scope = scope.where(status: filters[:status]) if filters[:status].present?
    scope = scope.where(brand_id: filters[:brand_id]) if filters[:brand_id].present?
    
    scope.includes(:user, :brand, :line_items, :admin)
  end

  def fetch_users_data(filters, user)
    return User.none unless user.admin? || user.super_admin?
    
    scope = user_scoped_users(user)
    
    if filters[:date_range]
      start_date, end_date = parse_date_range(filters[:date_range])
      scope = scope.where(created_at: start_date..end_date)
    end
    
    scope = scope.where(role: filters[:role]) if filters[:role].present?
    scope = scope.where(region_id: filters[:region_id]) if filters[:region_id].present?
    
    scope.includes(:store, :region, :sales)
  end

  def fetch_performance_data(filters, user)
    # Return performance metrics as a hash
    start_date, end_date = if filters[:date_range]
      parse_date_range(filters[:date_range])
    else
      [1.month.ago.beginning_of_month, 1.month.ago.end_of_month]
    end

    {
      total_sales: user_scoped_sales(user).where(created_at: start_date..end_date).count,
      approved_sales: user_scoped_sales(user).approved.where(created_at: start_date..end_date).count,
      total_points: user_scoped_sales(user).approved.where(created_at: start_date..end_date).sum(:points),
      total_orders: user_scoped_orders(user).where(created_at: start_date..end_date).count,
      period: "#{start_date.strftime('%B %Y')} - #{end_date.strftime('%B %Y')}"
    }
  end

  def user_scoped_sales(user)
    return Sale.all if user.super_admin?
    return Sale.where(brand: user.admin_brand) if user.brand_admin?
    return Sale.where(region: user.region) if user.region_admin?
    return Sale.where(user: user) if user.regular_user?
    
    Sale.all
  end

  def user_scoped_orders(user)
    return Order.all if user.super_admin?
    return Order.joins(:user).where(users: { store: { brand: user.admin_brand } }) if user.brand_admin?
    return Order.joins(:user).where(users: { region: user.region }) if user.region_admin?
    return Order.where(user: user) if user.regular_user?
    
    Order.all
  end

  def user_scoped_users(user)
    return User.all if user.super_admin?
    return User.joins(:store).where(stores: { brand: user.admin_brand }) if user.brand_admin?
    return User.where(region: user.region) if user.region_admin?
    return User.where(id: user.id) if user.regular_user?
    
    User.all
  end

  def parse_date_range(date_range)
    case date_range
    when 'last_7_days'
      [7.days.ago, Date.current]
    when 'last_30_days'
      [30.days.ago, Date.current]
    when 'last_90_days'
      [90.days.ago, Date.current]
    when 'this_month'
      [Date.current.beginning_of_month, Date.current.end_of_month]
    when 'last_month'
      [1.month.ago.beginning_of_month, 1.month.ago.end_of_month]
    when 'this_year'
      [Date.current.beginning_of_year, Date.current.end_of_year]
    else
      # Try to parse as custom range "YYYY-MM-DD to YYYY-MM-DD"
      if date_range.include?(' to ')
        start_str, end_str = date_range.split(' to ')
        [Date.parse(start_str), Date.parse(end_str)]
      else
        [30.days.ago, Date.current]
      end
    end
  end

  def calculate_next_run_time(frequency)
    case frequency
    when 'daily'
      1.day.from_now
    when 'weekly'
      1.week.from_now
    when 'monthly'
      1.month.from_now
    when 'quarterly'
      3.months.from_now
    else
      1.month.from_now
    end
  end

  def send_report_email(scheduled_report, exported_data, filename, user)
    # This would use ActionMailer to send the report
    ReportMailer.scheduled_report(
      recipients: scheduled_report[:email_recipients],
      report_type: scheduled_report[:report_type],
      filename: filename,
      data: exported_data,
      user: user
    ).deliver_now
  end

  def update_last_run(scheduled_report)
    scheduled_report[:last_run_at] = Time.current
    scheduled_report[:next_run_at] = calculate_next_run_time(scheduled_report[:frequency])
    store_scheduled_report(scheduled_report)
  end

  def handle_report_error(scheduled_report, error)
    # Log error and potentially notify administrators
    @logger.error "Scheduled report error: #{error.message}"
    @logger.error error.backtrace.join("\n")
    
    # Could send error notification email here
  end

  def store_scheduled_report(scheduled_report)
    # In a real implementation, this would store in the database
    # For now, we'll use Rails cache with a long expiration
    Rails.cache.write("scheduled_report_#{scheduled_report[:id]}", scheduled_report, expires_in: 1.year)
  end

  def get_scheduled_report(id)
    Rails.cache.read("scheduled_report_#{id}")
  end

  def get_all_scheduled_reports
    # In a real implementation, this would query the database
    # For now, we'll return an empty array since we're using cache
    []
  end

  def remove_scheduled_report(id)
    Rails.cache.delete("scheduled_report_#{id}")
  end

  def store_report_history(scheduled_report, filename, user)
    # Store report generation history
    history_entry = {
      id: SecureRandom.uuid,
      user_id: user.id,
      report_type: scheduled_report[:report_type],
      filename: filename,
      generated_at: Time.current,
      scheduled: true
    }
    
    Rails.cache.write("report_history_#{history_entry[:id]}", history_entry, expires_in: 6.months)
  end
end
