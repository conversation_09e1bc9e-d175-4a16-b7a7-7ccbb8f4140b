class UserPerformanceService
  include ActionView::Helpers::<PERSON><PERSON><PERSON>per
  include ActionView::Helpers::Date<PERSON><PERSON><PERSON>

  def initialize(user, target_user = nil, date_range = nil)
    @user = user
    @target_user = target_user || user
    @date_range = date_range || "30d"
    @start_date, @end_date = parse_date_range(@date_range)
  end

  def comprehensive_performance_report
    {
      user_profile: user_profile_data,
      performance_overview: performance_overview,
      activity_tracking: activity_tracking_data,
      goal_tracking: goal_tracking_data,
      performance_trends: performance_trends_data,
      comparative_analysis: comparative_analysis_data,
      achievements: achievements_data,
      recommendations: generate_recommendations,
      detailed_metrics: detailed_metrics_data
    }
  end

  def user_profile_data
    {
      id: @target_user.id,
      name: @target_user.name,
      email: @target_user.email,
      role: @target_user.role.humanize,
      store: @target_user.store&.name,
      brand: @target_user.store&.brand&.name,
      region: @target_user.region&.name,
      registration_date: @target_user.created_at,
      last_login: @target_user.last_sign_in_at,
      total_logins: @target_user.sign_in_count,
      status: @target_user.status,
      profile_completion: calculate_profile_completion
    }
  end

  def performance_overview
    sales_scope = @target_user.sales.where(created_at: @start_date..@end_date)
    orders_scope = @target_user.orders.where(created_at: @start_date..@end_date)
    
    # Current period metrics
    total_sales = sales_scope.count
    approved_sales = sales_scope.approved.count
    pending_sales = sales_scope.pending.count
    declined_sales = sales_scope.declined.count
    total_points_earned = sales_scope.approved.sum(:points)
    total_orders = orders_scope.count
    points_spent = orders_scope.sum(:points)
    current_balance = @target_user.wallet&.balance || 0

    # Previous period for comparison
    previous_start = @start_date - (@end_date - @start_date)
    previous_end = @start_date - 1.day
    previous_sales = @target_user.sales.where(created_at: previous_start..previous_end)
    previous_total = previous_sales.count
    previous_approved = previous_sales.approved.count
    previous_points = previous_sales.approved.sum(:points)

    {
      current_period: {
        total_sales: total_sales,
        approved_sales: approved_sales,
        pending_sales: pending_sales,
        declined_sales: declined_sales,
        total_points_earned: total_points_earned,
        total_orders: total_orders,
        points_spent: points_spent,
        current_balance: current_balance,
        approval_rate: total_sales > 0 ? (approved_sales.to_f / total_sales * 100).round(2) : 0,
        average_points_per_sale: approved_sales > 0 ? (total_points_earned.to_f / approved_sales).round(2) : 0
      },
      previous_period: {
        total_sales: previous_total,
        approved_sales: previous_approved,
        total_points_earned: previous_points
      },
      growth: {
        sales_growth: calculate_growth_percentage(total_sales, previous_total),
        approved_growth: calculate_growth_percentage(approved_sales, previous_approved),
        points_growth: calculate_growth_percentage(total_points_earned, previous_points)
      }
    }
  end

  def activity_tracking_data
    # Login activity
    login_activity = calculate_login_activity
    
    # Sales activity patterns
    sales_activity = calculate_sales_activity_patterns
    
    # Engagement metrics
    engagement_metrics = calculate_engagement_metrics

    {
      login_activity: login_activity,
      sales_activity: sales_activity,
      engagement_metrics: engagement_metrics,
      activity_score: calculate_activity_score(login_activity, sales_activity, engagement_metrics)
    }
  end

  def goal_tracking_data
    # Define goals (these could be configurable per user/role)
    monthly_sales_goal = get_user_goal(:monthly_sales) || 10
    monthly_points_goal = get_user_goal(:monthly_points) || 1000
    quarterly_sales_goal = get_user_goal(:quarterly_sales) || 30

    # Calculate current progress
    current_month_sales = @target_user.sales.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count
    current_month_points = @target_user.sales.approved.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).sum(:points)
    current_quarter_sales = @target_user.sales.where(created_at: Date.current.beginning_of_quarter..Date.current.end_of_quarter).count

    {
      monthly_sales: {
        goal: monthly_sales_goal,
        current: current_month_sales,
        progress: (current_month_sales.to_f / monthly_sales_goal * 100).round(1),
        status: goal_status(current_month_sales, monthly_sales_goal),
        days_remaining: (Date.current.end_of_month - Date.current).to_i
      },
      monthly_points: {
        goal: monthly_points_goal,
        current: current_month_points,
        progress: (current_month_points.to_f / monthly_points_goal * 100).round(1),
        status: goal_status(current_month_points, monthly_points_goal),
        days_remaining: (Date.current.end_of_month - Date.current).to_i
      },
      quarterly_sales: {
        goal: quarterly_sales_goal,
        current: current_quarter_sales,
        progress: (current_quarter_sales.to_f / quarterly_sales_goal * 100).round(1),
        status: goal_status(current_quarter_sales, quarterly_sales_goal),
        days_remaining: (Date.current.end_of_quarter - Date.current).to_i
      }
    }
  end

  def performance_trends_data
    # Daily performance over the selected period
    daily_sales = @target_user.sales
      .where(created_at: @start_date..@end_date)
      .group_by_day(:created_at)
      .count

    daily_points = @target_user.sales.approved
      .where(created_at: @start_date..@end_date)
      .group_by_day(:created_at)
      .sum(:points)

    # Weekly trends
    weekly_sales = @target_user.sales
      .where(created_at: @start_date..@end_date)
      .group_by_week(:created_at)
      .count

    # Performance by day of week
    day_of_week_performance = @target_user.sales
      .where(created_at: @start_date..@end_date)
      .group_by_day_of_week(:created_at)
      .count

    # Performance by hour (for recent data)
    hourly_performance = @target_user.sales
      .where(created_at: 7.days.ago..Time.current)
      .group_by_hour_of_day(:created_at)
      .count

    {
      daily_sales: daily_sales,
      daily_points: daily_points,
      weekly_sales: weekly_sales,
      day_of_week_performance: day_of_week_performance,
      hourly_performance: hourly_performance,
      trend_direction: calculate_trend_direction(daily_sales.values),
      consistency_score: calculate_consistency_score(daily_sales.values)
    }
  end

  def comparative_analysis_data
    return {} unless can_view_comparative_data?

    # Compare with peers in same role/region/brand
    peer_comparison = calculate_peer_comparison
    
    # Ranking within organization
    ranking_data = calculate_user_ranking
    
    # Performance percentiles
    percentile_data = calculate_performance_percentiles

    {
      peer_comparison: peer_comparison,
      ranking: ranking_data,
      percentiles: percentile_data,
      benchmark_comparison: calculate_benchmark_comparison
    }
  end

  def achievements_data
    achievements = []
    
    # Sales milestones
    total_sales = @target_user.sales.approved.count
    achievements << create_achievement("Sales Milestone", "#{total_sales} approved sales", calculate_sales_badge(total_sales))
    
    # Points milestones
    total_points = @target_user.sales.approved.sum(:points)
    achievements << create_achievement("Points Earned", "#{number_with_delimiter(total_points)} total points", calculate_points_badge(total_points))
    
    # Consistency achievements
    if calculate_consistency_score(@target_user.sales.where(created_at: 30.days.ago..Date.current).group_by_day(:created_at).count.values) > 80
      achievements << create_achievement("Consistent Performer", "High consistency score", "gold")
    end
    
    # Recent achievements
    recent_achievements = calculate_recent_achievements

    {
      all_time_achievements: achievements,
      recent_achievements: recent_achievements,
      next_milestones: calculate_next_milestones,
      achievement_score: calculate_achievement_score(achievements)
    }
  end

  def generate_recommendations
    recommendations = []
    overview = performance_overview
    
    # Performance-based recommendations
    if overview[:current_period][:approval_rate] < 70
      recommendations << {
        type: "improvement",
        title: "Improve Sales Quality",
        description: "Your approval rate is #{overview[:current_period][:approval_rate]}%. Focus on quality over quantity.",
        priority: "high",
        action_items: [
          "Review declined sales to identify patterns",
          "Ensure all required documentation is complete",
          "Double-check product information before submission"
        ]
      }
    end
    
    # Activity-based recommendations
    activity = activity_tracking_data
    if activity[:activity_score] < 60
      recommendations << {
        type: "engagement",
        title: "Increase Activity",
        description: "Your activity score suggests room for improvement in engagement.",
        priority: "medium",
        action_items: [
          "Log in more regularly to stay updated",
          "Participate in training sessions",
          "Set daily sales targets"
        ]
      }
    end
    
    # Goal-based recommendations
    goals = goal_tracking_data
    goals.each do |goal_type, goal_data|
      if goal_data[:progress] < 50 && goal_data[:days_remaining] < 10
        recommendations << {
          type: "goal",
          title: "Goal Achievement Risk",
          description: "You're at risk of missing your #{goal_type.to_s.humanize.downcase} goal.",
          priority: "high",
          action_items: [
            "Increase daily activity to meet targets",
            "Focus on high-value opportunities",
            "Consider extending working hours if possible"
          ]
        }
      end
    end

    recommendations
  end

  def detailed_metrics_data
    {
      sales_metrics: calculate_detailed_sales_metrics,
      product_preferences: calculate_product_preferences,
      time_analysis: calculate_time_analysis,
      efficiency_metrics: calculate_efficiency_metrics,
      quality_metrics: calculate_quality_metrics
    }
  end

  private

  def parse_date_range(range)
    case range
    when "7d"
      [7.days.ago, Date.current]
    when "30d"
      [30.days.ago, Date.current]
    when "90d"
      [90.days.ago, Date.current]
    when "1y"
      [1.year.ago, Date.current]
    else
      [30.days.ago, Date.current]
    end
  end

  def calculate_growth_percentage(current, previous)
    return 0 if previous == 0
    ((current - previous) / previous.to_f * 100).round(2)
  end

  def calculate_profile_completion
    completion_score = 0
    total_fields = 10
    
    completion_score += 1 if @target_user.first_name.present?
    completion_score += 1 if @target_user.last_name.present?
    completion_score += 1 if @target_user.email.present?
    completion_score += 1 if @target_user.store.present?
    completion_score += 1 if @target_user.region.present?
    # Add more fields as needed
    
    (completion_score.to_f / total_fields * 100).round(1)
  end

  def calculate_login_activity
    recent_logins = @target_user.sign_in_count || 0
    last_login = @target_user.last_sign_in_at
    
    # Calculate login frequency
    days_since_registration = (Date.current - @target_user.created_at.to_date).to_i
    login_frequency = days_since_registration > 0 ? (recent_logins.to_f / days_since_registration).round(2) : 0
    
    {
      total_logins: recent_logins,
      last_login: last_login,
      login_frequency: login_frequency,
      days_since_last_login: last_login ? (Date.current - last_login.to_date).to_i : nil,
      login_streak: calculate_login_streak
    }
  end

  def calculate_sales_activity_patterns
    sales = @target_user.sales.where(created_at: @start_date..@end_date)
    
    {
      total_submissions: sales.count,
      peak_day: sales.group_by_day_of_week(:created_at).count.max_by(&:last)&.first,
      peak_hour: sales.group_by_hour_of_day(:created_at).count.max_by(&:last)&.first,
      average_daily_sales: (sales.count.to_f / (@end_date - @start_date).to_i).round(2),
      most_productive_day: find_most_productive_day(sales)
    }
  end

  def calculate_engagement_metrics
    # This would include various engagement indicators
    {
      profile_views: 0, # Would need to track this
      help_requests: 0, # Would need to track this
      training_completions: 0, # Would need to track this
      feedback_submissions: 0 # Would need to track this
    }
  end

  def calculate_activity_score(login_activity, sales_activity, engagement_metrics)
    # Weighted scoring system
    login_score = [login_activity[:login_frequency] * 20, 40].min
    sales_score = [sales_activity[:average_daily_sales] * 10, 40].min
    engagement_score = 20 # Base score, would be calculated from actual engagement data
    
    (login_score + sales_score + engagement_score).round(1)
  end

  def get_user_goal(goal_type)
    # In a real implementation, this would fetch from user preferences or admin settings
    case goal_type
    when :monthly_sales
      case @target_user.role
      when 'regular_user' then 10
      when 'region_admin' then 50
      else 25
      end
    when :monthly_points
      case @target_user.role
      when 'regular_user' then 1000
      when 'region_admin' then 5000
      else 2500
      end
    when :quarterly_sales
      get_user_goal(:monthly_sales) * 3
    else
      nil
    end
  end

  def goal_status(current, goal)
    progress = (current.to_f / goal * 100).round(1)
    
    case progress
    when 0...25 then "behind"
    when 25...75 then "on_track"
    when 75...100 then "ahead"
    else "exceeded"
    end
  end

  def calculate_trend_direction(values)
    return "stable" if values.length < 3
    
    first_half = values.first(values.length / 2).sum
    second_half = values.last(values.length / 2).sum
    
    if second_half > first_half * 1.1
      "increasing"
    elsif second_half < first_half * 0.9
      "decreasing"
    else
      "stable"
    end
  end

  def calculate_consistency_score(values)
    return 0 if values.empty?
    
    mean = values.sum.to_f / values.length
    return 100 if mean == 0
    
    variance = values.sum { |x| (x - mean) ** 2 } / values.length
    coefficient_of_variation = Math.sqrt(variance) / mean
    
    # Convert to a 0-100 score (lower CV = higher consistency)
    [100 - (coefficient_of_variation * 100), 0].max.round(1)
  end

  def can_view_comparative_data?
    @user.admin? || @user.super_admin? || @user == @target_user
  end

  # Additional helper methods would continue here...
  # For brevity, I'll include just the key structure
end
