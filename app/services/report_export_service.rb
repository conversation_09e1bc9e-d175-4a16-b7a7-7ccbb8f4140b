require 'csv'

class ReportExportService
  include ActionView::Helpers::NumberHelper
  include ActionView::Helpers::Date<PERSON><PERSON><PERSON>

  def initialize(report_type, data, options = {})
    @report_type = report_type
    @data = data
    @options = options.with_defaults({
      format: 'xlsx',
      include_charts: false,
      user: nil,
      date_range: nil
    })
  end

  def export
    case @options[:format].to_s.downcase
    when 'csv'
      export_csv
    when 'pdf'
      export_pdf
    when 'json'
      export_json
    when 'xlsx'
      export_xlsx
    else
      raise ArgumentError, "Unsupported format: #{@options[:format]}"
    end
  end

  def filename
    timestamp = Time.current.strftime("%Y%m%d_%H%M%S")
    "#{@report_type}_report_#{timestamp}.#{@options[:format]}"
  end

  private

  def export_csv
    CSV.generate(headers: true) do |csv|
      case @report_type
      when 'sales'
        export_sales_csv(csv)
      when 'orders'
        export_orders_csv(csv)
      when 'users'
        export_users_csv(csv)
      when 'performance'
        export_performance_csv(csv)
      else
        raise ArgumentError, "Unsupported report type for CSV: #{@report_type}"
      end
    end
  end

  def export_pdf
    # This would require a PDF generation library like Prawn or WickedPDF
    # For now, we'll create a simple HTML-to-PDF conversion
    html_content = generate_pdf_html
    
    # In a real implementation, you'd use something like:
    # WickedPdf.new.pdf_from_string(html_content)
    # or Prawn for more complex layouts
    
    html_content # Return HTML for now
  end

  def export_json
    {
      report_type: @report_type,
      generated_at: Time.current.iso8601,
      date_range: @options[:date_range],
      user: @options[:user]&.name,
      data: format_data_for_json,
      metadata: {
        total_records: @data.respond_to?(:count) ? @data.count : @data.size,
        export_format: 'json',
        version: '1.0'
      }
    }.to_json
  end

  def export_xlsx
    # This would use the existing XLSX views
    # Return a reference to the view template
    { template: "reports/#{@report_type}.xlsx.axlsx", data: @data }
  end

  def export_sales_csv(csv)
    # Header row
    csv << [
      'Sale ID',
      'Date',
      'User Name',
      'User Email',
      'Product Name',
      'Brand',
      'Region',
      'Points',
      'Status',
      'Approved Date',
      'Admin'
    ]

    # Data rows
    @data.each do |sale|
      csv << [
        sale.id,
        sale.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        sale.user.name,
        sale.user.email,
        sale.product.name,
        sale.brand.name,
        sale.region.name,
        sale.points,
        sale.status.humanize,
        sale.approved_at&.strftime('%Y-%m-%d %H:%M:%S'),
        sale.admin&.name
      ]
    end
  end

  def export_orders_csv(csv)
    # Header row
    csv << [
      'Order ID',
      'Date',
      'User Name',
      'User Email',
      'Brand',
      'Total Items',
      'Total Value',
      'Points Used',
      'Status',
      'Approved Date',
      'Shipped Date'
    ]

    # Data rows
    @data.each do |order|
      csv << [
        order.id,
        order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        order.user.name,
        order.user.email,
        order.brand.name,
        order.total_items,
        order.total_value,
        order.points,
        order.status.humanize,
        order.approved_at&.strftime('%Y-%m-%d %H:%M:%S'),
        order.shipped_at&.strftime('%Y-%m-%d %H:%M:%S')
      ]
    end
  end

  def export_users_csv(csv)
    # Header row
    csv << [
      'User ID',
      'Name',
      'Email',
      'Role',
      'Store',
      'Brand',
      'Region',
      'Registration Date',
      'Last Login',
      'Total Sales',
      'Total Points'
    ]

    # Data rows
    @data.each do |user|
      csv << [
        user.id,
        user.name,
        user.email,
        user.role.humanize,
        user.store&.name,
        user.store&.brand&.name,
        user.region&.name,
        user.created_at.strftime('%Y-%m-%d'),
        user.last_sign_in_at&.strftime('%Y-%m-%d %H:%M:%S'),
        user.sales.count,
        user.sales.approved.sum(:points)
      ]
    end
  end

  def export_performance_csv(csv)
    # Header row
    csv << [
      'Metric',
      'Value',
      'Period',
      'Change',
      'Trend'
    ]

    # Data rows - this would depend on the performance data structure
    if @data.is_a?(Hash)
      @data.each do |key, value|
        csv << [
          key.to_s.humanize,
          value.is_a?(Numeric) ? number_with_delimiter(value) : value.to_s,
          @options[:date_range] || 'Current',
          '', # Change calculation would go here
          '' # Trend calculation would go here
        ]
      end
    end
  end

  def generate_pdf_html
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>#{@report_type.humanize} Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
          .title { font-size: 24px; font-weight: bold; color: #333; }
          .subtitle { font-size: 14px; color: #666; margin-top: 5px; }
          .metadata { background: #f5f5f5; padding: 10px; margin: 20px 0; border-radius: 5px; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; font-weight: bold; }
          .summary { background: #e8f4fd; padding: 15px; margin: 20px 0; border-radius: 5px; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">#{@report_type.humanize} Report</div>
          <div class="subtitle">Generated on #{Time.current.strftime('%B %d, %Y at %I:%M %p')}</div>
        </div>

        <div class="metadata">
          <strong>Report Details:</strong><br>
          Type: #{@report_type.humanize}<br>
          Date Range: #{@options[:date_range] || 'All time'}<br>
          Generated by: #{@options[:user]&.name || 'System'}<br>
          Total Records: #{@data.respond_to?(:count) ? @data.count : @data.size}
        </div>

        #{generate_pdf_content}

        <div class="footer">
          This report was generated automatically by the ZeissPoints system.<br>
          For questions or support, please contact your system administrator.
        </div>
      </body>
      </html>
    HTML
  end

  def generate_pdf_content
    case @report_type
    when 'sales'
      generate_sales_pdf_content
    when 'orders'
      generate_orders_pdf_content
    when 'users'
      generate_users_pdf_content
    else
      "<p>Content for #{@report_type} reports is not yet implemented.</p>"
    end
  end

  def generate_sales_pdf_content
    total_sales = @data.count
    approved_sales = @data.select { |s| s.status == 'approved' }.count
    total_points = @data.select { |s| s.status == 'approved' }.sum(&:points)

    content = <<~HTML
      <div class="summary">
        <h3>Summary</h3>
        <p><strong>Total Sales:</strong> #{number_with_delimiter(total_sales)}</p>
        <p><strong>Approved Sales:</strong> #{number_with_delimiter(approved_sales)}</p>
        <p><strong>Total Points:</strong> #{number_with_delimiter(total_points)}</p>
        <p><strong>Approval Rate:</strong> #{total_sales > 0 ? ((approved_sales.to_f / total_sales) * 100).round(1) : 0}%</p>
      </div>

      <h3>Sales Details</h3>
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>User</th>
            <th>Product</th>
            <th>Points</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
    HTML

    @data.limit(100).each do |sale| # Limit for PDF performance
      content += <<~HTML
        <tr>
          <td>#{sale.created_at.strftime('%m/%d/%Y')}</td>
          <td>#{sale.user.name}</td>
          <td>#{sale.product.name}</td>
          <td>#{sale.points}</td>
          <td>#{sale.status.humanize}</td>
        </tr>
      HTML
    end

    content += "</tbody></table>"
    
    if @data.count > 100
      content += "<p><em>Note: Only the first 100 records are shown in this PDF. Download the full CSV or Excel report for complete data.</em></p>"
    end

    content
  end

  def generate_orders_pdf_content
    # Similar implementation for orders
    "<p>Orders PDF content would be generated here.</p>"
  end

  def generate_users_pdf_content
    # Similar implementation for users
    "<p>Users PDF content would be generated here.</p>"
  end

  def format_data_for_json
    case @report_type
    when 'sales'
      @data.map do |sale|
        {
          id: sale.id,
          date: sale.created_at.iso8601,
          user: {
            id: sale.user.id,
            name: sale.user.name,
            email: sale.user.email
          },
          product: {
            id: sale.product.id,
            name: sale.product.name
          },
          brand: sale.brand.name,
          region: sale.region.name,
          points: sale.points,
          status: sale.status,
          approved_at: sale.approved_at&.iso8601
        }
      end
    when 'orders'
      @data.map do |order|
        {
          id: order.id,
          date: order.created_at.iso8601,
          user: {
            id: order.user.id,
            name: order.user.name,
            email: order.user.email
          },
          brand: order.brand.name,
          total_items: order.total_items,
          total_value: order.total_value,
          points: order.points,
          status: order.status
        }
      end
    else
      @data.as_json
    end
  end
end
