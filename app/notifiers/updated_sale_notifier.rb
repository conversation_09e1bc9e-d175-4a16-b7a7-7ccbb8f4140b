# To deliver this notification:
#
# UpdatedSaleNotification.with(post: @post).deliver_later(current_user)
# UpdatedSaleNotification.with(post: @post).deliver(current_user)

class UpdatedSaleNotifier < Noticed::Event
  # Add your delivery methods
  #
  deliver_by :email do |config|
    config.mailer = "SaleMailer"
    config.method = :updated_sale_notification
    config.if = :email_notification?
  end
  # deliver_by :slack
  # deliver_by :custom, class: "MyDeliveryMethod"

  # Add required params
  #
  required_param :sale

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  # def url
  #   post_path(params[:post])
  # end

  def email_notification?(notification)
    notification.recipient.mail_sale_updated
  end
end
