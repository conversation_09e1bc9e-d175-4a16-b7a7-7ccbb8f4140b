# To deliver this notification:
#
# UpdatedOrderNotification.with(post: @post).deliver_later(current_user)
# UpdatedOrderNotification.with(post: @post).deliver(current_user)

class UpdatedOrderNotifier < Noticed::Event
  # Add your delivery methods
  #
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :updated_order_notification
    config.if = :email_notification?
  end
  # deliver_by :slack
  # deliver_by :custom, class: "MyDeliveryMethod"

  # Add required params
  #
  required_param :order

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  # def url
  #   post_path(params[:post])
  # end

  def email_notification?(notification)
    notification.recipient.mail_order_updated
  end
end
