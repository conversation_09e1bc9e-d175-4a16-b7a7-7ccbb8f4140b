# To deliver this notification:
#
# StartingPromotion.with(post: @post).deliver_later(current_user)
# StartingPromotion.with(post: @post).deliver(current_user)

class StartingPromotionNotifier < Noticed::Event
  # Add your delivery methods
  #
  deliver_by :email, mailer: "PromotionMailer", if: :email_notification?
  deliver_by :twilio, format: :sms_format, if: :sms_notification?

  # Add required params
  #
  required_param :promotion

  # Define helper methods to make rendering easier.
  #
  def message
    t(".message", multiplier: promotion.multiplier, product_name: promotion.products.map(&:name).join(", "))
  end
  #
  # def url
  #   post_path(params[:post])
  # end

  def sms_format
    {
      To: recipient.phone_number,
      From: Rails.application.credentials.twilio.fetch(:phone_number),
      Body: message,
      MessagingServiceSid: Rails.application.credentials.twilio.fetch(:message_service_sid)
    }
  end

  def email_notification?
    recipient.mail_promotion
  end

  def sms_notification?
    recipient.sms_promotion
  end
end
