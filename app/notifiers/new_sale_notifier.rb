# To deliver this notification:
#
# NewSaleNotification.with(post: @post).deliver_later(current_user)
# NewSaleNotification.with(post: @post).deliver(current_user)

class NewSaleNotifier < Noticed::Event
  # Add your delivery methods
  #
  deliver_by :email do |config|
    config.mailer = "SaleMailer"
    config.method = :new_sale_notification
    config.if = :email_notification?
  end
  # deliver_by :slack
  # deliver_by :custom, class: "MyDeliveryMethod"

  # Add required params
  #
  required_param :sale

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  def url
    sale_path(params[:sale])
  end

  def email_notification?(notification)
    recipient = notification.recipient

    return false unless params[:sale].user.address.country == recipient.address.country

    # User wants to receive every email
    return false unless recipient.every?

    # User wants to receive new sale notifications
    return false unless recipient.mail_sale_new

    # User is a brand admin and is part of the same brand as the sale
    return true unless recipient.admin_brand.present?
    return false unless correct_brand

    # User is a region admin and the sale is from their region
    return true unless recipient.admin_region.present?
    correct_region
  end

  private

  def correct_brand
    params[:sale].brand == recipient.admin_brand
  end

  def correct_region
    params[:sale].region == recipient.admin_region
  end
end
