# To deliver this notification:
#
# NewStoreRequestNotification.with(post: @post).deliver_later(current_user)
# NewStoreRequestNotification.with(post: @post).deliver(current_user)

class NewStoreRequestNotifier < Noticed::Event
  # Add your delivery methods
  #
  # deliver_by :database
  deliver_by :email do |config|
    config.mailer = "StoreRequestMailer"
    config.method = :new_store_request_notification
    config.if = :email_notification?
  end
  # deliver_by :slack
  # deliver_by :custom, class: "MyDeliveryMethod"

  # Add required params
  #
  required_param :request

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  # def url
  #   post_path(params[:post])
  # end

  def email_notification?(notification)
    recipient = notification.recipient
    
    return false unless params[:request].address.country == recipient.address.country
    
    notification.recipient.mail_store_request_new
  end
end
