# To deliver this notification:
#
# Promotion.with(post: @post).deliver_later(current_user)
# Promotion.with(post: @post).deliver(current_user)

class UpcomingPromotionNotifier < Noticed::Event
  # Add your delivery methods
  #
  deliver_by :email, mailer: "PromotionMailer", if: :email_notification?
  deliver_by :twilio, format: :sms_format, if: :sms_notification?

  # Add required params
  #
  required_param :promotion

  # Define helper methods to make rendering easier.
  #
  def message
    t(".message", start: promotion.starts_at, product_name: promotion.products.map(&:name).join(", "), multiplier: promotion.multiplier)
  end
  #
  # def url
  #   post_path(params[:post])
  # end

  def sms_format
    {
      To: recipient.phone_number,
      From: Rails.application.credentials.twilio.fetch(:phone_number),
      Body: message,
      MessagingServiceSid: Rails.application.credentials.twilio.fetch(:message_service_sid)
    }
  end

  def email_notification?
    recipient.mail_promotion
  end

  def sms_notification?
    recipient.sms_promotion
  end
end
