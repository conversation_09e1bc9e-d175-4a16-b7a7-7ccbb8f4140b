# To deliver this notification:
#
# NewUserNotification.with(post: @post).deliver_later(current_user)
# NewUserNotification.with(post: @post).deliver(current_user)

class NewUserNotifier < Noticed::Event
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :new_user_notification
    config.if = :email_notification?
  end

  required_param :user

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  def url
    user_url(params[:user])
  end

  def email_notification?(notification)
    recipient = notification.recipient
    
    return false unless params[:user].address.country == recipient.address.country
    
    recipient.mail_user_new
  end
end
