# To deliver this notification:
#
# ApprovedUserNotification.with(post: @post).deliver_later(current_user)
# ApprovedUserNotification.with(post: @post).deliver(current_user)

class ApprovedUserNotifier < Noticed::Event
  # Add your delivery methods
  #
  # deliver_by :database
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :approved_user_notification
  end
  # deliver_by :slack
  # deliver_by :custom, class: "MyDeliveryMethod"

  # Add required params
  #
  # param :post

  # Define helper methods to make rendering easier.
  #
  # def message
  #   t(".message")
  # end
  #
  # def url
  #   post_path(params[:post])
  # end
end
