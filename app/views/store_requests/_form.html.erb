<%= form_with(model: store_request, data: {turbo_frame: "_top"}) do |form| %>
  <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
    <div>
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          New Store Request
        </h3>
        <p class="max-w-2xl mt-1 text-sm text-gray-500">
          Please complete the form below and someone will assist you.
        </p>
      </div>
      <%= render "shared/error_messages", resource: form.object %>
      <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :name, autocomplete: "name", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "<PERSON>" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :email, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :email, autocomplete: "email", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "<EMAIL>" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :store_name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :store_name, autocomplete: "store-name", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "Joe's Hardware" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :brand_id, "What Zeiss products does your store primarily sell?", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <%= form.collection_select :brand_id, Brand.all, :id, :name, {include_blank: 'Please Select'}, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :street, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :street, autocomplete: "address", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "123 Somewhere St" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :city, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :city, autocomplete: "city", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "Smalltown" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :state_id, "State", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.collection_select :state_id, State.order(:name), :id, :name, {include_blank: "Select State"}, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :zip, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :zip, autocomplete: "zipcode", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "12324" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :manager_name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :manager_name, autocomplete: "manager-name", class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", placeholder: "Jill Smith" %>
          </div>
        </div>
        <% if user_signed_in? && allowed_to?(:edit?, store_request) %>
          <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
            <%= form.label :status, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
            <%= form.select :status, options_for_select(StoreRequest.statuses.keys.map {|s| [s.titleize, s]}), {}, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <div class="pt-5">
    <div class="flex justify-end">
      <%= link_to "Cancel", root_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <% if form.object.persisted? && allowed_to?(:destroy?, form.object) %>
        <%= link_to "Delete", form.object, class: "bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs text-sm font-medium text-red-700 hover:bg-red-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ml-3", data: { confirm: "Are you sure?", turbo_method: :delete } %>
      <% end %>
    </div>
  </div>
<% end %>
