<%= component "page" do |page| %>
  <% page.with_header title: "Store Requests" %>
  <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
      <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full">
          <thead class="bg-white">
            <tr>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Store name</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">City</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">State</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Manager</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Status</th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"></th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @requests.each do |request| %>
              <tr>
                <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6"><%= request.name %></td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= request.store_name %></td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= request.city %></td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= request.state.name %></td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= request.manager_name %></td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= request.status.titleize %></td>
                <td class="flex justify-end px-6 py-4 space-x-2 text-sm">
                  <%= link_to icon(name: "eye", class: "w-5 h-5"), request, class: "text-zeiss-600 hover:text-zeiss-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="px-4 py-4 sm:px-6"><!-- Card Footer -->
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
