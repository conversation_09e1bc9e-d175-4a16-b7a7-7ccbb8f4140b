<%= component "page" do |page| %>
  <% page.with_header title: "Store Request" do |header| %>
    <% header.with_actions([
      { title: "Edit", url: edit_store_request_path(@request), primary: true, visible: allowed_to?(:edit?, @request) }
    ]) %>
  <% end %>
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
    <div>
      <%= component "card" do |card| %>
        <% card.with_header title: "Requester" %>
        <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Name</dt>
            <dl><%= @request.name %></dl>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Email</dt>
            <dl><%= @request.email %></dl>
          </div>
        </dl>
      <% end %>
    </div>
    <div>
      <%= component "card" do |card| %>
        <% card.with_header title: "Store" %>
        <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-gray-500">Name</dt>
            <dl><%= @request.store_name %></dl>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Address</dt>
            <dl>
              <address>
                <%= @request.street %><br/>
                <%= @request.city %>, <%= @request.state.name %> <%= @request.zip %>
              </address>
            </dl>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Brand</dt>
            <dl><%= @request.brand.name %></dl>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Manager</dt>
            <dl><%= @request.manager_name %></dl>
          </div>
        </dl>
      <% end %>
    </div>
    <div class="col-span-2">
      <div class="pb-5 border-b border-gray-200">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Similar Stores</h3>
      </div>
      <div class="flex flex-col mt-8">
        <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="min-w-full">
                <thead class="bg-white">
                  <tr>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                    <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">Region</th>
                    <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Brand</th>
                    <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">Chain</th>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Users</th>
                  </tr>
                </thead>
                <tbody class="bg-white">
                  <% @stores.each do |store| %>
                    <tr class="border-t border-gray-300">
                      <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                        <%= link_to store.name, store %>
                        <div class="text-gray-500">
                          <%= store.street %>, <%= store.city %>, <%= store.state_name %> <%= store.zip %>
                        </div>
                        <dl class="font-normal lg:hidden">
                          <dt class="sr-only">Region</dt>
                          <dd class="mt-1 text-gray-500 truncate"><%= store.region_name %></dd>
                          <dt class="sr-only md:hidden">Brand</dt>
                          <dd class="mt-1 text-gray-500 truncate md:hidden"><%= store.brand_name %></dd>
                          <dt class="sr-only sm:hidden">Chain</dt>
                          <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= store.store_chain_name.presence %></dd>
                        </dl>
                      </td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= store.region_name %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= store.brand_name %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= store.store_chain_name.presence %></td>
                      <td class="px-3 py-4 text-sm text-center text-gray-500 whitespace-nowrap"><%= store.users_count %></td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
