<%= render Page::Tabbed::Component.new do |page| %>
  <% page.header(title: resource.name, tabs: true) %>
  <% page.tab(title: "Profile") do %>
    <%= form_with model: resource, as: resource_name, url: registration_path(resource), html: {method: :put} do |f| %>
      <div class="shadow-sm sm:rounded-md sm:overflow-hidden">
        <div class="px-4 py-6 space-y-6 bg-white sm:p-6">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">Profile</h3>
          </div>
  <%= render "shared/error_messages", resource: form.object %>
          <div class="grid grid-cols-6 gap-6">
            <div class="col-span-6 sm:col-span-3">
              <%= f.label :first_name, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :first_name, autocomplete: "given-name", placeholder: "<PERSON>", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-3">
              <%= f.label :last_name, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :last_name, autocomplete: "family-name", placeholder: "Jobs", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-4">
              <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
              <%= f.email_field :email, autocomplete: "email", placeholder: "<EMAIL>", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
                <p class="mt-2 text-sm text-gray-500"><%= t('.currently_waiting_confirmation_for_email', email: resource.unconfirmed_email) %></p>
              <% end %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= f.label :phone_number, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :phone_number, autocomplete: "tel", placeholder: "************", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6">
              <%= f.label :street, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :street, autocomplete: "street-address", placeholder: "555 Main St.", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= f.label :city, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :city, autocomplete: "address-level2", placeholder: "San Francisco", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= f.label :state_id, class: "block text-sm font-medium text-gray-700" %>
              <%= f.collection_select :state_id, State.order(:name), :id, :name, {}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= f.label :zip, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :zip, autocomplete: "postal-code", placeholder: "94103", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= f.label :store, class: "block text-sm font-medium text-gray-700" %>
              <%= resource.store.name.presence %>
            </div>
            <div class="col-span-6 sm:col-span-4">
              <div class="grid grid-cols-7 gap-4" data-controller="upload" data-upload-aspect-value="1">
                <div class="col-span-7 md:col-span-1">
                  <%= f.label :avatar, class: "block text-sm font-medium text-gray-700" %>
                  <div class="inline-block w-12 h-12 overflow-hidden bg-gray-100 rounded-full">
                    <%= image_tag resource.avatar_path, size: "200x200", data: {upload_target: "preview"} %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="px-4 py-3 text-right bg-gray-50 sm:px-6">
          <%= f.submit "Update Profile", class: "bg-zeiss-600 border border-transparent rounded-md shadow-xs py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    <% end %>
    <div class="flex items-center justify-center mt-6">
      <div class="border border-red-500 shadow-sm bg-red-50 sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Delete Your Account</h3>
          <p class="prose-sm prose text-gray-500">Once you delete your account, you will lose all information, including unused points.</p>
          <div class="mt-5">
            <%= button_to "Cancel My Account", registration_path(resource_name), data: {turbo_confirm: "Are you sure?", turbo_method: :delete}, class: "inline-flex items-center justify-center px-4 py-2 border border-transparent font-medium rounded-md text-red-700 bg-red-200 hover:bg-red-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <% page.tab(title: "Password") do %>
    <%= form_with(model: current_user, url: account_password_path, html: { method: :put }) do |f| %>
      <div class="shadow-sm sm:rounded-md sm:overflow-hidden">
        <div class="px-4 py-6 space-y-6 bg-white sm:p-6">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">Update Password</h3>
          </div>
  <%= render "shared/error_messages", resource: form.object %>
          <div class="grid grid-cols-6 gap-6">
            <div class="col-span-6">
              <%= f.label :current_password, class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :current_password, autocomplete: "current-password", placeholder: "Current password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              <p class="mt-3 text-sm text-gray-600">We need your current password to confirm your changes</p>
            </div>
            <div class="col-span-6 md:col-span-3">
              <%= f.label :password, "New password", class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :password, autocomplete: "new-password", placeholder: "New password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              <% if @minimum_password_length %>
                <p class="mt-3 text-sm text-gray-600"><%= @minimum_password_length %> characters minimum</p>
              <% end %>
            </div>
            <div class="col-span-6 md:col-span-3">
              <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :password_confirmation, autocomplete: "new-password", placeholder: "Confirm new password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
          </div>
        </div>
        <div class="px-4 py-3 text-right bg-gray-50 sm:px-6">
          <%= f.submit "Update Password", class: "bg-zeiss-600 border border-transparent rounded-md shadow-xs py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    <% end %>
  <% end %>
  <% page.tab(title: "Notifications") do %>
    <%= form_with(model: current_user, url: account_notifications_path, local: true, html: { method: :put }) do |f| %>
      <div class="shadow-sm sm:rounded-md sm:overflow-hidden">
        <div class="px-4 py-6 space-y-6 bg-white sm:p-6">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">Update Notification Settings</h3>
          </div>
          <div class="flex w-1/2 mx-auto">
            <div class="grid grid-cols-[max-content_max-content_minmax(0,1fr)] gap-4">
              <div x-tooltip="Email">
                <%= icon name: "envelope", class: "h-5 w-5 text-gray-700" %>
                <span class="sr-only">Email</span>
              </div>
              <div x-tooltip="SMS">
                <%= icon name: "comment-sms", class: "h-5 w-5 text-gray-700" %>
                <span class="sr-only">SMS</span>
              </div>
              <div></div>
              <% if current_user.admin? || current_user.super_admin? %>
                <div>
                  <%= f.check_box :mail_user_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                </div>
                <div><!-- SMS New User --></div>
                <div>
                  <div class="ml-3 text-sm">
                    <%= f.label :mail_user_new, "New User", class: "font-medium text-gray-700" %>
                    <p class="text-gray-500">Get notified when someone new signs up.</p>
                  </div>
                </div>
                <div>
                  <%= f.check_box :mail_order_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                </div>
                <div><!-- SMS New Order --></div>
                <div>
                  <div class="ml-3 text-sm">
                    <%= f.label :mail_order_new, "New Order", class: "font-medium text-gray-700" %>
                    <p class="text-gray-500">Get notified when a new order is placed.</p>
                  </div>
                </div>
                <div>
                  <%= f.check_box :mail_sale_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                </div>
                <div><!-- SMS New Sale --></div>
                <div>
                  <div class="ml-3 text-sm">
                    <%= f.label :mail_sale_new, "New Sale", class: "font-medium text-gray-700" %>
                    <p class="text-gray-500">Get notified when a new sale is created.</p>
                  </div>
                </div>
                <div>
                  <%= f.check_box :mail_store_request_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                </div>
                <div><!-- SMS New Store Request --></div>
                <div>
                  <div class="ml-3 text-sm">
                    <%= f.label :mail_store_request_new, "New Store Request", class: "font-medium text-gray-700" %>
                    <p class="text-gray-500">Get notified when someone requests a new store.</p>
                  </div>
                </div>
              <% end %>
              <div>
                <%= f.check_box :mail_sale_updated, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              </div>
              <div><!-- SMS Updated Sale --></div>
              <div>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_sale_updated, "Updated Sale", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when a sale you created is updated.</p>
                </div>
              </div>
              <div>
                <%= f.check_box :mail_order_updated, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              </div>
              <div><!-- SMS Updated Order --></div>
              <div>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_order_updated, "Updated Order", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when an order you placed is updated.</p>
                </div>
              </div>
              <div>
                <%= f.check_box :mail_promotion, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              </div>
              <div>
                <%= f.check_box :sms_promotion, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              </div>
              <div>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_promotion, "Promotions", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified about upcoming and ending promotions.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="px-4 py-3 text-right bg-gray-50 sm:px-6">
          <%= f.submit "Update", class: "bg-zeiss-600 border border-transparent rounded-md shadow-xs py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    <% end %>
  <% end %>
<% end %>
