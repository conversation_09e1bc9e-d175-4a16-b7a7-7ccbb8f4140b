<div class="lg:grid lg:grid-cols-12 lg:gap-x-5">
  <aside class="px-2 py-6 lg:col-span-3 sm:px-6 lg:py-0 lg:px-0">
    <%= render partial: "shared/account_navbar" %>
  </aside>
  <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
    <%= form_with(model: current_user, url: account_notifications_path, local: true, html: { method: :put }) do |f| %>
      <div class="shadow-sm sm:rounded-md sm:overflow-hidden">
        <div class="px-4 py-6 space-y-6 bg-white sm:p-6">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">Update Notification Settings</h3>
          </div>
          <%= render "shared/error_messages", resource: current_user %>
          <fieldset class="space-y-5">
            <legend class="sr-only">Notifications</legend>
            <% if current_user.admin? %>
              <div class="relative flex items-start">
                <%= f.check_box :mail_user_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_user_new, "New User", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when someone new signs up.</p>
                </div>
              </div>
              <div class="relative flex items-start">
                <%= f.check_box :mail_order_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_order_new, "New Order", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when a new order is placed.</p>
                </div>
              </div>
              <div class="relative flex items-start">
                <%= f.check_box :mail_sale_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_sale_new, "New Sale", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when a new sale is created.</p>
                </div>
              </div>
              <div class="relative flex items-start">
                <%= f.check_box :mail_store_request_new, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
                <div class="ml-3 text-sm">
                  <%= f.label :mail_store_request_new, "New Store Request", class: "font-medium text-gray-700" %>
                  <p class="text-gray-500">Get notified when someone requests a new store.</p>
                </div>
              </div>
            <% end %>
            <div class="relative flex items-start">
              <%= f.check_box :mail_sale_updated, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              <div class="ml-3 text-sm">
                <%= f.label :mail_sale_updated, "Updated Sale", class: "font-medium text-gray-700" %>
                <p class="text-gray-500">Get notified when a sale you created is updated.</p>
              </div>
            </div>
            <div class="relative flex items-start">
              <%= f.check_box :mail_order_updated, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              <div class="ml-3 text-sm">
                <%= f.label :mail_order_updated, "Updated Order", class: "font-medium text-gray-700" %>
                <p class="text-gray-500">Get notified when an order you placed is updated.</p>
              </div>
            </div>
          </fieldset>
          <div class="grid w-1/2 grid-cols-6 gap-6">
            <div class="col-span-6">
              <%= f.label :current_password, class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :current_password, autocomplete: "current-password", placeholder: "Current password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              <p class="mt-3 text-sm text-gray-600">We need your current password to confirm your changes</p>
            </div>
          </div>
        </div>
        <div class="px-4 py-3 text-right bg-gray-50 sm:px-6">
          <%= f.submit "Update", class: "bg-zeiss-600 border border-transparent rounded-md shadow-xs py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
