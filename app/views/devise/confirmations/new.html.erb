<main>
  <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
    <%= form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post }) do |f| %>
      <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900"><%= t('.resend_confirmation_instructions') %></h3>
        </div>
        <div class="px-4 py-5 sm:px-6">
          <%= render "shared/error_messages", resource: resource %>
          <div class="">
            <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
            <div class="relative mt-1 rounded-md shadow-xs">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <%= icon name: "envelope", class: "text-gray-400" %>
              </div>
              <%= f.email_field :email, autofocus: true, autocomplete: "email", value: (resource.pending_reconfirmation? ? resource.unconfirmed_email : resource.email), placeholder: true, class: "focus:ring-zeiss-500 focus:border-zeiss-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
        </div>
        <div class="px-4 py-5 sm:px-6">
          <div class="grid grid-cols-2">
            <div class="text-sm">
              <%= link_to "Forgot your password?", new_password_path(resource_name), class: "font-medium text-zeiss-600 hover:text-zeiss-500" %>
            </div>
            <%= f.submit "Resend Instructions", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</main>
