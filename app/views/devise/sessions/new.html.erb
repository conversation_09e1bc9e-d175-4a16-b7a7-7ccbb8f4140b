
<div>
  <%= image_tag "zeisslogo.png", class: "h-12 w-auto", alt: "ZEISS Points Program" %>
  <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
</div>
<div class="mt-8">
  <%= form_with model: resource, as: resource_name, url: session_path(resource_name), class: "space-y-6", data: {turbo_frame: "_top"} do |f| %>
    <div>
      <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= f.email_field :email, class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", required: true, autocomplete: "email" %>
      </div>
    </div>
    <div>
      <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= f.password_field :password, class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", required: true, autocomplete: "current-password" %>
      </div>
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <%= f.check_box :remember_me, class: "h-4 w-4 text-zeiss-600 focus:ring-zeiss-500 border-gray-300 rounded-sm" %>
        <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-900" %>
      </div>
      <div class="text-sm">
        <%= link_to "Forgot your password?", new_password_path(resource_name), class: "font-medium text-zeiss-600 hover:text-zeiss-500", data: { turbo_frame: "_top" } %>
      </div>
    </div>
    <div>
      <%= f.submit "Sign in", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-xs text-sm font-medium text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
    </div>
  <% end %>
</div>
