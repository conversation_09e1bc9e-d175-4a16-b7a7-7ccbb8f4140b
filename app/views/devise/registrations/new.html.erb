<div class="overflow-scroll bg-white sm:rounded-lg shadow-sm max-h-full sm:max-h-[46rem]">
  <div class="px-4 py-5 sm:p-6">
    <div class="pb-5 mb-2 border-b border-gray-200">
      <h3 class="text-base font-semibold leading-6 text-gray-900">Account Registration</h3>
    </div>
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |form| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      <div class="space-y-12">
        <!-- Profile -->
        <div class="pb-12 border-b border-gray-900/10">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Profile</h2>
          <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="col-span-full">
              <%= form.label :email, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.email_field :email, autofocus: true, autocomplete: "email", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
            <div class="sm:col-span-3">
              <%= form.label :password, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.password_field :password, autocomplete: "new-password", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
              </div>
              <% if @minimum_password_length %>
                <p class="mt-2 text-sm text-gray-500">(<%= @minimum_password_length %> characters minimum)</p>
              <% end %>
            </div>
            <div class="sm:col-span-3">
              <%= form.label :password_confirmation, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.password_field :password_confirmation, autocomplete: "new-password", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>
        <!-- Personal -->
        <div class="pb-12 border-b border-gray-900/50">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Personal</h2>
          <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <%= form.label :first_name, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.text_field :first_name, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
            <div class="sm:col-span-3">
              <%= form.label :last_name, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.text_field :last_name, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
            <%= form.fields_for :address do |address| %>
              <div class="col-span-full">
                <%= address.label :line1, "Street address", class: "block text-sm font-medium leading-6 text-gray-900" %>
                <div class="mt-1">
                  <%= address.text_field :line1, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
                </div>
              </div>
              <div class="col-span-full">
                <%= address.label :line2, "Unit / Apartment", class: "block text-sm font-medium leading-6 text-gray-900" %>
                <div class="mt-1">
                  <%= address.text_field :line2, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
                </div>
              </div>
              <div class="sm:col-span-2 sm:col-start-1">
                <%= address.label :city, class: "block text-sm font-medium leading-6 text-gray-900" %>
                <div class="mt-1">
                  <%= address.text_field :city, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
                </div>
              </div>
              <div class="sm:col-span-2">
                <%= address.label :state_id, "State / Province", class: "block text-sm font-medium leading-6 text-gray-900" %>
                <div class="mt-1">
                  <%= address.collection_select :state_id, @states, :id, :name, {}, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
                </div>
              </div>
              <div class="sm:col-span-2">
                <%= address.label :zip_code, class: "block text-sm font-medium leading-6 text-gray-900" %>
                <div class="mt-1">
                  <%= address.text_field :zip_code, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6" %>
                </div>
              </div>
              <%= address.hidden_field :country_id, value: Current.country&.id %>
            <% end %>
            <div>
              <%= form.label :phone_number, class: "block text-sm font-medium leading-6 text-gray-900" %>
              <div class="mt-1">
                <%= form.text_field :phone_number, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-zeiss-600 sm:text-sm sm:leading-6", "x-mask": "(*************" %>
              </div>
            </div>
          </div>
        </div>
        <div class="pb-12 border-b border-gray-900/50">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Store</h2>
          <fieldset x-radio x-data="{value: ''}" x-model="value" name="user[store_id]">
            <div class="max-w-6xl overflow-x-scroll">
              <div class="grid grid-rows-4 gap-4 pb-4 sm:grid-flow-col auto-cols-max">
                <% @stores.each do |store| %>
                  <div class="relative block max-w-xs px-6 py-4 bg-white border rounded-lg shadow-xs cursor-pointer sm:max-w-sm focus:outline-hidden sm:flex sm:justify-between" :class="{'border-gray-300': !$radioOption.isActive, 'border-zeiss-500 ring-2 ring-zeiss-500': $radioOption.isActive}" x-radio:option value="<%= store.id %>">
                    <span class="flex items-center">
                      <span class="flex flex-col text-sm">
                        <span class="font-medium text-gray-900" x-radio:label><%= store.name %></span>
                        <span class="text-gray-500" x-radio:description>
                          <%= store.address.full_address %>
                        </span>
                      </span>
                    </span>
                    <span class="absolute rounded-lg pointer-events-none border-zeiss-500 -inset-px" aria-hidden="true" :class="{ 'border': $radioOption.isActive, 'border-2': !$radioOption.isActive, 'border-zeiss-500': $radioOption.isChecked, 'border-transparent': !$radioOption.isChecked }"></span>
                  </div>
                <% end %>
              </div>
            </div>
          </fieldset>
          <p class="prose-sm prose">Don't see your store? Just <%= link_to "request it", new_store_path %> and we'll look into it.</p>
        </div>
      </div>
      <div class="flex items-center justify-end mt-6 gap-x-6">
        <%= link_to "Cancel", root_path, class: "text-sm font-semibold leading-6 text-gray-900" %>
        <%= form.submit "Sign Up", class: "rounded-md bg-zeiss-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-zeiss-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-zeiss-600" %>
      </div>
    <% end %>
  </div>
</div>
