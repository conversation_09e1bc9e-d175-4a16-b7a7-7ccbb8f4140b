<main>
  <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
    <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
      <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Change your password</h3>
        </div>
        <div class="px-4 py-5 sm:px-6">
          <%= render "shared/error_messages", resource: resource %>
          <div>
            <%= f.hidden_field :reset_password_token %>
            <div class="grid grid-cols-6 gap-6">
              <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
              <div class="col-span-6 md:col-span-3">
                <%= f.label :password, "New password", class: "block text-sm font-medium text-gray-700" %>
                <%= f.password_field :password, autocomplete: "new-password", placeholder: "New password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
                <% if @minimum_password_length %>
                  <p class="mt-3 text-sm text-gray-600"><%= @minimum_password_length %> characters minimum</p>
                <% end %>
              </div>
              <div class="col-span-6 md:col-span-3">
                <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700" %>
                <%= f.password_field :password_confirmation, autocomplete: "new-password", placeholder: "Confirm new password", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
            </div>
          </div>
        </div>
        <div class="px-4 py-5 sm:px-6">
          <div class="grid grid-cols-2">
            <%= f.submit "Change password", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</main>
