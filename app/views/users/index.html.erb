<%= component "page" do |page| %>
  <% page.with_header(title: "Users") do |header| %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_link(name: "Export (.xlsx)", url: users_path(format: :xlsx), icon: "file-spreadsheet", visible: allowed_to?(:export?, User)) %>
    <% end %>
  <% end %>
  <%= component "page/filter", name: "Users", sort_options: [{label: "Role", option: "role"}, {label: "State", option: "state"}, {label: "Status", option: "status"}, {label: "Store", option: "store"}] do |filter| %>
    <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :users, filter_field: "brand_name") %>
    <% filter.with_filter_collection(name: "Status", collection: User.statuses, name_method: :first, value_method: :first, klass: :users, filter_field: "status") %>
    <% filter.with_filter_collection(name: "Country", collection: Country.all, name_method: :name, value_method: :name, klass: :users, filter_field: "country_name") %>
  <% end %>
  <%= turbo_frame_tag "users_results", target: "_top" do %>
    <div class="flex flex-col mt-8">
      <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full">
              <thead class="bg-white">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">Email</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Store Name</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">Points</th>
                  <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Role</th>
                  <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Last Seen</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <% @grouped_users.keys.each do |key| %>
                  <tr class="border-t border-gray-200">
                    <th class="px-4 py-2 text-sm font-semibold text-left text-gray-900 bg-gray-50 sm:px-6" colspan="5" scope="colgroup">
                      <%= key.titleize %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-300 text-gray-800 ml-2">
                        <%= @grouped_users[key].size %>
                      </span>
                    </th>
                  </tr>
                  <% @grouped_users[key].each do |user| %>
                    <tr class="border-t border-gray-300">
                      <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                        <%= link_to user.name, user_path(user) %>
                        <dl class="font-normal lg:hidden">
                          <dt class="sr-only">Email</dt>
                          <dd class="mt-1 text-gray-500 truncate"><%= user.email %></dd>
                          <dt class="sr-only md:hidden">Store Name</dt>
                          <dd class="mt-1 text-gray-500 truncate md:hidden"><%= user.store&.name || "Unknown" %></dd>
                          <dt class="sr-only sm:hidden">Points</dt>
                          <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= user.wallet&.available_points %></dd>
                        </dl>
                      </td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= user.email %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= user.store&.name || "Unknown" %></td>
                      <td class="hidden px-3 py-4 text-sm text-right text-gray-500 whitespace-nowrap sm:table-cell"><%= user.wallet&.available_points %></td>
                      <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap"><%= render User::RoleBadge.new user: user %></td>
                      <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap"><%= user.last_seen.present? ? "#{time_ago_in_words(user.last_seen)} ago" : "Never" %></td>
                      <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                        <% if current_user != user && allowed_to?(:impersonate?, user) %>
                          <%= button_to impersonate_user_path(user), class: "text-zeiss-600 hover:text-zeiss-900" do %>
                            <%= icon name: "mask", class: "w-5 h-5" %>
                          <% end %>
                        <% end %>
                      </td>
                    </tr>
                  <% end %>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <div class="mt-3">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
