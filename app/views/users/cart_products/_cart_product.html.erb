<li class="flex py-6 sm:py-10" id="<%= dom_id(cart_product) %>">
  <div class="shrink-0">
    <%= image_tag cart_product.product&.image_path, class: "object-cover object-center w-24 h-24 rounded-md sm:w-48 sm:h-48" %>
  </div>
  <div class="flex flex-col justify-between flex-1 ml-4 sm:ml-6">
    <div class="relative pr-9 sm:grid sm:grid-cols-2 sm:gap-x-6 sm:pr-0">
      <div>
        <div class="flex justify-between">
          <h3 class="text-sm">
            <%= link_to cart_product.product&.name, [:shops, cart_product.product], class: "font-medium text-gray-700 hover:text-gray-800" %>
          </h3>
        </div>
        <div class="flex mt-1 text-sm">
        </div>
        <p class="mt-1 text-sm font-medium text-gray-900"><%= cart_product.points_needed %> points</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:pr-9">
        <%= label :cart_product, :quantity, class: "sr-only" %>
        <%= number_field :cart_product, :quantity, value: cart_product.quantity, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-20 sm:text-sm border-gray-300 rounded-md", data:{ fetch_method: "patch", fetch_url: "#{url_for([current_user, :cart, cart_product])}"}, "@change.debounce": "$fetch(`{\"cart_product\": {\"quantity\": ${$el.value}}}`)" %>
        <div class="absolute top-0 right-0">
          <%= link_to user_cart_cart_product_path(current_user, cart_product), data: { turbo_method: :delete }, class: "inline-flex p-2 -m-2 text-gray-400 hover:text-gray-500" do %>
            <span class="sr-only">Remove</span>
            <%= icon name: "times", class: "w-5 h-5" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</li>
