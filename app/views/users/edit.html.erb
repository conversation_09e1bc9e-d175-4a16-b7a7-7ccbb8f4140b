<% content_for :css do %>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slim-select/2.8.1/slimselect.css">
<% end %>
<%= component "page" do |page| %>
  <% page.with_header title: "Edit #{@user.name}" %>
  <%= form_with model: @user do |form| %>
    <%= render "shared/error_messages", resource: form.object %>
    <div class="space-y-8 bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
      <div class="px-4 py-5 space-y-8 divide-y divide-gray-200 sm:p-6">
        <div>
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Personal Information
            </h3>
          </div>
          <div class="grid grid-cols-6 gap-6 mt-6">
            <div class="col-span-6 sm:col-span-3">
              <%= form.label :first_name, class: "block text-sm font-medium text-gray-700" %>
              <%= form.text_field :first_name, autocomplete: "given-name", placeholder: "<PERSON>", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-3">
              <%= form.label :last_name, class: "block text-sm font-medium text-gray-700" %>
              <%= form.text_field :last_name, autocomplete: "family-name", placeholder: "Jobs", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-4">
              <%= form.label :email, class: "block text-sm font-medium text-gray-700" %>
              <%= form.email_field :email, autocomplete: "email", placeholder: "<EMAIL>", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= form.label :phone_number, class: "block text-sm font-medium text-gray-700" %>
              <%= form.text_field :phone_number, autocomplete: "tel", placeholder: "************", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", "x-data": "", "x-mask": "(*************" %>
            </div>
            <%= form.fields_for :address do |address| %>
              <div class="col-span-6">
                <%= address.label :line1, class: "block text-sm font-medium text-gray-700" %>
                <%= address.text_field :line1, autocomplete: "street-address", placeholder: "555 Main St.", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
              <div class="col-span-6 sm:col-span-2">
                <%= address.label :city, class: "block text-sm font-medium text-gray-700" %>
                <%= address.text_field :city, autocomplete: "address-level2", placeholder: "San Francisco", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
              <div class="col-span-6 sm:col-span-2">
                <%= address.label :state_id, class: "block text-sm font-medium text-gray-700" %>
                <%= address.collection_select :state_id, State.order(:name), :id, :name, {}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
              <div class="col-span-6 sm:col-span-2">
                <%= address.label :zip_code, class: "block text-sm font-medium text-gray-700" %>
                <%= address.text_field :zip_code, autocomplete: "postal-code", placeholder: "94103", class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
            <% end %>
            <div class="col-span-6 sm:col-span-2">
              <%= form.label :store_id, class: "block text-sm font-medium text-gray-700" %>
              <%= form.collection_select :store_id, Store.includes(:address).order(:name), :id, :name_with_city, {}, class: "max-w-lg block focus:ring-zeiss-500 focus:border-zeiss-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md", "x-slimselect": "" %>
            </div>
            <div class="col-span-6 sm:col-span-2">
              <%= form.label :status, class: "block text-sm font-medium text-gray-700" %>
              <%= form.select :status, User.statuses.keys.reject { |s| %w[fresh deleted].include?(s) }.map { |r| [r.titleize, r] }, {}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", "x-ref": "roles", "@change": "showBrands = $el.value == 'admin'" %>
            </div>
          </div>
        </div>
        <% if current_user.super_admin? %>
          <div class="pt-8">
            <div>
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                Authorizations
              </h3>
            </div>
            <div class="grid grid-cols-6 gap-6 mt-6" x-data="{showBrands: false, showRegions: false}" x-init="showBrands = $refs.roles.value == 'admin'; showRegions = $refs.brands.value">
              <div class="col-span-6 sm:col-span-2">
                <%= form.label :role, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :role, User.roles.keys.reject {|r| %w[region_admin photo_admin optics_admin].include?(r) }.map {|r| [r.titleize, r]}, {}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", "x-ref": "roles", "@change": "showBrands = $el.value == 'admin'" %>
              </div>
              <div class="col-span-6 sm:col-span-2" x-show="showBrands" x-cloak>
                <%= form.label :admin_brand_id, class: "block text-sm font-medium text-gray-700" %>
                <%= form.collection_select :admin_brand_id, Brand.all, :id, :name, {include_blank: true}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm", "x-ref": "brands", "@change": "showRegions = $el.value" %>
              </div>
              <div class="col-span-6 sm:col-span-2" x-show="showRegions" x-cloak>
                <%= form.label :admin_region_id, class: "block text-sm font-medium text-gray-700" %>
                <%= form.collection_select :admin_region_id, Region.all, :id, :name, {include_blank: true}, class: "mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      <div class="px-4 py-5 bg-gray-50 sm:p-6">
        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", @user, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          <% if allowed_to?(:destroy?, form.object) && @user.persisted? %>
            <%= link_to "Delete", form.object, class: "bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs text-sm font-medium text-red-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500", data: { turbo_onfirm: "Are you sure?", turbo_method: :delete } %>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
