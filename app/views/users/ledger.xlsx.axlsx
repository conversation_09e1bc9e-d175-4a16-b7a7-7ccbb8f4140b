workbook = xlsx_package.workbook

workbook.add_worksheet(name: @user.name) do |sheet|
  sheet.add_row [@user.name, "", "Starting Points", @user.points_earned], offset: 3
  sheet.add_row
  sheet.add_row ["Date", "Type", "Credit", "Debit", "Balance"]

  @transactions.sort_by(&:created_at).each_with_index do |transaction, index|
    credit = transaction.points if transaction.is_a?(Sale) || (transaction.is_a?(Adjustment) && transaction.kind == "credit")
    debit = transaction.points if transaction.is_a?(Order) || (transaction.is_a?(Adjustment) && transaction.kind == "debit")

    previous = index == 0 ? 0 : "E#{index + 3}"

    sheet.add_row [transaction.created_at.strftime("%d %b %Y"), transaction.class.to_s, credit, debit, "=#{previous}+C#{index + 4}-D#{index + 4}"]
  end
end
