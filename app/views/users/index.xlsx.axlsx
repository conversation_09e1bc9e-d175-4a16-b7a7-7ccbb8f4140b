require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD", b: true

wb.add_worksheet(name: "Users") do |sheet|
  sheet.add_row ["Name", "Email", "Phone", "Street", "City", "State", "Zip", "Points", "Store"], style: head_style
  @users.each do |user|
    sheet.add_row [user.name, user.email, number_to_phone(user.phone_number.delete("-"), area_code: true), user.address.line1, user.address.city, user.address.state&.name, user.address.zip_code, user.wallet&.available_points, user.store&.name]
  end
end
