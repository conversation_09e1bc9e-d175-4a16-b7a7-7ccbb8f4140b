<%= component "page" do |page| %>
  <% page.with_header title: @user.name do |header| %>
    <% header.with_actions([
      {title: "New Order", url: shop_path, visible: allowed_to?(:create?, Order)},
      {title: "Edit", url: (current_user == @user ? edit_user_registration_path : edit_user_path(@user)), primary: true, visible: allowed_to?(:update?, @user)}
    ]) %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_button(title: "Impersonate", icon_name: "mask", url: impersonate_user_path(@user), visible: allowed_to?(:impersonate?, @user)) %>
      <% menu.with_item_modal(title: "Change Password", icon_name: "key", visible: allowed_to?(:change_password?, @user)) do %>
        <%= form_with model: @user do |form| %>
          <div class="px-4 py-5 sm:p-6">
            <div>
              <%= form.label :password, class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= form.password_field :password, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>
            <div>
              <%= form.label :password_confirmation, class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= form.password_field :password_confirmation, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>
          </div>
          <div class="px-4 py-3 sm:px-6 bg-gray-50">
            <div class="flex justify-end">
              <%= form.submit "Change Password", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
  <%= turbo_stream_from(:user, @user) %>
  <div class="grid grid-cols-1 gap-6 md:grid-cols-6">
    <div class="md:col-span-2">
      <div class="overflow-hidden bg-white rounded-lg shadow-sm">
        <div class="px-4 py-5 sm:p-6">
          <%= image_tag @user.avatar_path %>
          <div class="pt-2 space-y-3">
            <%= render User::RoleBadge.new user: @user %>
            <%= render User::StatusBadge.new user: @user %>
            <p class="flex items-center text-sm text-gray-500">
              <%= icon(name: "envelope", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400") %>
              <span class="truncate"><%= @user.email %></span>
            </p>
            <p class="flex items-center text-sm text-gray-500">
              <%= icon(name: "phone", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400") %>
              <span class="truncate"><%= number_to_phone(@user.phone_number, area_code: true) %></span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="md:col-span-4">
      <div class="grid grid-rows-1 gap-4 md:grid-rows-2">
        <div class="md:row-span-1">
          <div class="overflow-hidden bg-white rounded-lg shadow-sm">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
                <div class="mt-2 ml-4">
                  <h3 class="text-lg font-medium leading-6 text-gray-900">
                    Details
                  </h3>
                </div>
                <% if allowed_to?(:activate?, @user) %>
                  <div class="shrink-0 mt-2 ml-4">
                    <%= component "page/button/toggle", user: @user %>
                  </div>
                <% end %>
              </div>
            </div>
            <div class="px-4 py-5">
              <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                <div class="sm:col-span-1">
                  <dt class="text-sm font-medium text-gray-500">
                    Address
                  </dt>
                  <dd class="mt-1 text-sm text-gray-900">
                    <address>
                      <%= @user.address.line1 %><br>
                      <%= @user.address.line2 %><br>
                      <%= @user.address.city %>, <%= @user.address.state.name %> <%= @user.address.zip_code %>
                    </address>
                  </dd>
                </div>
                <% if @user.store.present? %>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">
                      Store
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <%= link_to @user.store.name, @user.store %>
                      <address>
                        <%= @user.store.address.line1 %><br>
                        <%= @user.store.address.line2 %><br>
                        <%= @user.store.address.city %>, <%= @user.store.address.state.name %> <%= @user.store.address.zip_code %>
                      </address>
                    </dd>
                  </div>
                <% end %>
              </dl>
            </div>
          </div>
        </div>
        <% if @user.wallet.present? %>
          <div class="md:row-span-1">
            <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-3">
              <div class="px-4 py-5 overflow-hidden bg-white rounded-lg shadow-sm sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Current Points
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900" id="current_balance">
                  <%= @user.wallet.current_balance %>
                </dd>
              </div>
              <div class="px-4 py-5 overflow-hidden bg-white rounded-lg shadow-sm sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Available Points
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900" id="available_balance">
                  <%= @user.wallet.available_points %>
                </dd>
              </div>
            </dl>
          </div>
        <% end %>
      </div>
    </div>
    <div class="md:col-span-6">
      <div class="mt-4 overflow-hidden bg-white rounded-lg shadow-sm">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
          <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
            <div class="mt-2 ml-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                Point Activity
              </h3>
            </div>
            <div class="flex mt-2 ml-4 space-x-3 shrink-0">
              <%= component "modal", id: "adjustment_dialog", name: "Adjust Points", visible: allowed_to?(:create?, Adjustment) && @user.wallet.present? do |modal| %>
                <% modal.with_button title: "Adjust Points", click: "modalOpen = true" %>
                <%= form_with model: [@user, @adjustment] do |form| %>
                  <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div id="adjustment_errors">
                      <%= render "shared/error_messages", resource: @adjustment %>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-6 gap-y-6 gap-x-4">
                      <div class="sm:col-span-6">
                        <%= form.label :kind, class: "text-base font-medium text-gray-900" %>
                        <fieldset class="mt-4">
                          <legend class="sr-only">Adjustment kind</legend>
                          <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
                            <%= form.collection_radio_buttons :kind, Adjustment.kinds, :first, :first do |button| %>
                              <div class="flex items-center">
                                <%= button.radio_button class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300" %>
                                <%= button.label class: "ml-3 block text-sm font-medium text-gray-700" do %>
                                  <%= button.text.titleize %>
                                <% end %>
                              </div>
                            <% end %>
                          </div>
                        </fieldset>
                      </div>
                      <div class="sm:col-span-3">
                        <%= form.label :points, class: "block text-sm font-medium text-gray-700" %>
                        <%= form.number_field :points, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md mt-1" %>
                      </div>
                      <div class="sm:col-span-6">
                        <%= form.label :notes, class: "block text-sm font-medium text-gray-700" %>
                        <%= form.text_area :notes, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md mt-1" %>
                      </div>
                    </div>
                  </div>
                  <div class="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
                    <%= form.submit "Adjust", class: "inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-zeiss-600 border border-transparent rounded-md shadow-xs hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 sm:ml-3 sm:w-auto sm:text-sm", "@click": "$dialog.close()" %>
                    <button type="button" class="inline-flex justify-center w-full px-4 py-2 mt-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" x-on:click="$dialog.close()">
                      Cancel
                    </button>
                  </div>
                <% end %>
              <% end %>
              <%= link_to("Audit", audit_user_path, class: "inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-red-500 focus:ring-offset-2") if current_user.super_admin? %>
            </div>
          </div>
        </div>
        <div class="px-4 py-5 sm:p-6">
          <%= turbo_frame_tag :activity_list, src: user_activities_path(@user) %>
        </div>
      </div>
    </div>
  </div>
<% end %>
