<%= component "page" do |page| %>
  <% page.with_header title: @category.name do |header| %>
    <% header.with_actions([
      {title: "Edit", url: edit_category_path(@category), primary: true, visible: allowed_to?(:edit?, @category)},
    ]) %>
  <% end %>
  <%= component "card" do %>
    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Name
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= @category.name %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Description
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= @category.description %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Brand
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= @category.brand.name %>
        </dd>
      </div>
    </dl>
  <% end %>
  <%= component "list", items: @products %>
  <div class="px-4 py-4 sm:px-6"><!-- Card Footer -->
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
<%= turbo_stream_from @category %>
