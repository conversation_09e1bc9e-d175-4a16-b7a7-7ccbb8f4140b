<%= component "page" do |page| %>
  <% page.with_header title: "Categories" do |header| %>
    <% header.with_actions([
      {title: "New Category", url: new_category_path, primary: true, visible: allowed_to?(:create?, Category)},
    ]) %>
  <% end %>
  <div class="mt-8 -mx-4 overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 sm:-mx-6 md:mx-0 md:rounded-lg">
    <table class="min-w-full divide-y divide-gray-300">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
          <th scope="col" class="hidden px-3 py-3.5 text-left text-sm font-semibold text-gray-900 sm:table-cell">Description</th>
          <th scope="col" class="hidden px-3 py-3.5 text-left text-sm font-semibold text-gray-900 lg:table-cell">No. of Products</th>
          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Brand</th>
          <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6"><span class="sr-only">Actions</span></th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <%= render @categories %>
      </tbody>
    </table>
  </div>
<% end %>
<%= turbo_stream_from :categories %>
