<%= form_with(model: category) do |form| %>
  <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
    <div>
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          Category Details
        </h3>
      </div>
      <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_field :name, class: "max-w-lg block focus:ring-zeiss-500 focus:border-zeiss-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.text_area :description, class: "max-w-lg shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md" %>
          </div>
        </div>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= form.label :brand_id, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <%= form.collection_select :brand_id, Brand.all, :id, :name, {}, class: "max-w-lg block focus:ring-zeiss-500 focus:border-zeiss-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>
    </div>
  </div>
  <%= render "shared/error_messages", resource: form.object %>
  <div class="pt-5">
    <div class="flex justify-end">
      <%= link_to "Cancel", categories_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <% if allowed_to?(:destroy?, form.object) && category.persisted? %>
        <%= link_to "Delete", form.object, class: "ml-3 bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs text-sm font-medium text-red-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500", data: { confirm: "Are you sure?", turbo_method: :delete } %>
      <% end %>
    </div>
  </div>
<% end %>
