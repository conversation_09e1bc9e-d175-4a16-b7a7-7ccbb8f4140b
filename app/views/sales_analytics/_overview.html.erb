<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
  <h3 class="text-lg font-semibold text-gray-900 mb-6">Sales Overview</h3>
  
  <!-- Key Metrics Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Sales -->
    <div class="text-center p-4 bg-blue-50 rounded-lg">
      <p class="text-3xl font-bold text-blue-600">
        <%= number_with_delimiter(data[:current_period][:total_sales]) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Total Sales</p>
      <% if data[:growth][:sales_growth] != 0 %>
        <p class="text-xs mt-2 <%= data[:growth][:sales_growth] > 0 ? 'text-green-600' : 'text-red-600' %>">
          <%= data[:growth][:sales_growth] > 0 ? '↗' : '↘' %>
          <%= data[:growth][:sales_growth].abs %>% vs previous period
        </p>
      <% end %>
    </div>

    <!-- Approved Sales -->
    <div class="text-center p-4 bg-green-50 rounded-lg">
      <p class="text-3xl font-bold text-green-600">
        <%= number_with_delimiter(data[:current_period][:approved_sales]) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Approved Sales</p>
      <% if data[:growth][:approved_growth] != 0 %>
        <p class="text-xs mt-2 <%= data[:growth][:approved_growth] > 0 ? 'text-green-600' : 'text-red-600' %>">
          <%= data[:growth][:approved_growth] > 0 ? '↗' : '↘' %>
          <%= data[:growth][:approved_growth].abs %>% vs previous period
        </p>
      <% end %>
    </div>

    <!-- Total Points -->
    <div class="text-center p-4 bg-yellow-50 rounded-lg">
      <p class="text-3xl font-bold text-yellow-600">
        <%= number_with_delimiter(data[:current_period][:total_points]) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Total Points</p>
      <% if data[:growth][:points_growth] != 0 %>
        <p class="text-xs mt-2 <%= data[:growth][:points_growth] > 0 ? 'text-green-600' : 'text-red-600' %>">
          <%= data[:growth][:points_growth] > 0 ? '↗' : '↘' %>
          <%= data[:growth][:points_growth].abs %>% vs previous period
        </p>
      <% end %>
    </div>

    <!-- Approval Rate -->
    <div class="text-center p-4 bg-purple-50 rounded-lg">
      <p class="text-3xl font-bold text-purple-600">
        <%= data[:current_period][:approval_rate] %>%
      </p>
      <p class="text-sm text-gray-600 mt-1">Approval Rate</p>
      <p class="text-xs mt-2 text-gray-500">
        <%= data[:current_period][:pending_sales] %> pending
      </p>
    </div>
  </div>

  <!-- Performance Breakdown -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Sales Status Breakdown -->
    <div>
      <h4 class="text-md font-medium text-gray-900 mb-4">Sales Status Breakdown</h4>
      <div class="space-y-3">
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-600">Approved</span>
          <div class="flex items-center">
            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
              <div class="bg-green-500 h-2 rounded-full" style="width: <%= (data[:current_period][:approved_sales].to_f / data[:current_period][:total_sales] * 100).round(1) %>%"></div>
            </div>
            <span class="text-sm font-medium text-gray-900">
              <%= data[:current_period][:approved_sales] %>
            </span>
          </div>
        </div>
        
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-600">Pending</span>
          <div class="flex items-center">
            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
              <div class="bg-yellow-500 h-2 rounded-full" style="width: <%= (data[:current_period][:pending_sales].to_f / data[:current_period][:total_sales] * 100).round(1) %>%"></div>
            </div>
            <span class="text-sm font-medium text-gray-900">
              <%= data[:current_period][:pending_sales] %>
            </span>
          </div>
        </div>
        
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-600">Declined</span>
          <div class="flex items-center">
            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
              <div class="bg-red-500 h-2 rounded-full" style="width: <%= (data[:current_period][:declined_sales].to_f / data[:current_period][:total_sales] * 100).round(1) %>%"></div>
            </div>
            <span class="text-sm font-medium text-gray-900">
              <%= data[:current_period][:declined_sales] %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Period Comparison -->
    <div>
      <h4 class="text-md font-medium text-gray-900 mb-4">Period Comparison</h4>
      <div class="space-y-4">
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div>
            <p class="text-sm font-medium text-gray-900">Current Period</p>
            <p class="text-xs text-gray-600">
              <%= data[:current_period][:total_sales] %> sales, 
              <%= number_with_delimiter(data[:current_period][:total_points]) %> points
            </p>
          </div>
          <div class="text-right">
            <p class="text-sm font-bold text-blue-600">Current</p>
          </div>
        </div>
        
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div>
            <p class="text-sm font-medium text-gray-900">Previous Period</p>
            <p class="text-xs text-gray-600">
              <%= data[:previous_period][:total_sales] %> sales, 
              <%= number_with_delimiter(data[:previous_period][:total_points]) %> points
            </p>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-500">Previous</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Performance Indicators -->
  <div class="mt-8 pt-6 border-t border-gray-200">
    <h4 class="text-md font-medium text-gray-900 mb-4">Key Performance Indicators</h4>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">
          <%= data[:current_period][:average_points_per_sale] %>
        </p>
        <p class="text-sm text-gray-600">Average Points per Sale</p>
      </div>
      
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">
          <%= ((data[:current_period][:approved_sales].to_f / data[:current_period][:total_sales]) * 100).round(1) %>%
        </p>
        <p class="text-sm text-gray-600">Conversion Rate</p>
      </div>
      
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">
          <%= data[:current_period][:total_sales] > 0 ? (data[:current_period][:total_points].to_f / data[:current_period][:total_sales]).round(1) : 0 %>
        </p>
        <p class="text-sm text-gray-600">Points per Transaction</p>
      </div>
    </div>
  </div>
</div>
