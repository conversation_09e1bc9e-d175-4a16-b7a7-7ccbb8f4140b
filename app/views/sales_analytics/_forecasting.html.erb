<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900">Sales Forecasting</h3>
      <p class="text-sm text-gray-600 mt-1">Predictive analytics based on historical trends</p>
    </div>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      <%= icon name: "trending-up", class: "h-3 w-3 mr-1" %>
      AI Powered
    </span>
  </div>

  <% if data[:error] %>
    <!-- Error State -->
    <div class="text-center py-8">
      <div class="mx-auto w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-4">
        <%= icon name: "alert-triangle", class: "h-6 w-6 text-yellow-600" %>
      </div>
      <h4 class="text-lg font-medium text-gray-900 mb-2">Insufficient Data</h4>
      <p class="text-sm text-gray-600 max-w-md mx-auto">
        <%= data[:error] %>
      </p>
      <p class="text-xs text-gray-500 mt-2">
        Forecasting will be available once you have more sales data.
      </p>
    </div>
  <% else %>
    <!-- Forecast Chart -->
    <div class="mb-6">
      <%= render Charts::LineChartComponent.new(
        title: "Sales Forecast - Next #{data[:forecast_period]}",
        data: {
          labels: (data[:historical_data].map { |d| d[:date].strftime("%m/%d") } + 
                   data[:forecast_data].map { |d| d[:date].strftime("%m/%d") }),
          datasets: [
            {
              label: "Historical Sales",
              data: data[:historical_data].map { |d| d[:sales] } + Array.new(data[:forecast_data].length, nil),
              borderColor: "#3b82f6",
              backgroundColor: "rgba(59, 130, 246, 0.1)",
              tension: 0.4,
              fill: false
            },
            {
              label: "Forecasted Sales",
              data: Array.new(data[:historical_data].length, nil) + data[:forecast_data].map { |d| d[:sales] },
              borderColor: "#10b981",
              backgroundColor: "rgba(16, 185, 129, 0.1)",
              borderDash: [5, 5],
              tension: 0.4,
              fill: false
            }
          ]
        },
        height: "300px",
        chart_id: "sales-forecast-chart",
        smooth: true,
        x_axis_label: "Date",
        y_axis_label: "Sales Count"
      ) %>
    </div>

    <!-- Forecast Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="text-center p-4 bg-green-50 rounded-lg">
        <p class="text-2xl font-bold text-green-600">
          <%= data[:forecast_data].sum { |d| d[:sales] } %>
        </p>
        <p class="text-sm text-gray-600">Predicted Sales</p>
        <p class="text-xs text-gray-500 mt-1">Next <%= data[:forecast_period] %></p>
      </div>
      
      <div class="text-center p-4 bg-blue-50 rounded-lg">
        <p class="text-2xl font-bold text-blue-600">
          <%= (data[:forecast_data].sum { |d| d[:sales] }.to_f / data[:forecast_data].length).round(1) %>
        </p>
        <p class="text-sm text-gray-600">Daily Average</p>
        <p class="text-xs text-gray-500 mt-1">Forecasted</p>
      </div>
      
      <div class="text-center p-4 bg-purple-50 rounded-lg">
        <p class="text-2xl font-bold text-purple-600">
          <%= data[:accuracy_metrics][:r_squared] %>
        </p>
        <p class="text-sm text-gray-600">R² Score</p>
        <p class="text-xs text-gray-500 mt-1">Model Accuracy</p>
      </div>
    </div>

    <!-- Forecast Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Methodology -->
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Forecast Methodology</h4>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Method:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:method] %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Data Points:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:historical_data].length %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Forecast Period:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:forecast_period] %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Confidence Level:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:confidence_interval][:confidence_level] %>%</span>
          </div>
        </div>
      </div>

      <!-- Accuracy Metrics -->
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Model Performance</h4>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Mean Absolute Error:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:accuracy_metrics][:mean_absolute_error] %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Mean Squared Error:</span>
            <span class="text-sm font-medium text-gray-900"><%= data[:accuracy_metrics][:mean_squared_error] %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Confidence Interval:</span>
            <span class="text-sm font-medium text-gray-900">
              <%= data[:confidence_interval][:lower_bound] %> - <%= data[:confidence_interval][:upper_bound] %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Forecast Insights -->
    <div class="mt-6 pt-6 border-t border-gray-200">
      <h4 class="text-md font-medium text-gray-900 mb-3">Forecast Insights</h4>
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <%= icon name: "lightbulb", class: "h-5 w-5 text-blue-600" %>
          </div>
          <div class="ml-3">
            <h5 class="text-sm font-medium text-blue-900">Forecast Analysis</h5>
            <p class="text-sm text-blue-700 mt-1">
              Based on current trends, we predict 
              <strong><%= data[:forecast_data].sum { |d| d[:sales] } %> sales</strong> 
              over the next <%= data[:forecast_period] %>, representing a 
              <% growth = ((data[:forecast_data].sum { |d| d[:sales] }.to_f / data[:historical_data].last(30).sum { |d| d[:sales] }) - 1) * 100 %>
              <strong><%= growth > 0 ? '+' : '' %><%= growth.round(1) %>%</strong> 
              change from recent performance.
            </p>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>
