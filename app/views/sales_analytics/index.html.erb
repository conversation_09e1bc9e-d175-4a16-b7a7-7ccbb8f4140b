<%= component "page" do |page| %>
  <% page.with_header title: "Advanced Sales Analytics" do %>
    <div class="flex items-center space-x-4">
      <%= link_to reports_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "arrow-left", class: "h-4 w-4 mr-2" %>
        Back to Reports
      <% end %>
      
      <!-- Date Range Selector -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Period:</label>
        <select 
          onchange="updateDateRange(this.value)"
          class="rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 text-sm"
        >
          <option value="7d" <%= 'selected' if @date_range == '7d' %>>Last 7 Days</option>
          <option value="30d" <%= 'selected' if @date_range == '30d' %>>Last 30 Days</option>
          <option value="90d" <%= 'selected' if @date_range == '90d' %>>Last 90 Days</option>
          <option value="1y" <%= 'selected' if @date_range == '1y' %>>Last Year</option>
        </select>
      </div>
      
      <!-- Export Options -->
      <div class="relative" x-data="{ open: false }">
        <button 
          @click="open = !open"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "download", class: "h-4 w-4 mr-2" %>
          Export Analytics
          <%= icon name: "chevron-down", class: "h-4 w-4 ml-2" %>
        </button>
        
        <div 
          x-show="open" 
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
        >
          <div class="py-1">
            <%= link_to export_sales_analytics_path(format: :xlsx, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              Excel Report
            <% end %>
            <%= link_to export_sales_analytics_path(format: :csv, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              CSV Data
            <% end %>
            <%= link_to export_sales_analytics_path(format: :pdf, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              PDF Report
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Analytics Insights -->
  <div id="analytics-insights" class="mb-8">
    <%= render "insights", insights: @analytics_data[:insights] %>
  </div>

  <!-- Sales Overview -->
  <div id="sales-overview" class="mb-8">
    <%= render "overview", data: @analytics_data[:overview] %>
  </div>

  <!-- Main Analytics Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Sales Trends -->
    <div id="sales-trends">
      <%= render "trends", data: @analytics_data[:trends] %>
    </div>

    <!-- Performance Comparison -->
    <div id="performance-comparison">
      <%= render "performance_comparison", data: @analytics_data[:performance_comparison] %>
    </div>
  </div>

  <!-- Forecasting Section -->
  <div id="sales-forecasting" class="mb-8">
    <%= render "forecasting", data: @analytics_data[:forecasting] %>
  </div>

  <!-- Product and Regional Analysis -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Product Analysis -->
    <div id="product-analysis">
      <%= render "product_analysis", data: @analytics_data[:product_analysis] %>
    </div>

    <!-- Regional Analysis (Admin only) -->
    <% if current_user.admin? || current_user.super_admin? %>
      <div id="regional-analysis">
        <%= render "regional_analysis", data: @analytics_data[:regional_analysis] %>
      </div>
    <% end %>
  </div>

  <!-- User Performance (Admin only) -->
  <% if current_user.admin? || current_user.super_admin? %>
    <div id="user-performance" class="mb-8">
      <%= render "user_performance", data: @analytics_data[:user_performance] %>
    </div>
  <% end %>

  <!-- Seasonal Analysis -->
  <div id="seasonal-analysis" class="mb-8">
    <%= render "seasonal_analysis", data: @analytics_data[:seasonal_patterns] %>
  </div>

  <!-- Conversion Metrics -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Conversion Metrics</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="text-center">
        <p class="text-3xl font-bold text-blue-600"><%= @analytics_data[:conversion_metrics][:approval_rate] %>%</p>
        <p class="text-sm text-gray-600">Approval Rate</p>
      </div>
      <div class="text-center">
        <p class="text-3xl font-bold text-green-600">
          <%= @analytics_data[:overview][:current_period][:average_points_per_sale] %>
        </p>
        <p class="text-sm text-gray-600">Avg Points/Sale</p>
      </div>
      <div class="text-center">
        <p class="text-3xl font-bold text-purple-600">
          <%= number_with_delimiter(@analytics_data[:overview][:current_period][:total_points]) %>
        </p>
        <p class="text-sm text-gray-600">Total Points</p>
      </div>
    </div>
  </div>
<% end %>

<script>
  function updateDateRange(dateRange) {
    const url = new URL(window.location);
    url.searchParams.set('date_range', dateRange);
    window.location.href = url.toString();
  }
  
  // Auto-refresh data every 5 minutes
  setInterval(() => {
    refreshAnalyticsData();
  }, 300000);
  
  function refreshAnalyticsData() {
    // Refresh key components
    fetch(`<%= overview_sales_analytics_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`<%= trends_sales_analytics_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`<%= insights_sales_analytics_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
  }
</script>
