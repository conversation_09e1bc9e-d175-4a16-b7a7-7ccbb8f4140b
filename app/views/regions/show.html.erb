<header class="mb-3">
  <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <div>
            <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
              <i class="w-5 h-5 shrink-0 fas fa-home"></i>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="w-5 h-5 text-gray-400 shrink-0 fas fa-chevron-right"></i>
            <%= link_to "Regions", regions_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="w-5 h-5 text-gray-400 shrink-0 fas fa-chevron-right"></i>
            <%= link_to "Region ##{@region.id}", @region, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
      </ol>
    </nav>
  </div>
</header>
<main>
  <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6">
        <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
          <div class="mt-2 ml-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Region Details
            </h3>
          </div>
          <div class="mt-2 ml-4 shrink-0">
            <%= link_to t("edit"), edit_region_path(@region), class: "relative inline-flex items-center px-4 py-2 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" if allowed_to?(:edit?, @region) %>
          </div>
        </div>
      </div>
      <div class="px-4 py-5 border-t border-gray-200 sm:p-0">
        <dl class="sm:divide-y sm:divide-gray-200">
          <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Name
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @region.name %>
            </dd>
          </div>
          <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Admin Name
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</main>
