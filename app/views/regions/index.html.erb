<%= component "page" do |page| %>
  <% page.with_header title: "Regions" do |header| %>
    <% header.with_actions([
      {title: "New Region", url: new_region_path, primary: true, visible: allowed_to?(:create?, Region)},
    ]) %>
  <% end %>
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="mt-8 -mx-4 overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 sm:-mx-6 md:mx-0 md:rounded-lg">
      <table class="min-w-full divide-y divide-gray-300">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-sm font-semibold text-left text-gray-900 sm:pl-6">Name</th>
            <th scope="col" class="hidden px-3 py-3.5 text-dm font-semibold text-left text-gray-900 sm:table-cell">Admin Name</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @regions.each do |region| %>
            <tr>
              <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6"><%= link_to region.name, region %></td>
              <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= region.admin&.name %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
  <div class="px-4 py-4 sm:px-6"><!-- Card Footer -->
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
