<header class="mb-3">
  <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <div>
            <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
              <i class="shrink-0 w-5 h-5 fas fa-home"></i>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="shrink-0 w-5 h-5 text-gray-400 fas fa-chevron-right"></i>
            <%= link_to "Regions", regions_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="shrink-0 w-5 h-5 text-gray-400 fas fa-chevron-right"></i>
            <%= link_to "Edit Region ##{@region.id}", [:edit, @region], class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
      </ol>
    </nav>
  </div>
</header>
<main>
  <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6"><!-- Card Header -->
        <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
          <div class="mt-2 ml-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Edit Region #<%= @region.id %>
            </h3>
          </div>
        </div>
      </div>
      <div class="px-4 py-5 sm:px-6">
        <%= render "form", region: @region %>
      </div>
    </div>
  </div>
</main>
