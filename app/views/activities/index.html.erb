<%= turbo_frame_tag :activity_list do %>
  <%= component "list/activity" do |list| %>
    <% @activities.each do |activity| %>
      <% list.with_item(transaction: activity.transactable, type: activity.transactable_type, date: activity.transactable.created_at, kind: activity.kind, declined: activity.declined?) %>
    <% end %>
  <% end %>
  <div class="mt-4">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
  <%= turbo_stream_from [@user, :activities] %>
<% end %>
