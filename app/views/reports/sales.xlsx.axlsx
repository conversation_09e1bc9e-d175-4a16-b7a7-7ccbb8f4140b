require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD", b: true

wb.add_worksheet(name: "Sales") do |sheet|
  sheet.add_row ["Date", "Product", "Points"], style: head_style
  @sales.each do |sale|
    sheet.add_row [sale.sold_at, sale.product.name, sale.points]
  end

  sheet.add_row

  sheet.add_row ["Top Products"], style: head_style
  sheet.add_row ["Name", "# Sold"]
  @sales.top(:product_id).each do |item|
    sheet.add_row [Product.find(item.first).name, item.last]
  end
end
