<% content_for :css do %>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
<% end %>
<%= component "page" do |page| %>
  <% page.with_header title: "Reports" %>
  <div class="flex flex-col mt-8">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full">
            <thead class="bg-white">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Download</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white">
              <tr class="border-t border-gray-200">
                <th colspan="2" scope="colgroup" class="px-4 py-2 text-sm font-semibold text-left text-gray-900 bg-gray-50 sm:px-6">User Activity</th>
              </tr>
              <tr class="border-t border-gray-300">
                <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">Login Activity</td>
                <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                  <%= component "modal", id: "login_dialog" do |modal| %>
                    <% modal.with_button title: "Download", click: "modalOpen = true" %>
                    <%= form_with url: login_reports_path(format: "xlsx") do |form| %>
                      <%= form.label :target, class: "block text-sm font-medium text-gray-700" %>
                      <div class="mt-1">
                        <%= form.text_field :target, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{altInput: true, dateFormat: 'Z', mode: 'range'}", required: true %>
                      </div>
                      <div class="flex justify-end mt-2">
                        <%= form.submit "Generate & Download", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500", "@click": "modalOpen = false" %>
                      </div>
                    <% end %>
                  <% end %>
                </td>
              </tr>
              <tr class="border-t border-gray-300">
                <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">Sale Activity</td>
                <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                  <%= component "modal", id: "sale_dialog" do |modal| %>
                    <% modal.with_button title: "Download", click: "modalOpen = true" %>
                    <%= form_with url: sales_reports_path(format: "xlsx") do |form| %>
                      <%= form.label :target, class: "block text-sm font-medium text-gray-700" %>
                      <div class="mt-1">
                        <%= form.text_field :target, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{altInput: true, dateFormat: 'Z', mode: 'range'}", required: true %>
                      </div>
                      <div class="flex justify-end mt-2">
                        <%= form.submit "Generate & Download", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500", "@click": "modalOpen = false" %>
                      </div>
                    <% end %>
                  <% end %>
                </td>
              </tr>
              <tr class="border-t border-gray-300">
                <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">Order Activity</td>
                <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                  <%= component "modal", id: "order_dialog" do |modal| %>
                    <% modal.with_button title: "Download", click: "modalOpen = true" %>
                    <%= form_with url: orders_reports_path(format: "xlsx") do |form| %>
                      <%= form.label :target, class: "block text-sm font-medium text-gray-700" %>
                      <div class="mt-1">
                        <%= form.text_field :target, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{altInput: true, dateFormat: 'Z', mode: 'range'}", required: true %>
                      </div>
                      <div class="flex justify-end mt-2">
                        <%= form.submit "Generate & Download", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500", "@click": "modalOpen = false" %>
                      </div>
                    <% end %>
                  <% end %>
                </td>
              </tr>
              <tr class="border-t border-gray-300">
                <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">Desktop vs Mobile</td>
                <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                  <%= link_to "Download", "#", class: "text-zeiss-600 hover:text-zeiss-900" %>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
<% end %>
