<% content_for :css do %>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
<% end %>

<%= component "page" do |page| %>
  <% page.with_header title: "Reports & Analytics" do %>
    <div class="flex items-center space-x-4">
      <%= link_to analytics_reports_path,
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "bar-chart-3", class: "h-4 w-4 mr-2" %>
        Analytics Dashboard
      <% end %>

      <%= link_to dashboard_real_time_analytics_path,
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "activity", class: "h-4 w-4 mr-2" %>
        Real-Time Analytics
        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
          Live
        </span>
      <% end %>

      <div class="relative" x-data="{ open: false }">
        <button
          @click="open = !open"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "download", class: "h-4 w-4 mr-2" %>
          Quick Export
          <%= icon name: "chevron-down", class: "h-4 w-4 ml-2" %>
        </button>

        <div
          x-show="open"
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
        >
          <div class="py-1">
            <a href="#" onclick="exportQuickReport('sales', '30d')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Last 30 Days Sales
            </a>
            <a href="#" onclick="exportQuickReport('orders', '30d')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Last 30 Days Orders
            </a>
            <a href="#" onclick="exportQuickReport('users', '30d')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              User Activity Report
            </a>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Quick Stats Overview -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <%= render Dashboard::StatCardComponent.new(
      title: "Total Reports",
      value: @available_reports.sum { |category| category[:reports].count },
      icon_name: "file-text",
      color: "blue"
    ) %>

    <%= render Dashboard::StatCardComponent.new(
      title: "This Month",
      value: Sale.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count,
      icon_name: "trending-up",
      color: "green",
      subtitle: "Sales generated"
    ) %>

    <%= render Dashboard::StatCardComponent.new(
      title: "Export Formats",
      value: "4",
      icon_name: "download",
      color: "purple",
      subtitle: "Excel, CSV, PDF, JSON"
    ) %>

    <%= render Dashboard::StatCardComponent.new(
      title: "Last Export",
      value: "2 hrs ago",
      icon_name: "clock",
      color: "orange",
      subtitle: "Sales report"
    ) %>
  </div>

  <!-- Report Builder -->
  <div class="mb-8">
    <%= render Reports::ReportBuilderComponent.new(user: current_user) %>
  </div>

  <!-- Available Reports -->
  <div class="space-y-8">
    <% @available_reports.each do |category| %>
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900"><%= category[:category] %></h3>
          <p class="text-sm text-gray-600 mt-1"><%= category[:reports].count %> reports available</p>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <% category[:reports].each do |report| %>
              <div class="border border-gray-200 rounded-lg p-4 hover:border-zeiss-300 hover:shadow-md transition-all duration-200">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="text-sm font-semibold text-gray-900 mb-2"><%= report[:name] %></h4>
                    <p class="text-xs text-gray-600 mb-4"><%= report[:description] %></p>

                    <div class="flex items-center space-x-2">
                      <%= component "modal", id: "#{report[:key]}_dialog" do |modal| %>
                        <% modal.with_button title: "Generate",
                           class: "inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500",
                           click: "modalOpen = true" %>

                        <div class="space-y-4">
                          <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Generate <%= report[:name] %></h3>
                            <p class="text-sm text-gray-600 mb-4"><%= report[:description] %></p>
                          </div>

                          <%= form_with url: send("#{report[:key]}_reports_path"), local: true, class: "space-y-4" do |form| %>
                            <div>
                              <%= form.label :target, "Date Range", class: "block text-sm font-medium text-gray-700" %>
                              <div class="mt-1">
                                <%= form.text_field :target,
                                    class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md",
                                    "x-flatpickr": "{altInput: true, dateFormat: 'Z', mode: 'range'}",
                                    required: true,
                                    placeholder: "Select date range" %>
                              </div>
                            </div>

                            <div>
                              <%= form.label :format, "Export Format", class: "block text-sm font-medium text-gray-700" %>
                              <div class="mt-1">
                                <%= form.select :format,
                                    options_for_select([
                                      ['Excel (.xlsx)', 'xlsx'],
                                      ['CSV (.csv)', 'csv'],
                                      ['PDF (.pdf)', 'pdf']
                                    ], 'xlsx'),
                                    {},
                                    { class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" } %>
                              </div>
                            </div>

                            <div class="flex justify-end space-x-3 pt-4">
                              <button
                                type="button"
                                @click="modalOpen = false"
                                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
                              >
                                Cancel
                              </button>
                              <%= form.submit "Generate & Download",
                                  class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500",
                                  "@click": "modalOpen = false" %>
                            </div>
                          <% end %>
                        </div>
                      <% end %>

                      <% if report[:key] == 'sales_analytics' %>
                        <%= link_to analytics_reports_path,
                            class: "inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
                          <%= icon name: "eye", class: "h-3 w-3 mr-1" %>
                          View
                        <% end %>
                      <% end %>
                    </div>
                  </div>

                  <div class="ml-4">
                    <%= icon name: "file-text", class: "h-8 w-8 text-gray-400" %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
<% end %>

<script>
  function exportQuickReport(type, period) {
    const endDate = new Date();
    const startDate = new Date();

    switch(period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
    }

    const dateRange = `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/${type}_reports.xlsx`;

    const targetInput = document.createElement('input');
    targetInput.type = 'hidden';
    targetInput.name = 'target';
    targetInput.value = dateRange;

    form.appendChild(targetInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }
</script>
