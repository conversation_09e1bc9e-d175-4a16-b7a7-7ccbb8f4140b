require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD", b: true

wb.add_worksheet(name: "Orders") do |sheet|
  sheet.add_row ["Date", "Product", "Points"], style: head_style
  @orders.each do |order|
    sheet.add_row [order.created_at, "", order.points]
  end

  sheet.add_row

  sheet.add_row ["Top Products"], style: head_style
  sheet.add_row ["Name", "# Ordered"]
  @top_products.each do |item|
    sheet.add_row [item.first, item.last]
  end
end
