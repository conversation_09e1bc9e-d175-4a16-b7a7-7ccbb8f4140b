require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD", b: true

wb.add_worksheet(name: "Users") do |sheet|
  sheet.add_row ["Name", "Store", "Region", "Device", "Sign In Count", "Last Login", "Signed Up"], style: head_style
  @users.each do |user|
    sheet.add_row [user.name, user.store.name, user.region.name, user.visits&.last&.device_type, user.sign_in_count, user.last_sign_in_at.strftime("%b %d, %Y"), user.created_at.strftime("%b %d, %Y")]
  end
end
