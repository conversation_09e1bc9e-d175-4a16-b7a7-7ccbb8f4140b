<%= component "page" do |page| %>
  <% page.with_header title: "Order for #{@user.name}" %>
  <%= form_with(model: Order.new, url: checkout_user_cart_path(@user)) do |form| %>
    <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-3">
      <div class="px-4 py-5 overflow-hidden bg-white border border-gray-200 rounded-lg sm:p-6">
        <dt class="text-sm font-medium text-gray-500 truncate">Points Available</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900">
          <%= @user.wallet.available_points %>
        </dd>
      </div>
      <div class="px-4 py-5 overflow-hidden bg-white border border-gray-200 rounded-lg sm:p-6">
        <dt class="text-sm font-medium text-gray-500 truncate">Cart Total</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900" id="cart_total">
          <%= @cart.total_points %>
        </dd>
      </div>
    </dl>
    <%= render "shared/error_messages", resource: form.object %>
    <div class="mt-8 -mx-4 overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 sm:-mx-6 md:mx-0 md:rounded-lg">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
            <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 lg:table-cell">SKU</th>
            <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:table-cell">Points</th>
            <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900">Quantity</th>
            <th scope="col" class="relative flex justify-end py-3 pl-3 pr-4 sm:pr-6">
              <%= render Modal::Component.new name: "Add Product" do |modal| %>
                <% modal.button title: "Add", size: :small, click: "modalOpen = true" %>
                <div class="relative">
                  <%= icon name: "magnifying-glass", weight: :thin, class: "pointer-events-none absolute top-3.5 left-4 h-5 w-5 text-gray-400" %>
                  <%= text_field_tag :search, "", class: "h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-800 placeholder-gray-400 sm:text-sm", placeholder: "Search...", role: "combobox", aria_controls: "options", "@input.debounce": "$fetch(`{\"q\": \"${$el.value}\", \"user_id\": \"#{@user.id}\"}`)", data: {fetch_method: "post", fetch_url: search_products_path}, autocomplete: :off %>
                </div>
                <ul class="hidden py-2 overflow-y-auto text-sm text-gray-800 max-h-72 scroll-py-2" id="search_results" role="listbox">
                </ul>
              <% end %>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200" id="products">
          <%= render @cart.cart_products %>
        </tbody>
      </table>
    </div>
    <div class="mt-8">
      <fieldset class="my-4" x-data="{value: 'work', active: 'work'}">
        <legend class="sr-only">Shipping location</legend>
        <div class="flex space-x-4">
          <%= form.label :ship_to_work, class: "relative block bg-white border rounded-lg shadow-xs px-6 py-4 cursor-pointer sm:flex sm:justify-between focus:outline-hidden border-transparent undefined", ":class": "{'border-transparent' : (value === 'work'), 'border-gray-300': !(value === 'work'), 'border-zeiss-500 ring-2 ring-zeiss-500': (active === 'work')}" do %>
            <%= form.radio_button :ship_to, "work", class: "sr-only", "x-model": "value", "@change": "active = $el.value" %>
            <div class="flex items-center">
              <div class="text-sm">
                <p class="font-medium text-gray-900">Store (Recommended)</p>
                <div class="text-gray-500">
                  <p class="sm:inline"><%= @user.store.name_with_city %></p>
                </div>
              </div>
            </div>
            <div class="absolute rounded-lg pointer-events-none border-zeiss-500 -inset-px" aria-hidden="true" :class="{ 'border': (active === 'work'), 'border-2': !(active === 'work'), 'border-zeiss-500': (value === 'work'), 'border-transparent': !(value === 'work') }"></div>
          <% end %>
          <%= form.label :ship_to_home, class: "relative block bg-white border rounded-lg shadow-xs px-6 py-4 cursor-pointer sm:flex sm:justify-between focus:outline-hidden border-transparent undefined", ":class": "{'border-transparent' : (value === 'home'), 'border-gray-300': !(value === 'home'), 'border-zeiss-500 ring-2 ring-zeiss-500': (active === 'home')}" do %>
            <%= form.radio_button :ship_to, "home", class: "sr-only", "x-model": "value", "@change": "active = $el.value" %>
            <div class="flex items-center">
              <div class="text-sm">
                <p class="font-medium text-gray-900">Home</p>
                <div class="text-gray-500">
                  <p class="sm:inline"><%= @user.street %>, <%= @user.city %>, <%= @user.state.name %></p>
                </div>
              </div>
            </div>
            <div class="absolute rounded-lg pointer-events-none border-zeiss-500 -inset-px" aria-hidden="true" :class="{ 'border': (active === 'home'), 'border-2': !(active === 'home'), 'border-zeiss-500': (value === 'home'), 'border-transparent': !(value === 'home') }"></div>
          <% end %>
        </div>
      </fieldset>
    </div>
    <div class="mt-8">
      <div class="flex justify-end">
        <%= link_to "Cancel", orders_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      </div>
    </div>
  <% end %>
<% end %>
