<%= component "page" do |page| %>
  <% page.with_header title: "Cart" %>
  <div class="overflow-hidden bg-white rounded-lg shadow-sm">
    <div class="max-w-2xl px-4 pt-16 pb-24 mx-auto sm:px-6 lg:max-w-7xl lg:px-8">
      <div class="mt-12 lg:grid lg:grid-cols-12 lg:gap-x-12 lg:items-start xl:gap-x-16">
        <section aria-labelledby="cart-heading" class="lg:col-span-7">
          <h2 id="cart-heading" class="sr-only">Items in your shopping cart</h2>
          <ul role="list" class="border-t border-b border-gray-200 divide-y divide-gray-200">
            <%= render @cart.products %>
          </ul>
        </section>
        <!-- Order summary -->
        <section aria-labelledby="summary-heading" class="px-4 py-6 mt-16 rounded-lg bg-gray-50 sm:p-6 lg:p-8 lg:mt-0 lg:col-span-5">
          <%= form_with model: Order.new do |form| %>
            <h2 class="text-lg font-medium text-gray-900">Shipping info</h2>
            <p class="prose-sm prose">
              To help you receive your order and prevent theft we recommend having your order shipped to your store. Should the address be incorrect please let us know.
            </p>
            <fieldset class="my-4" x-data="{value: 'work'}" x-radio x-model="value" name="order[ship_to]">
              <legend class="sr-only">Shipping location</legend>
              <div class="space-y-4">
                <div class="relative block px-6 py-4 bg-white border rounded-lg shadow-xs cursor-pointer sm:flex sm:justify-between focus:outline-hidden" :class="{'border-gray-300': !$radioOption.isActive, 'border-zeiss-500 ring-2 ring-zeiss-500': $radioOption.isActive}" x-radio:option value="work">
                  <div class="flex items-center">
                    <div class="text-sm">
                      <p class="font-medium text-gray-900" x-radio:label>Store (Recommended)</p>
                      <div class="text-gray-500" x-radio:description>
                        <p class="sm:inline"><%= current_user.store.name_with_city %></p>
                      </div>
                    </div>
                  </div>
                  <span class="absolute rounded-lg pointer-events-none border-zeiss-500 -inset-px" aria-hidden="true" :class="{ 'border': $radioOption.isActive, 'border-2': !$radioOption.isActive, 'border-zeiss-500': $radioOption.isChecked, 'border-transparent': !$radioOption.isChecked }"></span>
                </div>
                <div class="relative block px-6 py-4 bg-white border rounded-lg shadow-xs cursor-pointer sm:flex sm:justify-between focus:outline-hidden" :class="{'border-gray-300': !$radioOption.isActive, 'border-zeiss-500 ring-2 ring-zeiss-500': $radioOption.isActive}" x-radio:option value="home">
                  <div class="flex items-center">
                    <div class="text-sm">
                      <p class="font-medium text-gray-900" x-radio:label>Home</p>
                      <div class="text-gray-500" x-radio:description>
                        <p class="sm:inline"><%= current_user.address.line1 %>, <%= current_user.address.city %>, <%= current_user.address.state.name %></p>
                      </div>
                    </div>
                  </div>
                  <span class="absolute rounded-lg pointer-events-none border-zeiss-500 -inset-px" aria-hidden="true" :class="{ 'border': $radioOption.isActive, 'border-2': !$radioOption.isActive, 'border-zeiss-500': $radioOption.isChecked, 'border-transparent': !$radioOption.isChecked }"></span>
                </div>
              </div>
            </fieldset>
            <h2 id="summary-heading" class="text-lg font-medium text-gray-900">Order summary</h2>
            <dl class="mt-6 space-y-4">
              <div class="flex items-center justify-between">
                <dt class="text-sm text-gray-600">
                  Subtotal
                </dt>
                <dd class="text-sm font-medium text-gray-900" id="cart_subtotal">
                  <%= @cart.subtotal %>
                </dd>
              </div>
              <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <dt class="text-base font-medium text-gray-900">
                  Order total
                </dt>
                <dd class="text-base font-medium text-gray-900" id="cart_total">
                  <%= @cart.subtotal %>
                </dd>
              </div>
              <div class="flex items-center justify-between">
                <dt class="text-sm text-gray-600">
                  Available points after checkout
                </dt>
                <dd class="text-sm font-medium text-gray-900" id="available">
                  <%= current_user.wallet.available_points %>
                </dd>
              </div>
            </dl>
            <div class="mt-6">
              <%= form.submit "Checkout", class: "w-full justify-center flex px-4 py-3 text-base font-medium text-white bg-zeiss-600 border border-transparent rounded-md shadow-xs hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-zeiss-500" %>
            </div>
          <% end %>
        </section>
      </div>
    </div>
  </div>
<% end %>
