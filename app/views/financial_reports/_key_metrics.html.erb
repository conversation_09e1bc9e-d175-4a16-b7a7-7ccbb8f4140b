<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
  <h3 class="text-lg font-semibold text-gray-900 mb-6">Key Financial Metrics</h3>
  
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Revenue -->
    <div class="text-center p-4 bg-green-50 rounded-lg">
      <div class="flex items-center justify-center w-12 h-12 bg-green-500 rounded-full mx-auto mb-3">
        <%= icon name: "dollar-sign", class: "h-6 w-6 text-white" %>
      </div>
      <p class="text-2xl font-bold text-green-600">
        $<%= number_with_delimiter(data[:total_revenue], precision: 0) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Total Revenue</p>
      <% if data[:revenue_growth] != 0 %>
        <p class="text-xs mt-2 <%= data[:revenue_growth] > 0 ? 'text-green-600' : 'text-red-600' %>">
          <%= data[:revenue_growth] > 0 ? '↗' : '↘' %>
          <%= data[:revenue_growth].abs %>% vs previous period
        </p>
      <% end %>
    </div>

    <!-- Total Costs -->
    <div class="text-center p-4 bg-red-50 rounded-lg">
      <div class="flex items-center justify-center w-12 h-12 bg-red-500 rounded-full mx-auto mb-3">
        <%= icon name: "trending-down", class: "h-6 w-6 text-white" %>
      </div>
      <p class="text-2xl font-bold text-red-600">
        $<%= number_with_delimiter(data[:total_costs], precision: 0) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Total Costs</p>
      <p class="text-xs mt-2 text-gray-500">
        $<%= number_with_delimiter(data[:cost_efficiency], precision: 2) %> efficiency ratio
      </p>
    </div>

    <!-- Gross Profit -->
    <div class="text-center p-4 bg-blue-50 rounded-lg">
      <div class="flex items-center justify-center w-12 h-12 bg-blue-500 rounded-full mx-auto mb-3">
        <%= icon name: "trending-up", class: "h-6 w-6 text-white" %>
      </div>
      <p class="text-2xl font-bold text-blue-600">
        $<%= number_with_delimiter(data[:gross_profit], precision: 0) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Gross Profit</p>
      <p class="text-xs mt-2 text-gray-500">
        <%= data[:profit_margin] %>% margin
      </p>
    </div>

    <!-- Points Liability -->
    <div class="text-center p-4 bg-yellow-50 rounded-lg">
      <div class="flex items-center justify-center w-12 h-12 bg-yellow-500 rounded-full mx-auto mb-3">
        <%= icon name: "star", class: "h-6 w-6 text-white" %>
      </div>
      <p class="text-2xl font-bold text-yellow-600">
        $<%= number_with_delimiter(data[:points_liability], precision: 0) %>
      </p>
      <p class="text-sm text-gray-600 mt-1">Points Liability</p>
      <p class="text-xs mt-2 text-gray-500">
        <%= data[:points_redemption_rate] %>% redemption rate
      </p>
    </div>
  </div>

  <!-- Financial Health Indicators -->
  <div class="mt-8 pt-6 border-t border-gray-200">
    <h4 class="text-md font-medium text-gray-900 mb-4">Financial Health Indicators</h4>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Profit Margin -->
      <div class="text-center">
        <div class="relative w-20 h-20 mx-auto mb-3">
          <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#e5e7eb"
              stroke-width="3"
            />
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#10b981"
              stroke-width="3"
              stroke-dasharray="<%= [data[:profit_margin], 100].min %>, 100"
            />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <span class="text-sm font-semibold text-gray-900"><%= data[:profit_margin] %>%</span>
          </div>
        </div>
        <p class="text-sm text-gray-600">Profit Margin</p>
        <p class="text-xs text-gray-500 mt-1">
          <%= data[:profit_margin] > 20 ? 'Excellent' : data[:profit_margin] > 10 ? 'Good' : 'Needs Improvement' %>
        </p>
      </div>

      <!-- Cost Efficiency -->
      <div class="text-center">
        <div class="relative w-20 h-20 mx-auto mb-3">
          <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#e5e7eb"
              stroke-width="3"
            />
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#3b82f6"
              stroke-width="3"
              stroke-dasharray="<%= [(data[:cost_efficiency] * 100), 100].min %>, 100"
            />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <span class="text-sm font-semibold text-gray-900"><%= (data[:cost_efficiency] * 100).round(1) %>%</span>
          </div>
        </div>
        <p class="text-sm text-gray-600">Cost Efficiency</p>
        <p class="text-xs text-gray-500 mt-1">
          <%= data[:cost_efficiency] > 0.8 ? 'Excellent' : data[:cost_efficiency] > 0.6 ? 'Good' : 'Needs Improvement' %>
        </p>
      </div>

      <!-- Revenue Growth -->
      <div class="text-center">
        <div class="relative w-20 h-20 mx-auto mb-3">
          <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#e5e7eb"
              stroke-width="3"
            />
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="<%= data[:revenue_growth] > 0 ? '#10b981' : '#ef4444' %>"
              stroke-width="3"
              stroke-dasharray="<%= [data[:revenue_growth].abs, 100].min %>, 100"
            />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <span class="text-sm font-semibold text-gray-900"><%= data[:revenue_growth] %>%</span>
          </div>
        </div>
        <p class="text-sm text-gray-600">Revenue Growth</p>
        <p class="text-xs text-gray-500 mt-1">
          <%= data[:revenue_growth] > 15 ? 'Excellent' : data[:revenue_growth] > 5 ? 'Good' : data[:revenue_growth] > 0 ? 'Stable' : 'Declining' %>
        </p>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mt-6 pt-6 border-t border-gray-200">
    <div class="flex flex-wrap gap-3">
      <button 
        onclick="showDetailedBreakdown()"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "pie-chart", class: "h-4 w-4 mr-2" %>
        Detailed Breakdown
      </button>
      
      <button 
        onclick="exportMetrics()"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "download", class: "h-4 w-4 mr-2" %>
        Export Metrics
      </button>
      
      <button 
        onclick="scheduleReport()"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "calendar", class: "h-4 w-4 mr-2" %>
        Schedule Report
      </button>
    </div>
  </div>
</div>

<script>
  function showDetailedBreakdown() {
    // Implementation for showing detailed financial breakdown
    console.log('Showing detailed breakdown');
  }
  
  function exportMetrics() {
    // Implementation for exporting key metrics
    window.location.href = '<%= export_financial_reports_path(format: :xlsx) %>';
  }
  
  function scheduleReport() {
    // Implementation for scheduling financial reports
    console.log('Scheduling report');
  }
</script>
