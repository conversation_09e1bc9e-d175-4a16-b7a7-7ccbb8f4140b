<%= component "page" do |page| %>
  <% page.with_header title: "Financial Reports & Analytics" do %>
    <div class="flex items-center space-x-4">
      <%= link_to reports_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "arrow-left", class: "h-4 w-4 mr-2" %>
        Back to Reports
      <% end %>
      
      <%= link_to dashboard_financial_reports_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "pie-chart", class: "h-4 w-4 mr-2" %>
        Financial Dashboard
      <% end %>
      
      <!-- Date Range Selector -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Period:</label>
        <select 
          onchange="updateDateRange(this.value)"
          class="rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 text-sm"
        >
          <option value="7d" <%= 'selected' if @date_range == '7d' %>>Last 7 Days</option>
          <option value="30d" <%= 'selected' if @date_range == '30d' %>>Last 30 Days</option>
          <option value="90d" <%= 'selected' if @date_range == '90d' %>>Last 90 Days</option>
          <option value="1y" <%= 'selected' if @date_range == '1y' %>>Last Year</option>
        </select>
      </div>
      
      <!-- Export Options -->
      <div class="relative" x-data="{ open: false }">
        <button 
          @click="open = !open"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "download", class: "h-4 w-4 mr-2" %>
          Export Financial Report
          <%= icon name: "chevron-down", class: "h-4 w-4 ml-2" %>
        </button>
        
        <div 
          x-show="open" 
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
        >
          <div class="py-1">
            <%= link_to export_financial_reports_path(format: :xlsx, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              Excel Report
            <% end %>
            <%= link_to export_financial_reports_path(format: :csv, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              CSV Data
            <% end %>
            <%= link_to export_financial_reports_path(format: :pdf, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              PDF Report
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Financial Insights -->
  <div id="financial-insights" class="mb-8">
    <%= render "insights", insights: @financial_data[:insights] %>
  </div>

  <!-- Key Financial Metrics -->
  <div id="key-metrics" class="mb-8">
    <%= render "key_metrics", data: @financial_data[:key_metrics] %>
  </div>

  <!-- Revenue and Cost Analysis -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Revenue Tracking -->
    <div id="revenue-tracking">
      <%= render "revenue_tracking", data: @financial_data[:revenue_tracking] %>
    </div>

    <!-- Cost Analysis -->
    <div id="cost-analysis">
      <%= render "cost_analysis", data: @financial_data[:cost_analysis] %>
    </div>
  </div>

  <!-- Points Economics -->
  <div id="points-economics" class="mb-8">
    <%= render "points_economics", data: @financial_data[:points_economics] %>
  </div>

  <!-- Profitability and ROI -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Profitability Analysis -->
    <div id="profitability-analysis">
      <%= render "profitability_analysis", data: @financial_data[:profitability_analysis] %>
    </div>

    <!-- ROI Analysis -->
    <div id="roi-analysis">
      <%= render "roi_analysis", data: @financial_data[:roi_analysis] %>
    </div>
  </div>

  <!-- Budget Tracking -->
  <div id="budget-tracking" class="mb-8">
    <%= render "budget_tracking", data: @financial_data[:budget_tracking] %>
  </div>

  <!-- Financial Forecasting -->
  <div id="financial-forecasting" class="mb-8">
    <%= render "financial_forecasting", data: @financial_data[:forecasting] %>
  </div>

  <!-- Financial Trends -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">Financial Trends</h3>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue Trends Chart -->
      <div>
        <%= render Charts::LineChartComponent.new(
          title: "Revenue Trends",
          subtitle: "Revenue performance over time",
          data: {
            labels: @financial_data[:financial_trends][:revenue_trends].keys.map { |date| date.strftime("%m/%d") },
            datasets: [{
              label: "Revenue",
              data: @financial_data[:financial_trends][:revenue_trends].values,
              borderColor: "#10b981",
              backgroundColor: "rgba(16, 185, 129, 0.1)",
              tension: 0.4,
              fill: true
            }]
          },
          height: "250px",
          smooth: true,
          colors: ["#10b981"]
        ) %>
      </div>

      <!-- Cost Trends Chart -->
      <div>
        <%= render Charts::LineChartComponent.new(
          title: "Cost Trends",
          subtitle: "Cost analysis over time",
          data: {
            labels: @financial_data[:financial_trends][:cost_trends].keys.map { |date| date.strftime("%m/%d") },
            datasets: [{
              label: "Costs",
              data: @financial_data[:financial_trends][:cost_trends].values,
              borderColor: "#ef4444",
              backgroundColor: "rgba(239, 68, 68, 0.1)",
              tension: 0.4,
              fill: true
            }]
          },
          height: "250px",
          smooth: true,
          colors: ["#ef4444"]
        ) %>
      </div>
    </div>
  </div>
<% end %>

<script>
  function updateDateRange(dateRange) {
    const url = new URL(window.location);
    url.searchParams.set('date_range', dateRange);
    window.location.href = url.toString();
  }
  
  // Auto-refresh financial data every 15 minutes
  setInterval(() => {
    refreshFinancialData();
  }, 900000);
  
  function refreshFinancialData() {
    // Refresh key components
    fetch(`<%= key_metrics_financial_reports_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`<%= revenue_tracking_financial_reports_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`<%= insights_financial_reports_path(format: :turbo_stream, date_range: @date_range) %>`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
  }
  
  // Show loading indicators during refresh
  function showLoadingIndicator(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      const loadingDiv = document.createElement('div');
      loadingDiv.className = 'absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center';
      loadingDiv.innerHTML = '<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-zeiss-500"></div>';
      element.style.position = 'relative';
      element.appendChild(loadingDiv);
      
      setTimeout(() => {
        if (loadingDiv.parentElement) {
          loadingDiv.parentElement.removeChild(loadingDiv);
        }
      }, 3000);
    }
  }
</script>
