<%= component "page" do |page| %>
  <% page.with_header title: "Search Results" %>
  <div class="space-y-8">
    <div class="flex flex-col mt-8">
      <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full">
              <thead class="bg-white">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <% @results.each_result do |klass, results| %>
                  <tr class="border-t border-gray-200">
                    <th class="px-4 py-2 text-sm font-semibold text-left text-gray-900 bg-gray-50 sm:px-6" colspan="2" scope="colgroup"><%= klass.name.pluralize.titleize %></th>
                  </tr>
                  <% results.each do |result| %>
                    <tr class="border-t border-gray-300">
                      <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6"><%= link_to result.name, result %></td>
                      <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6"></td>
                    </tr>
                  <% end %>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
