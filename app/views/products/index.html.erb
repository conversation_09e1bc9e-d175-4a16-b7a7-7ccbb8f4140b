<%= component "page" do |page| %>
  <% page.with_header title: "Products" do |header| %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_link name: "Export (.xlsx)", url: request.params.merge(format: "xlsx").except(:limit), visible: allowed_to?(:export?, Product), icon: "file-spreadsheet" %>
    <% end %>
    <% header.with_actions([
      {title: "New Product", url: new_product_url, visible: allowed_to?(:create?, Product), primary: true},
    ]) %>
  <% end %>
  <%= component "page/filter", name: "Products", sort_options: [{label: "Name", option: "name"}, {label: "Brand", option: "category_brand_name"}, {label: "Category", option: "category_name"}] do |filter| %>
    <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :products, filter_field: "category_brand_name") %>
    <% filter.with_filter_collection(name: "Category", collection: Category.order(:name), name_method: :name, value_method: :name, klass: :products, filter_field: "category_name") %>
  <% end %>
  <%= turbo_frame_tag "products_results", target: "_top" do %>
    <div class="flex flex-col mt-8">
      <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full">
              <thead class="bg-white">
                <tr>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6"></th>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">SKU</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">UPC</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Points</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">MSRP</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <% @grouped_products.keys.each do |key| %>
                  <tr class="border-t border-gray-200">
                    <th class="px-4 py-2 text-sm font-semibold text-left text-gray-900 bg-gray-50 sm:px-6" colspan="5" scope="colgroup"><%= key.titleize %></th>
                  </tr>
                  <% @grouped_products[key].each do |product| %>
                    <tr class="border-t border-gray-300">
                      <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                        <%= image_tag product.image_path, width: 50, height: 50 %>
                      </td>
                      <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                        <%= link_to product.name, current_user.regular_user? ? shops_product_path(product) : product %>
                        <dl class="font-normal lg:hidden">
                          <dt class="sr-only">MSRP</dt>
                          <dd class="mt-1 text-gray-500 truncate"><%= number_to_currency(product.msrp_for_country(Current.country.id)) %></dd>
                          <dt class="sr-only md:hidden">Points</dt>
                          <dd class="mt-1 text-gray-500 truncate md:hidden"><%= product.points(Current.country.id) %></dd>
                          <dt class="sr-only sm:hidden">UPC</dt>
                          <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= product.upc %></dd>
                          <dt class="sr-only sm:hidden">SKU</dt>
                          <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= number_to_sku(product.sku) %></dd>
                        </dl>
                      </td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= number_to_sku(product.sku) %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= product.upc %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= product.points(Current.country.id) %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= number_to_currency(product.msrp_for_country(Current.country.id)) %></td>
                      <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6"></td>
                    </tr>
                  <% end %>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <div class="mt-4">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
