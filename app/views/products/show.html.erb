<%= component "page" do |page| %>
  <% page.with_header title: @product.name do |header| %>
    <% header.with_actions([
      {title: "Edit", url: edit_product_path(@product), visible: allowed_to?(:edit?, @product), primary: true},
    ]) %>
  <% end %>
  <% page.with_modal id: "price_dialog" do %>
    <%= turbo_frame_tag :price_form, "x-ref": "price_form" %>
  <% end %>
  <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6">
        <%= image_tag @product.image_path %>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-gray-500">
              Description
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @product.description %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              <abbr title="Stock Keeping Unit">SKU</abbr>
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= number_to_sku(@product.sku) %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              <abbr title="Universal Product Code">UPC</abbr>
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @product.upc? %>
                <%= @product.barcode_data.html_safe %>
                <%= @product.upc %>
              <% else %>
                No UPC defined
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
  <div class="pb-5 my-4 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
    <h3 class="text-lg font-medium leading-6 text-gray-900">Prices</h3>
    <% if Country.available(@product).any? %>
      <div class="flex mt-3 sm:ml-4 sm:mt-0">
        <%= component "page/button", title: "New Pricing", click: "modalOpen = true; $refs.price_form.src = '#{new_product_price_path(@product)}'" %>
      </div>
    <% end %>
  </div>
  <div class="flex flex-col">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Country</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"><abbr title="Manufacturer Suggested Retail Price">MSRP</abbr></th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Points Needed</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Points Earned</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6"><span class="sr-only">Edit</span></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="prices">
              <%= render @product.prices %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="pb-5 my-4 border-b border-gray-200">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Promotions</h3>
    </div>
    <div class="flex flex-col">
      <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Stores</th>
                  <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Multiplier</th>
                  <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Starts</th>
                  <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Ends</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @product.promotions.each do |promotion| %>
                  <tr class="<%= promotion.active? ? "bg-green-50" : "" %>">
                    <td class="py-2 pl-4 pr-3 text-sm text-gray-500 whitespace-nowrap sm:pl-6">
                      <% promotion.stores.each do |store| %>
                        <%= link_to store.name, store %>
                      <% end %>
                    </td>
                    <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= promotion.multiplier %></td>
                    <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= local_time promotion.starts_at %></td>
                    <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= local_time promotion.ends_at %></td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
<%= turbo_stream_from :prices %>
