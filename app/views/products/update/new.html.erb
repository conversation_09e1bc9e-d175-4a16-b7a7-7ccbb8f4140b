<% content_for :css do %>
  <link rel="stylesheet" href="https://unpkg.com/dropzone@6.0.0-beta.2/dist/dropzone.css" type="text/css" />
  <link rel="stylesheet" href="https://jsuites.net/v4/jsuites.css" type="text/css" />
  <link rel="stylesheet" href="https://bossanova.uk/jspreadsheet/v4/jexcel.css" type="text/css" />
<% end %>
<%= component "page" do |page| %>
  <% page.with_header title: "Update Products" %>
  <div x-data="update">
    <div x-show="dropOpen" x-cloak>
      <p class="prose">
        To start updating products, upload a CSV or Excel file. It should have the following columns:
        <code class="p-2 bg-white border border-gray-300 rounded-md shadow-sm">name,sku,upc,msrp,points needed,points earned</code>
      </p>
      <div class="flex justify-center max-w-lg px-6 pt-5 pb-6 mx-auto mt-6 border-2 border-gray-300 border-dashed rounded-md" x-dropzone>
        <div class="space-y-1 text-center">
          <%= inline_svg_tag "icons/file-spreadsheet-thin.svg", class: "mx-auto h-12 w-12 text-gray-400" %>
          <div class="flex text-sm text-gray-600">
            <%= label_tag "file-upload", nil, class: "relative cursor-pointer rounded-md font-medium text-zeiss-600 hover:text-zeiss-500 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-zeiss-500" do %>
              <span>Upload a file</span>
              <%= file_field_tag "file-upload", accept: "text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", class: "sr-only", "x-bind": "file_input" %>
            <% end %>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">CSV up to 10MB</p>
        </div>
      </div>
    </div>
    <div x-show="progressOpen" x-cloak>
      <progress x-progress value="0" max="100"></progress>
    </div>
    <div x-show="sheetOpen" x-cloak>
      <div x-spreadsheet="[{title: 'Name'}, {title: 'Description'}, {title: 'SKU'}, {title: 'UPC'}, {title: 'MSRP'}, {title: 'Points Needed'}, {title: 'Points Earned'}]"></div>
    </div>
  </div>
<% end %>
