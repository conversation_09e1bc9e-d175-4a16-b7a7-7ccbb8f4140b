
require 'axlsx'

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD",  b: true

wb.add_worksheet(name: "Product") do |sheet|
  sheet.add_row %w(ProductName Category MSRP PointsEarned PointsNeeded)
  sheet.row_style 0, head_style
  @products.each do |product|
    sheet.add_row [product.name, product.category.name, product.msrp, product.points_earned, product.points_needed]
  end
end