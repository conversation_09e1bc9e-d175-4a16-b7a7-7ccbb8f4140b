<% content_for :css do %>
  <link href="https://unpkg.com/dropzone@6.0.0-beta.2/dist/dropzone.css" rel="stylesheet" type="text/css" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.css" rel="stylesheet" type="text/css" />
<% end %>
<%= form_with(model: product) do |form| %>
  <%= render "shared/error_messages", resource: form.object %>
  <div class="grid grid-cols-1 sm:grid-cols-2 sm:gap-x-4 gap-y-6">
    <div class="space-y-6">
      <div class="bg-white rounded-md shadow-sm">
        <div class="px-4 py-6 space-y-4">
          <div>
            <%= form.label :name, class: class_names("block", "text-sm", "font-medium", "text-gray-700") %>
            <div class="mt-1">
              <%= form.text_field :name, class: class_names("block", "w-full", "shadow-xs", "sm:leading-6", "sm:text-sm", "border-0", "rounded-md", "shadow-xs", "ring-inset", "ring-gray-300", "ring-1", "placeholder:text-gray-400", "focus:ring-2", "focus:ring-inset", "focus:ring-zeiss-600") %>
            </div>
          </div>
          <div>
            <%= form.label :description, class: class_names("block", "text-sm", "font-medium", "text-gray-700") %>
            <div class="mt-1">
              <%= form.text_area :description, class: "max-w-lg shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md" %>
            </div>
          </div>
          <div>
            <%= form.label :category_id, "Category", class: class_names("block", "text-sm", "font-medium", "text-gray-700") %>
            <div class="mt-1">
              <%= form.collection_select :category_id, Category.all, :id, :name, {}, class: "max-w-lg shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md" %>
            </div>
          </div>
          <div class="mt-1">
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= form.check_box :active, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm" %>
              </div>
              <div class="ml-3 text-sm">
                <%= form.label :active, class: "font-medium text-gray-700" %>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-md shadow-sm" x-data="{gc: <%= form.object.gift_card %>}">
        <div class="px-6 py-4">
          <div class="relative flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :gift_card, class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-sm", "x-model": "gc" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :gift_card, class: "font-medium text-gray-700" %>
            </div>
          </div>
          <div x-show="gc" x-cloak>
            <%= form.label :gift_card_value, class: class_names("block", "text-sm", "font-medium", "text-gray-700") %>
            <div class="mt-1">
              <%= form.text_field :gift_card_value, class: class_names("block", "w-full", "shadow-xs", "sm:leading-6", "sm:text-sm", "border-0", "rounded-md", "shadow-xs", "ring-inset", "ring-gray-300", "ring-1", "placeholder:text-gray-400", "focus:ring-2", "focus:ring-inset", "focus:ring-zeiss-600") %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="space-y-6">
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 space-y-4">
          <div>
            <abbr title="Stock Keeping Unit"><%= form.label :sku, class: "block text-sm font-medium text-gray-700 uppercase" %></abbr>
            <div class="mt-1">
              <%= form.text_field :sku, "x-mask": "999999-9999-999", class: class_names("block", "w-full", "shadow-xs", "sm:leading-6", "sm:text-sm", "border-0", "rounded-md", "shadow-xs", "ring-inset", "ring-gray-300", "ring-1", "placeholder:text-gray-400", "focus:ring-2", "focus:ring-inset", "focus:ring-zeiss-600") %>
            </div>
          </div>
          <div>
            <abbr title="Universal Product Code"><%= form.label :upc, class: "block text-sm font-medium text-gray-700 uppercase" %></abbr>
            <div class="mt-1">
              <%= form.text_field :upc, class: class_names("block", "w-full", "shadow-xs", "sm:leading-6", "sm:text-sm", "border-0", "rounded-md", "shadow-xs", "ring-inset", "ring-gray-300", "ring-1", "placeholder:text-gray-400", "focus:ring-2", "focus:ring-inset", "focus:ring-zeiss-600") %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Image</label>
            <div class="flex items-center justify-center mt-1 space-x-4">
              <%= image_tag product.image_path, width: 200, height: 200, id: "product_image" %>
              <%= component "modal", id: "image_dialog" do |modal| %>
                <% modal.with_button title: "Change", primary: false, click: "modalOpen = true" %>
                <div x-data="{acceptedFiles: 'image/jpeg,image/webp,image/png', uploadUrl: '<%= rails_direct_uploads_url %>', disablePreviews: true}" x-upload>
                  <div x-upload:drop-panel>
                    <div class="flex justify-center max-w-lg px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md" x-upload:dropzone>
                      <div class="space-y-1 text-center">
                        <%= icon name: "image-polaroid", class: "mx-auto w-12 h-12 text-gray-400" %>
                        <div class="flex text-sm text-gray-600">
                          <%= form.label :image, class: "relative cursor-pointer bg-white rounded-md font-medium text-zeiss-600 hover:text-zeiss-500 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-zeiss-500" do %>
                            <span>Upload a file</span>
                            <%= form.file_field :image, accept: "image/jpeg,image/png,image/webp", direct_upload: true, class: "sr-only", "x-upload:input": "" %>
                          <% end %>
                          <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">
                          PNG, JPG, WEBP up to 10MB
                        </p>
                      </div>
                    </div>
                  </div>
                  <div x-upload:crop-panel x-cloak>
                    <div>
                      <img x-upload:cropper />
                    </div>
                    <div class="flex justify-end">
                      <button x-upload:button class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md shadow-xs shadow-zeiss-500 bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500">Crop & Upload</button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="pt-5">
    <div class="flex justify-end space-x-3">
      <%= link_to "Cancel", products_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs shadow-zeiss-500 text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <% if allowed_to?(:destroy?, form.object) && product.persisted? %>
        <%= link_to "Delete", form.object, class: "bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs shadow-red-400 text-sm font-medium text-red-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500", data: { confirm: "Are you sure?", turbo_method: :delete } %>
      <% end %>
    </div>
  </div>
<% end %>
