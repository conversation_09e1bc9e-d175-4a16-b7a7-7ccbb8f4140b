<div class="divide-y divide-gray-200">
  <div class="px-4 py-5 sm:px-6">
    Order #<%= item.id %>
  </div>
  <div class="px-4 py-5 sm:p-6">
    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Order Date
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.created_at.strftime("%b %d, %Y") %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Ordered By
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.user.name %>
        </dd>
      </div>
      <div class="sm:col-span-2">
        <dt class="text-sm font-medium text-gray-500">
          Notes
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.notes %>
        </dd>
      </div>
      <div class="sm:col-span-2">
        <dt class="text-sm font-medium text-gray-500">
          Products
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Sku</th>
                <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Quantity</th>
                <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Points</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% item.line_items.each do |item| %>
                <tr>
                  <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap" x-tooltip="<%= item.product&.name %>"><%= item.product&.sku || "Unknown Product" %></td>
                  <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= item.quantity %></td>
                  <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= item.points %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </dd>
      </div>
      <div class="sm:col-span-2">
        <dt class="text-sm font-medium text-gray-500">
          Status
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.status.titleize %>
        </dd>
      </div>
      <% if item.approved? %>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Approved By
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= item.admin.name if item.admin.present? %>
          </dd>
        </div>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Approved At
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= item.approved_at.strftime("%b %d, %Y") if item.approved_at.present? %>
          </dd>
        </div>
      <% end %>
      <% if item.declined? %>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Declined By
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= item.admin.name if item.admin.present? %>
          </dd>
        </div>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Declined At
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= item.declined_at.strftime("%b %d, %Y") if item.declined_at.present? %>
          </dd>
        </div>
      <% end %>
    </dl>
  </div>
  <div class="px-4 py-4 sm:px-6 bg-gray-50 sm:flex sm:flex-row-reverse">
    <button type="button" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 bg-zeiss-600 hover:bg-zeiss-700 shadow-zeiss-500/50" @click="modalOpen = false">
      Close
    </button>
  </div>
</div>
