<header class="mb-3">
  <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <div>
            <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
              <i class="w-5 h-5 shrink-0 fas fa-home"></i>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="w-5 h-5 text-gray-400 shrink-0 fas fa-chevron-right"></i>
            <%= link_to "Orders", orders_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <i class="w-5 h-5 text-gray-400 shrink-0 fas fa-chevron-right"></i>
            <%= link_to "Edit Order ##{@order.id}", [:edit, @order], class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
      </ol>
    </nav>
  </div>
</header>
<main>
  <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6"><!-- Card Header -->
        <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
          <div class="mt-2 ml-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Edit Order #<%= @order.id %>
            </h3>
          </div>
        </div>
      </div>
      <div class="px-4 py-5 sm:px-6">
        <%= form_with(model: @order) do |form| %>
          <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
            <div>
              <div>
                <h3 class="text-lg font-medium leading-6 text-gray-900">
                  Order Details
                </h3>
              </div>
              <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
                      <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Quantity</th>
                      <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Points</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <% @order.line_items.each do |item| %>
                      <tr>
                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= item.product.name %></td>
                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= item.quantity %></td>
                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap"><%= item.points %></td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                  <%= form.label :status, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                  <div class="mt-1 sm:mt-0 sm:col-span-2">
                    <% if !@order.pending? %>
                      <%= @order.status.titleize %>
                    <% else %>
                      <%= form.select :status, Order.statuses.keys.map {|s| [s.titleize, s]}, {}, class: "max-w-lg block focus:ring-zeiss-500 focus:border-zeiss-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <%= render "shared/error_messages", resource: form.object %>
          <div class="pt-5">
            <div class="flex justify-end">
              <%= link_to "Cancel", orders_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
              <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
              <% if allowed_to?(:destroy?, form.object) && @order.persisted? %>
                <%= link_to "Delete", form.object, class: "ml-3 bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs text-sm font-medium text-red-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500", data: { confirm: "Are you sure?", turbo_method: :delete } %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</main>
