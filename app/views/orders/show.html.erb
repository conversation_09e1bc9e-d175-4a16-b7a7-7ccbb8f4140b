<%= component "page" do |page| %>
  <% page.with_header title: "Order ##{@order.id}" do |header| %>
    <% header.with_status status: @order.status %>
    <% header.with_actions([
      {title: "Edit", url: edit_order_path(@order), primary: true, visible: allowed_to?(:edit?, @order) }
    ]) %>
  <% end %>
  <div class="space-y-4">
    <div class="overflow-hidden bg-white rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6">
        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-8">
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              User
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= render User::Badge.new user: @order.user %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              Ordered On
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @order.created_at.strftime("%b %d, %Y") %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              Ship To <%= @order.ship_to&.titleize || "" %>
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @order.home? %>
                <address>
                  <%= @order.user.address.line1 %><br>
                  <%= @order.user.address.line2 %><br>
                  <%= @order.user.address.city %>, <%= @order.user.address.state.name %><br>
                  <%= @order.user.address.zip_code %>
                </address>
              <% elsif @order.work? && @order.user.store.present? %>
                <%= @order.user.store.name %>
                <address>
                  <%= @order.user.store.address.line1 %><br>
                  <%= @order.user.store.address.line2 %><br>
                  <%= @order.user.store.address.city %>, <%= @order.user.store.address.state.name %><br>
                  <%= @order.user.store.address.zip_code %>
                </address>
              <% end %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <% if !@order.pending? %>
              <dt class="text-sm font-medium text-gray-500">
                <% if @order.declined? %>
                  Declined by
                <% elsif @order.approved? %>
                  Approved by
                <% end %>
              </dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= render User::Badge.new user: @order.admin if @order.admin.present? %>
              </dd>
            <% end %>
          </div>
          <% if current_user.admin? || current_user.super_admin? %>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">
                SAP ID
              </dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @order.sap_id %>
              </dd>
            </div>
          <% end %>
          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-gray-500">
              Products
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <table class="min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:pl-0">Name</th>
                    <th scope="col" class="hidden py-3.5 px-3 text-right text-sm font-semibold text-gray-900 sm:table-cell">SKU</th>
                    <th scope="col" class="py-3.5 px-3 text-right text-sm font-semibold text-gray-900">Quantity</th>
                    <th scope="col" class="py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-6 md:pr-0">Points</th>
                  </tr>
                </thead>
                <tbody>
                  <% @order.line_items.each do |item| %>
                    <tr class="border-b border-gray-200">
                      <td class="py-4 pl-4 pr-3 text-sm truncate sm:pl-6 md:pl-0" x-tooltip="<%= item.product.name %>"><%= item.product.name %></td>
                      <td class="hidden px-3 py-4 text-sm text-right text-gray-500 sm:table-cell"><%= number_to_sku(item.product.sku) %></td>
                      <td class="px-3 py-4 text-sm text-right text-gray-500"><%= item.quantity %></td>
                      <td class="py-4 pl-3 pr-4 text-sm text-right text-gray-500 sm:pr-6 md:pr-0"><%= item.points %></td>
                    </tr>
                  <% end %>
                </tbody>
                <tfoot>
                  <tr>
                    <th scope="row" colspan="3" class="hidden pt-6 pl-6 pr-3 text-sm font-normal text-right text-gray-500 md:pl-0 sm:table-cell">Total</th>
                    <th scope="row" colspan="2" class="pt-6 pl-6 pr-3 text-sm font-normal text-right text-gray-500 md:pl-0 sm:hidden">Total</th>
                    <td class="pt-6 pl-3 pr-4 text-sm font-semibold text-right text-gray-900 sm:pr-6 md:pr-0"><%= @order.points %></td>
                  </tr>
                </tfoot>
              </table>
            </dd>
          </div>
        </dl>
      </div>
      <% if !@order.declined? || !@order.processed? %>
        <div class="flex justify-end px-4 py-5 sm:px-6 bg-gray-50">
          <% if allowed_to?(:edit?, @order) %>
            <% if @order.pending? %>
              <div class="flex flex-row space-x-3">
                <%= form_with model: @order do |f| %>
                  <%= f.button "Approve", type: :submit, name: "order[status]", value: :approved, class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-lime-700 bg-lime-100 hover:bg-lime-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-lime-500" %>
                  <%= f.button "Decline", type: :submit, name: "order[status]", value: :declined, class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
                <% end %>
              </div>
            <% elsif @order.approved? %>
              <%= render Modal::Component.new name: "SAP" do |modal| %>
                <% modal.with_button title: "Process", click: "modalOpen = true" %>
                <%= form_with model: @order do |f| %>
                  <div class="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                    <%= f.hidden_field :status, value: "processed" %>
                    <div>
                      <%= f.label :sap_id, "SAP ID", class: "block text-sm font-medium text-gray-700" %>
                      <div class="mt-1">
                        <%= f.text_field :sap_id, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                      </div>
                    </div>
                    <div class="flex flex-row-reverse mt-4">
                      <%= f.submit "Process", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-lime-700 bg-lime-100 hover:bg-lime-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-lime-500" %>
                    </div>
                  </div>
                <% end %>
              <% end %>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>
    <%= component "card" do |card| %>
      <% card.with_header title: "Notes" %>
      <div class="px-4 py-6 sm:px-6">
        <ul role="list" class="space-y-8">
          <% @order.notations.each do |note| %>
            <li>
              <div class="flex space-x-3">
                <div class="shrink-0">
                  <%= image_tag note.user.avatar_path, class: "h-10 w-10 rounded-full" %>
                </div>
                <div>
                  <div class="text-sm">
                    <%= link_to note.user.name, note.user, class: "font-medium text-gray-900" %>
                  </div>
                  <div class="mt-1 text-sm text-gray-700">
                    <%= note.content %>
                  </div>
                  <div class="mt-2 space-x-2 text-sm">
                    <span class="font-medium text-gray-500">
                      <%= time_ago_in_words note.created_at %>
                    </span>
                  </div>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
      <% if allowed_to?(:edit?, @order) %>
        <% card.with_footer do %>
          <div class="flex space-x-3">
            <div class="shrink-0">
              <%= image_tag current_user.avatar_path, class: "h-10 w-10 rounded-full" %>
            </div>
            <div class="flex-1 min-w-0">
              <%= form_with model: @order do |form| %>
                <div>
                  <%= form.fields_for :notations, Note.new do |fields| %>
                    <%= fields.label :content, class: "sr-only" %>
                    <%= fields.hidden_field :user_id, value: current_user.id %>
                    <%= fields.rich_text_area :content, rows: 3, class: "shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md bg-white" %>
                  <% end %>
                </div>
                <div class="flex items-center justify-between mt-3">
                  <span></span>
                  <%= form.submit "Add Note", class: "inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-zeiss-500 shadow-xs text-white bg-zeiss-500 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% end %>
  </div>
<% end %>
