<%= component "page" do |page| %>
  <% page.with_header title: "Orders" do |header| %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_link(name: "Export (.xlsx)", url: request.params.merge(format: "xlsx").except(:limit), icon: "file-spreadsheet", visible: allowed_to?(:export?, Order)) %>
    <% end %>
  <% end %>
  <%= component "page/filter", name: "Orders" do |filter| %>
    <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :orders, filter_field: "brand_name") %>
    <% filter.with_filter_collection(name: "Status", collection: Order.statuses, name_method: :first, value_method: :first, klass: :orders, filter_field: "status") %>
    <% filter.with_filter_date(name: "Ordered Between", field: :created_at, url: orders_path) %>
    <% filter.with_filter_collection(name: "Country", collection: Country.all, name_method: :name, value_method: :name, klass: :orders, filter_field: "country_name") %>
  <% end %>
  <%= turbo_frame_tag "orders_results", target: "_top" do %>
    <div class="mt-4 overflow-hidden bg-white shadow-sm sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200">
        <%= render(@orders) || "<div class='p-4'>No orders for current filters</div>".html_safe %>
      </ul>
    </div>
  <% end %>
  <div class="mt-4">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
