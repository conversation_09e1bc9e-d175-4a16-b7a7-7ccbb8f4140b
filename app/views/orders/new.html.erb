<%= component "page" do |page| %>
  <% page.with_header title: "Order for #{@order.user.name}" %>
  <%= form_with(model: @order, url: user_orders_path(@order.user)) do |form| %>
    <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-3">
      <div class="px-4 py-5 overflow-hidden bg-white border border-gray-200 rounded-lg sm:p-6">
        <dt class="text-sm font-medium text-gray-500 truncate">Points Available</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900">
          <%= @order.user.wallet.available_balance %>
        </dd>
      </div>
      <div class="px-4 py-5 overflow-hidden bg-white border border-gray-200 rounded-lg sm:p-6">
        <dt class="text-sm font-medium text-gray-500 truncate">Cart Total</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900" id="cart_total">
          <%= @order.user.admin_cart.total_points %>
        </dd>
      </div>
    </dl>
    <div class="mt-8 -mx-4 overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 sm:-mx-6 md:mx-0 md:rounded-lg">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
            <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 lg:table-cell">SKU</th>
            <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:table-cell">Points</th>
            <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900">Quantity</th>
            <th scope="col" class="relative flex justify-end py-3 pl-3 pr-4 sm:pr-6">
              <%= component "modal", name: "Add Product" do |modal| %>
                <% modal.button title: "Add", size: :small, click: "modalOpen = true" %>
              <% end %>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <%= render @order.user.admin_cart.cart_products, locals: {admin: true} %>
        </tbody>
      </table>
    </div>
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-4 py-5 sm:px-6">
        <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
          <div>
            <%= render "shared/error_messages", resource: form.object %>
            <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
              <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                <%= form.label :notes, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                <div class="mt-1 sm:mt-0 sm:col-span-2">
                  <%= form.text_area :notes, class: "max-w-lg shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md" %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="px-4 py-5 bg-gray-50 sm:px-6">
        <div class="flex justify-end">
          <%= link_to "Cancel", orders_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
