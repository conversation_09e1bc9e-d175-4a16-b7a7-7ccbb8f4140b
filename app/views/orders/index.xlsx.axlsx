require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "DDDDDD", b: true

wb.add_worksheet(name: "Order") do |sheet|
  sheet.add_row %w[OrderId OrderDate Points SalesRep Email Store Status ReviewedBy ReviewDate]
  sheet.row_style 0, head_style
  @orders.each do |order|
    review_date = if order.approved?
                    order.approved_at
                  elsif order.declined?
                    order.declined_at
                  else
                    nil
                  end
    sheet.add_row [order.id, order.created_at, order.points, order.user&.name, order.user.email, order.user.store.name, order.status.titleize, order.admin&.name, review_date ]
  end
end
