<%= component "page" do |page| %>
  <% page.with_header title: "Sales" do |header| %>
    <% header.with_actions([
      {title: "New Sale", url: new_sale_path, primary: true}
    ]) %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_link(name: "Export (.xlsx)", url: request.params.merge(format: "xlsx").except(:limit), icon: "file-spreadsheet", visible: allowed_to?(:export?, Sale)) %>
    <% end %>
  <% end %>
  <% if @calendar %>
    <div class="flex flex-col space-y-3">
      <div>
        <%= component "page/pagination", pagy: @calendar[:year], calendar: true %>
      </div>
      <div>
        <%= component "page/pagination", pagy: @calendar[:month], calendar: true %>
      </div>
    </div>
  <% end %>
  <%= component "page/filter", name: "Sales" do |filter| %>
    <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :sales, filter_field: "brand_name") %>
    <% filter.with_filter_collection(name: "Status", collection: Sale.statuses, name_method: :first, value_method: :first, klass: :sales, filter_field: "status") %>
    <% filter.with_filter_collection(name: "Country", collection: Country.all, name_method: :name, value_method: :name, klass: :sales, filter_field: "country_name") %>
    <% filter.with_filter_date(name: "Sold Between", field: :sold_at, url: sales_path) %>
  <% end %>
  <%= turbo_frame_tag "sales_results", target: "_top" do %>
    <div class="mt-4 overflow-hidden bg-white shadow-sm sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200">
        <%= render(@sales) || "<div class='p-4'>No sales for current filters</div>".html_safe %>
      </ul>
    </div>
  <% end %>
  <div class="mt-4">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
