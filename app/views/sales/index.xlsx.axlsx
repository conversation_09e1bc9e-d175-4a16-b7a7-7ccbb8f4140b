require "axlsx"

wb = xlsx_package.workbook
head_style = wb.styles.add_style bg_color: "e8e9f4", fg_color: "141e8c", locked: true

wb.add_worksheet(name: "Sales") do |sheet|
  sheet.add_row ["Id", "Sold At", "Points", "Sales Rep", "Store Name", "State", "Email", "Status", "Product Name", "Serial Number", "SKU"], style: head_style
  @sales.each do |sale|
    sheet.add_row [sale.id, sale.sold_at, sale.points, sale.user&.name, sale.user.store&.name, sale.user.store.state&.name, sale.user.email, sale.status.titleize, sale.product&.name, sale.serial_number, sale.product&.sku]
  end
end
