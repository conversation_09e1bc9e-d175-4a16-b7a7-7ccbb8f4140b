<%= component "page" do |page| %>
  <% page.with_header title: "Sale ##{@sale.id}" do |header| %>
    <% header.with_status status: @sale.status, large: true %>
    <% header.with_actions([
      { title: "Edit", url: edit_sale_path(@sale), visible: allowed_to?(:edit, @sale) },
      { title: "New Sale", url: new_sale_path, visible: allowed_to?(:create?, Sale), primary: true }
    ]) %>
  <% end %>
  <div class="space-y-4">
    <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-8">
          <div class="sm:col-span-1">
            <div class="grid grid-rows-2 gap-y-8">
              <div>
                <dt class="text-sm font-medium text-gray-500">
                  User
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= render User::Badge.new user: @sale.user %>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">
                  Serial Number
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= @sale.serial_number %>
                </dd>
              </div>
            </div>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              Product
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= render "products/sale_product", product: @sale.product, promotion: @sale.promotion %>
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">
              Sold On
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= local_time(@sale.sold_at, "%B %d, %Y") %>
            </dd>
          </div>
          <% if !@sale.pending? && @sale.admin.present? %>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">
                Status
              </dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @sale.admin.name %> <%= @sale.status %> the sale at <%= local_time(@sale.approved_at, "%B %d, %Y") %>
              </dd>
            </div>
          <% end %>
          <% if Flipper.enabled?(:receipts, Current.country) %>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">
                Receipt
              </dt>
              <dd class="mt-1 text-sm text-gray-900">
                <% if @sale.receipt.attached? %>
                  <%= image_tag @sale.receipt, size: "400x400" %>
                <% end %>
              </dd>
            </div>
          <% end %>
        </dl>
      </div>
      <% if @sale.pending? && allowed_to?(:update?, @sale) %>
        <div class="px-4 py-5 sm:px-6 bg-gray-50">
          <div class="flex justify-end space-x-3">
            <%= form_with model: @sale, method: :put do |form| %>
              <%= form.hidden_field :status, value: "approved" %>
              <%= form.submit "Approve", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-lime-700 bg-lime-100 hover:bg-lime-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-lime-500" %>
            <% end %>
            <%= form_with model: @sale, method: :put do |form| %>
              <%= form.hidden_field :status, value: "declined" %>
              <%= form.submit "Decline", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    <%= component "card" do |card| %>
      <% card.with_header title: "Notes" %>
      <div class="px-4 py-6 sm:px-6">
        <ul role="list" class="space-y-8">
          <% @sale.notations.each do |note| %>
            <li>
              <div class="flex space-x-3">
                <div class="shrink-0">
                  <%= image_tag note.user.avatar_path, class: "h-10 w-10 rounded-full" %>
                </div>
                <div>
                  <div class="text-sm">
                    <%= link_to note.user.name, note.user, class: "font-medium text-gray-900" %>
                  </div>
                  <div class="mt-1 text-sm text-gray-700">
                    <%= note.content %>
                  </div>
                  <div class="mt-2 space-x-2 text-sm">
                    <span class="font-medium text-gray-500">
                      <%= time_ago_in_words note.created_at %>
                    </span>
                  </div>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
      <% if allowed_to?(:edit?, @sale) %>
        <% card.with_footer do %>
          <div class="flex space-x-3">
            <div class="shrink-0">
              <%= image_tag current_user.avatar_path, class: "h-10 w-10 rounded-full" %>
            </div>
            <div class="flex-1 min-w-0">
              <%= form_with model: @sale do |form| %>
                <div>
                  <%= form.fields_for :notations, Note.new do |fields| %>
                    <%= fields.label :content, class: "sr-only" %>
                    <%= fields.hidden_field :user_id, value: current_user.id %>
                    <%= fields.rich_text_area :content, rows: 3, class: "shadow-xs block w-full focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border border-gray-300 rounded-md bg-white" %>
                  <% end %>
                </div>
                <div class="flex items-center justify-between mt-3">
                  <span></span>
                  <%= form.submit "Add Note", class: "inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-zeiss-500 shadow-xs text-white bg-zeiss-500 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% end %>
  </div>
<% end %>
