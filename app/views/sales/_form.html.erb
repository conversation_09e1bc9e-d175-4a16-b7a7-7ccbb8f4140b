<% content_for :css do %>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<% end %>
<%= form_with(model: sale) do |form| %>
  <div class="bg-white rounded-lg shadow-sm">
    <div class="px-4 py-5 sm:px-6">
      <div>
        <%= render "shared/error_messages", resource: form.object %>
        <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-3">
            <%= form.label :product_id, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <% if sale.persisted? %>
                <%= sale.product.name %>
              <% else %>
                <div class="w-full max-w-xs" x-data="{query: '', selected: null, products: <%= @products %>, get filteredProducts() { return this.query === '' ? this.products : this.products.filter((product) => { return product.name.toLowerCase().includes(this.query.toLowerCase()) }) } }">
                  <div id="product" x-combobox x-model="selected">
                    <div class="relative mt-1 rounded-md focus-within:ring-2 focus-within:ring-zeiss-500">
                      <div class="flex items-center justify-between gap-2 w-full bg-white pl-5 pr-3 py-2.5 rounded-md shadow-xs border-gray-300 border">
                        <input type="text" autocomplete="off" class="p-0 border-none focus:outline-hidden focus:ring-0" x-combobox:input placeholder="Search..." :display-value="product => product.name" x-on:change="query = $event.target.value">
                        <%= form.hidden_field :product_id, "x-model": "selected.id" %>
                        <button x-combobox:button class="absolute inset-y-0 right-0 flex items-center pr-2">
                          <svg class="w-5 h-5 text-gray-500 shrink-0" viewBox="0 0 20 20" fill="none" stroke="currentColor"><path d="M7 7l3-3 3 3m0 6l-3 3-3-3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" /></svg>
                        </button>
                      </div>
                      <div x-combobox:options x-cloak class="absolute left-0 z-10 w-full max-w-xs mt-2 overflow-auto origin-top-right bg-white border border-gray-200 rounded-md shadow-md outline-hidden max-h-60" x-transition.out.opacity>
                        <ul class="divide-y divide-gray-100">
                          <template x-for="product in filteredProducts" :key="product.id" hidden>
                            <li x-combobox:option :value="product" :class="{'bg-cyan-500/10 text-gray-900': $comboboxOption.isActive, 'text-gray-500': !$comboboxOption.isActive }" class="flex items-center w-full gap-2 px-4 py-2 text-sm cursor-default" x-on:click="$fetch('/products/' + product.id + '/sale')" data-fetch-method="get">
                              <div>
                                <img :src="product.image_select_path" height="32" width="32">
                              </div>
                              <div>
                                <div x-text="product.name" class="w-48 truncate" :x-tooltip.overflow="product.name"></div>
                                <div x-text="product.sku"></div>
                              </div>
                            </li>
                          </template>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex justify-center mt-2 sm:hidden">
                  <%= component "modal", name: "Scan Barcode", id: "barcode_dialog" do |modal| %>
                    <% modal.with_button title: "Scan Barcode", click: "modalOpen = true", size: "medium" %>
                    <div class="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                      <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                          <h3 class="text-lg font-medium leading-6 text-gray-900" :id="$id('modal-title')">
                            Scan Barcode
                          </h3>
                          <div class="flex w-auto mt-2 space-y-4 h-60">
                            <div data-controller="barcode" x-on:scanned="modalOpen = false">
                              <video height="480"></video>
                              <canvas height="480" width="640"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
          <div class="sm:col-span-3">
            <div id="sale_product">
            </div>
            <div class="hidden text-sm text-gray-500" id="product_missing">
              We can't seem to find that product. Please try again or look manually. If you still cannot find it please contact support.
            </div>
          </div>
          <div class="sm:col-span-3">
            <%= form.label :serial_number, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
            <div class="mt-1">
              <% if sale.persisted? %>
                <%= sale.serial_number %>
              <% else %>
                <%= form.text_field :serial_number, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md", required: true %>
              <% end %>
            </div>
          </div>
          <div class="sm:col-span-3">
            <%= form.label :sold_at, "Sold On", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
            <div id="sold_on" class="relative max-w-lg mt-1 rounded-md shadow-xs sm:max-w-xs">
              <% if sale.persisted? %>
                <%= local_time sale.sold_at %>
              <% else %>
                <%= form.text_field :sold_at, class: "w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{maxDate: '#{Time.current}', dateFormat: 'Z', altInput: true}" %>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <%= icon name: "calendar", class: "h-5 w-5 text-gray-400" %>
                </div>
              <% end %>
            </div>
          </div>
          <div class="sm:col-span-3">
            <%= form.label :origin, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
            <div class="mt-1">
              <%= form.collection_radio_buttons :origin, Sale.origins, :first, :first do |b| %>
                <div class="flex items-center">
                  <%= b.label(class: "text-sm text-gray-600") { b.radio_button(class: "w-4 h-4 text-zeiss-600 border-gray-300 focus:ring-indigo-500 mr-2") + b.text.titleize } %>
                </div>
              <% end %>
            </div>
          </div>
          <% if Flipper.enabled?(:receipts, Current.country) %>
            <div class="sm:col-span-3">
              <%= form.label :receipt, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
              <div class="mt-1">
                <%= form.file_field :receipt, direct_upload: true, accept: "image/*", class: "block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50" %>
              </div>
            </div>
          <% end %>
          <% if sale.persisted? %>
            <div class="sm:col-span-2">
              <%= form.label :status, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
              <div class="mt-1">
                <% if allowed_to?(:edit?, sale) && sale.pending? %>
                  <%= form.select :status, Sale.statuses.keys.map {|s| [s.titleize, s]}, {}, class: "max-w-lg block focus:ring-zeiss-500 focus:border-zeiss-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                <% else %>
                  <%= sale.status.titleize %>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    <div class="px-4 py-5 sm:px-6 bg-gray-50">
      <div class="flex justify-end space-x-3">
        <%= link_to "Cancel", sales_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        <% if allowed_to?(:destroy?, form.object) && sale.persisted? %>
          <%= link_to "Delete", form.object, class: "bg-white py-2 px-4 border border-red-300 rounded-md shadow-xs text-sm font-medium text-red-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500", data: { confirm: "Are you sure?", turbo_method: :delete } %>
        <% end %>
      </div>
    </div>
  </div>
<% end %>
