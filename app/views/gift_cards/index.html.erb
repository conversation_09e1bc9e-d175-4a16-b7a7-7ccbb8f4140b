<%= component "page" do |page| %>
  <% page.with_header title: "Gift Cards" %>
  <div class="flex flex-col mt-8">
    <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full">
            <thead class="bg-white">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Product Name</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">Buyer</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Ordered On</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">Store</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white">
              <% @cards.each do |card| %>
                <tr class="border-t border-gray-300">
                  <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                    <%= link_to card.product.name, card.product %>
                    <dl class="font-normal lg:hidden">
                      <dt class="sr-only">Buyer</dt>
                      <dd class="mt-1 text-gray-500 truncate"><%= card.order.user.name %></dd>
                      <dt class="sr-only md:hidden">Ordered On</dt>
                      <dd class="mt-1 text-gray-500 truncate md:hidden"><%= card.order.created_at %></dd>
                      <dt class="sr-only sm:hidden">Store</dt>
                      <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= card.order.user.store.name %></dd>
                    </dl>
                  </td>
                  <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= card.order.user.name %></td>
                  <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= card.order.created_at %></td>
                  <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= card.order.user.store.name %></td>
                  <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                    <%= link_to icon(name: "eye", class: "w-5 h-5"), card.order, class: "text-zeiss-600 hover:text-zeiss-900" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="flex flex-col mt-8">
    <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full">
            <thead class="bg-white">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Created At</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">Begins</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Ends</th>
                <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">Order Count</th>
                <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">File</th>
              </tr>
            </thead>
            <tbody class="bg-white">
              <% @fulfillments.each do |fulfillment| %>
                <tr class="border-t border-gray-300">
                  <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                    <%= local_time fulfillment.created_at %>
                    <dl class="font-normal lg:hidden">
                      <dt class="sr-only">Begins</dt>
                      <dd class="mt-1 text-gray-500 truncate"><%= local_time fulfillment.begins_at %></dd>
                      <dt class="sr-only md:hidden">Ends</dt>
                      <dd class="mt-1 text-gray-500 truncate md:hidden"><%= local_time fulfillment.ends_at %></dd>
                      <dt class="sr-only sm:hidden">Order Count</dt>
                      <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= fulfillment.order_count %></dd>
                    </dl>
                  </td>
                  <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= local_time fulfillment.begins_at %></td>
                  <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= local_time fulfillment.ends_at %></td>
                  <td class="hidden px-3 py-4 text-sm text-center text-gray-500 whitespace-nowrap sm:table-cell"><%= fulfillment.order_count %></td>
                  <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap"><%= link_to fulfillment.csv.filename, fulfillment.csv %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
<% end %>
