<div x-data="{open: false}">
  <%= link_to "Adjust Points", new_user_adjustment_path(@user), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500", "@click": "open = true", "@click.prevent": "true" %>
  <div
    class="absolute inset-0 z-50 transition-opacity bg-gray-500 bg-opacity-75"
    aria-hidden="true"
    x-show="open"
    style="display: none;"
    x-transition:enter="ease-in-out duration-500"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="ease-in-out duration-500"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"></div>
  <%= form_with(model: [@user, adjustment]) do |f| %>
    <div class="fixed inset-y-0 right-0 z-50 flex max-w-full pl-10">
      <div
        class="w-screen max-w-md"
        x-show="open"
        style="display: none;"
        @click.outside="open = false"
        x-transition:enter="transition ease-in-out duration-500 sm:duration-700"
        x-transition:enter-start="translate-x-full"
        x-transition:enter-end="translate-x-0"
        x-transition:leave="transition ease-in-out duration-500 sm:duration-700"
        x-transition:leave-start="translate-x-0"
        x-transition:leave-end="translate-x-full">
        <div class="flex flex-col h-full bg-white divide-y divide-gray-200 shadow-xl">
          <div class="flex flex-col flex-1 min-h-0 py-6 overflow-y-scroll">
            <div class="px-4 sm:px-6">
              <div class="flex items-start justify-between">
                <h2 class="text-lg font-medium text-gray-900" id="slide-over-title">
                  New Adjustment
                </h2>
                <div class="flex items-center ml-3 h-7">
                  <button class="text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" @click="open = false">
                    <span class="sr-only">Close panel</span>
                    <!-- Heroicon name: outline/x -->
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div class="relative flex-1 px-4 mt-6 text-left sm:px-6">
              <!-- Content Goes Here -->
              <div class="grid grid-cols-1 gap-y-6 gap-x-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Sales Agent</label>
                  <div class="mt-1">
                    <%= user.name %>
                  </div>
                </div>
                <div>
                  <%= f.label :points, class: "block text-sm font-medium text-gray-700" %>
                  <div class="mt-1">
                    <%= f.number_field :points, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
                <div>
                  <%= f.label :notes, class: "block text-sm font-medium text-gray-700" %>
                  <div class="mt-1">
                    <%= f.text_area :notes, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
                <div>
                  <%= f.label :kind, class: "block text-sm font-medium text-gray-700" %>
                  <div class="mt-1">
                    <%= f.select :kind, Adjustment.kinds.keys.map {|a| [a.titleize, a]}, {}, class: "shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-end px-4 py-4 shrink-0">
            <%= f.submit "Create Adjustment", class: "ml-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>
