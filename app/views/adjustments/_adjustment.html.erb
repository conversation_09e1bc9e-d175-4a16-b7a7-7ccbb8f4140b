<div x-data="{open: false}">
  <%= link_to icon(name: "eye"), user_adjustments_path(adjustment.user, adjustment), class: "text-zeiss-600 hover:text-zeiss-900", "@click": "open = true", "@click.prevent": "true" %>
  <div
    class="absolute inset-0 z-50 transition-opacity bg-gray-500 bg-opacity-75"
    aria-hidden="true"
    x-show="open"
    style="display: none;"
    x-transition:enter="ease-in-out duration-500"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="ease-in-out duration-500"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"></div>
  <div class="fixed inset-y-0 right-0 z-50 flex max-w-full pl-10">
    <div
      class="w-screen max-w-md"
      x-show="open"
      style="display: none;"
      @click.outside="open = false"
      x-transition:enter="transition ease-in-out duration-500 sm:duration-700"
      x-transition:enter-start="translate-x-full"
      x-transition:enter-end="translate-x-0"
      x-transition:leave="transition ease-in-out duration-500 sm:duration-700"
      x-transition:leave-start="translate-x-0"
      x-transition:leave-end="translate-x-full">
      <div class="flex flex-col h-full bg-white divide-y divide-gray-200 shadow-xl">
        <div class="flex flex-col flex-1 min-h-0 py-6 overflow-y-scroll">
          <div class="px-4 sm:px-6">
            <div class="flex items-start justify-between">
              <h2 class="text-lg font-medium text-gray-900" id="slide-over-title">
                Adjustment <%= adjustment.id %>
              </h2>
              <div class="flex items-center ml-3 h-7">
                <button class="text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" @click="open = false">
                  <span class="sr-only">Close panel</span>
                  <!-- Heroicon name: outline/x -->
                  <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div class="relative flex-1 px-4 mt-6 text-left sm:px-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
              <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">
                  Adjustment Date
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= adjustment.created_at.strftime("%b %d, %Y") %>
                </dd>
              </div>
              <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">
                  Agent Name
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= adjustment.user.name %>
                </dd>
              </div>
              <div class="sm:col-span-2">
                <dt class="text-sm font-medium text-gray-500">
                  Note
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= adjustment.notes %>
                </dd>
              </div>
              <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">
                  Approved By
                </dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= adjustment.admin.name %>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
