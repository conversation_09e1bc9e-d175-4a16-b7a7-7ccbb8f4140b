<div class="divide-y divide-gray-200">
  <div class="px-4 py-5 sm:px-6">
    Adjustment #<%= item.id %>
  </div>
  <div class="px-4 py-5 sm:p-6">
    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Adjustment Date
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.created_at.strftime("%b %d, %Y") %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Agent Name
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.user.name %>
        </dd>
      </div>
      <div class="sm:col-span-2">
        <dt class="text-sm font-medium text-gray-500">
          Note
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.notes %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Approved By
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= item.admin.name %>
        </dd>
      </div>
    </dl>
  </div>
  <div class="px-4 py-4 sm:px-6 bg-gray-50 sm:flex sm:flex-row-reverse">
    <button type="button" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500 bg-zeiss-600 hover:bg-zeiss-700 shadow-zeiss-500/50" @click="modalOpen = false">
      Close
    </button>
  </div>
</div>
