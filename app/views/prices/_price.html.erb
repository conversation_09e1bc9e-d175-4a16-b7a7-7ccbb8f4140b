<tr id="<%= dom_id(price) %>">
  <td class="py-2 pl-4 pr-3 text-sm text-gray-500 whitespace-nowrap sm:pl-6">
    <span class="relative inline-block bg-no-repeat bg-contain before:content-['\00a0'] leading-6 w-9" style="background-image: url(<%= asset_path("flags/#{price.country.abbreviation.downcase}.svg") %>)"></span>
    <span class="sr-only"><%= price.country.name %></span>
  </td>
  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= "#{humanized_money_with_symbol(price.msrp)} #{price.currency}" %></td>
  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= price.points_needed %></td>
  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= price.points_earned %></td>
  <td class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
    <%= link_to "Edit", edit_product_price_path(price.product, price), class: "text-zeiss-600 hover:text-zeiss-900", "x-on:click.prevent": "modalOpen = true; $refs.price_form.src = $el.href" %>
  </td>
</tr>
<%= turbo_stream_from price %>
