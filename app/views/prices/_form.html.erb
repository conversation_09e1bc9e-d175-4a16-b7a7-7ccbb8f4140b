<%= form_with(model: [product, price]) do |form| %>
  <%= render "shared/error_messages", resource: form.object %>
  <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4">
    <div>
      <%= form.label :msrp, "MSRP", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
      <div class="relative mt-1 rounded-md shadow-xs" x-data>
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span class="text-gray-500 sm:text-sm">$</span>
        </div>
        <%= form.text_field :msrp, class: "block w-full rounded-md border-0 py-1.5 pl-7 pr-12 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6", "x-mask:dynamic": "$money($input)" %>
      </div>
    </div>
    <div>
      <%= form.label :points_needed, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
      <div class="mt-1">
        <%= form.text_field :points_needed, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
      </div>
    </div>
    <div>
      <%= form.label :points_earned, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
      <div class="mt-1">
        <%= form.text_field :points_earned, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
      </div>
    </div>
    <div class="col-span-3">
      <%= form.label :country_id, "Country", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
      <div class="mt-1">
        <% if form.object.persisted? %>
          <%= form.object.country.name %>
        <% else %>
          <%= form.collection_select :country_id, countries, :id, :name, {}, class: "max-w-lg block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
        <% end %>
      </div>
    </div>
  </div>
  <div class="pt-5">
    <div class="flex justify-end space-x-3">
      <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs shadow-zeiss-500 text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
    </div>
  </div>
<% end %>
