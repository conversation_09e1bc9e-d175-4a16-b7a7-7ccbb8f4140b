<% content_for :css do %>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slim-select/1.27.0/slimselect.min.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
<% end %>
<%= form_with model: @message do |form| %>
  <div class="bg-white rounded-lg shadow-sm">
    <div class="px-4 py-5 sm:px-6">
      <div>
        <%= render "shared/error_messages", resource: form.object %>
        <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-4">
            <%= form.label :subject, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.text_field :subject, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div class="sm:col-span-6">
            <%= form.label :body, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.rich_text_area :body %>
            </div>
          </div>
          <div class="sm:col-span-3">
            <%= form.label :recipient_sgid, "Recipients", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.grouped_collection_select :recipient_sgid, [Brand, Region, StoreChain, Store], :order_by_name, :group_name, :to_sgid, :name_with_city, {include_blank: true}, class: "block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm rounded-md", "x-slimselect": true %>
            </div>
          </div>
          <div class="sm:col-span-3">
            <%= form.label :send_on, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.text_field :send_on, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{altInput: true, minDate: '#{Time.current.utc}', altFormat: 'Z'}" %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="px-4 py-5 sm:px-6 bg-gray-50">
      <div class="flex justify-end space-x-3">
        <%= link_to "Cancel", messages_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        <%= form.submit "Send", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      </div>
    </div>
  </div>
<% end %>
