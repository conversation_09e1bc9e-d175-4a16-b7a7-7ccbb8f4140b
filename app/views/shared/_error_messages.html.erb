<% if resource.errors.any? %>
  <div class="p-4 mb-6 border border-red-300 rounded-md shadow-sm bg-red-50 col-span-full shadow-red-500" role="alert">
    <div class="flex">
      <div class="shrink-0">
        <%= icon(name: "times-circle", weight: :solid, class: "w-5 h-5 text-red-400") %>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-red-800">
          <%= t("errors.messages.not_saved", count: resource.errors.size) %>
        </h3>
        <div class="mt-2 text-sm text-red-700">
          <ul class="pl-5 space-y-1 list-disc">
            <% resource.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  </div>
<% end %>
