<%= form_with(model: store) do |form| %>
  <%= render "shared/error_messages", resource: form.object %>
  <div class="space-y-6">
    <%= component "card" do %>
      <div class="grid grid-cols-2 gap-4">
        <div class="col-span-2">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.text_field :name, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
        <div>
          <%= form.label :account_number, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.text_field :account_number, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
        <div>
          <%= form.label :brands, class: "block text-sm font-medium text-gray-700" %>
          <fieldset class="mt-4">
            <div class="space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
              <%= form.collection_radio_buttons :brand_id, Brand.all, :id, :name, include_hidden: false do |b| %>
                <div class="flex items-center">
                  <%= b.radio_button(class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300") %>
                  <%= b.label(class: "ml-3 block text-sm leading-6 font-medium text-gray-900") %>
                </div>
              <% end %>
            </div>
          </fieldset>
        </div>
      </div>
    <% end %>
    <%= component "card" do %>
      <div class="grid grid-cols-3 gap-4" x-data>
        <%= form.fields_for :address do |address| %>
          <div class="col-span-3">
            <%= address.label :line1, "Street Address", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= address.text_field :line1, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div class="col-span-3">
            <%= address.label :line2, "Unit/Apartment etc.", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= address.text_field :line2, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div>
            <%= address.label :city, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= address.text_field :city, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div>
            <%= address.label :state_id, "State", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= address.collection_select :state_id, State.order(:name).where(country: Current.country), :id, :name, {}, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div>
            <%= address.label :zip_code, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= address.text_field :zip_code, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <%= address.hidden_field :country_id, value: Current.country.id %>
        <% end %>
        <div>
          <%= form.label :phone_number, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.text_field :phone_number, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-mask": "(*************" %>
          </div>
        </div>
        <div class="col-start-3">
          <%= form.label :status, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.select :status, Store.statuses.keys.map { |s| [s.titleize, s] }, {}, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  <div class="pt-5">
    <div class="flex justify-end">
      <%= link_to "Cancel", stores_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
    </div>
  </div>
<% end %>
