<%= component "page" do |page| %>
  <% page.with_header title: "Stores" do |header| %>
    <% header.with_actions([
      {title: "New Store", url: new_store_path, primary: true, visible: allowed_to?(:create?, Store)},
    ]) %>
  <% end %>
  <%= component "page/filter", name: "Stores", sort_options: [{label: "Brand", option: "brand_name"}, {label: "Store Chain", option: "store_chain"}, {label: "State", option: "state_name"}, {label: "Region", option: "region_name"}] do |filter| %>
    <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :stores, filter_field: "brand_name") %>
    <% filter.with_filter_collection(name: "State", collection: State.order(:name).where(country: Current.country), name_method: :name, value_method: :abbreviation, klass: :stores, filter_field: "state_abbr") %>
    <% filter.with_filter_collection(name: "Status", collection: Store.statuses, name_method: :first, value_method: :first, klass: :stores, filter_field: "status") %>
    <% filter.with_filter_collection(name: "Country", collection: Country.all, name_method: :name, value_method: :name, klass: :stores, filter_field: "country_name") %>
  <% end %>
  <%= turbo_frame_tag "stores_results", target: "_top" do %>
    <div class="flex flex-col mt-8">
      <div class="mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full">
              <thead class="bg-white">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:table-cell">Region</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:table-cell">Brand</th>
                  <th scope="col" class="hidden py-3.5 px-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 sm:table-cell">Chain</th>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Users</th>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <% @grouped_stores.keys.each do |key| %>
                  <tr class="border-t border-gray-200">
                    <th class="px-4 py-2 text-sm font-semibold text-left text-gray-900 bg-gray-50 sm:px-6" colspan="5" scope="colgroup"><%= key.try(:titleize) || "Independent" %></th>
                  </tr>
                  <% @grouped_stores[key].each do |store| %>
                    <tr class="border-t border-gray-300">
                      <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                        <%= link_to store.name, store %>
                        <div class="text-gray-500">
                          <%= store.address.full_address %>
                        </div>
                        <dl class="font-normal lg:hidden">
                          <dt class="sr-only">Region</dt>
                          <dd class="mt-1 text-gray-500 truncate"><%= store.address.state.region.name %></dd>
                          <dt class="sr-only md:hidden">Brand</dt>
                          <dd class="mt-1 text-gray-500 truncate md:hidden"><%= store.brand_name %></dd>
                          <dt class="sr-only sm:hidden">Chain</dt>
                          <dd class="mt-1 text-gray-500 truncate sm:hidden"><%= store.store_chain_name.presence %></dd>
                        </dl>
                      </td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap lg:table-cell"><%= store.address.state.region.name %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap md:table-cell"><%= store.brand_name %></td>
                      <td class="hidden px-3 py-4 text-sm text-gray-500 whitespace-nowrap sm:table-cell"><%= store.store_chain_name.presence %></td>
                      <td class="px-3 py-4 text-sm text-center text-gray-500 whitespace-nowrap"><%= store.users_count %></td>
                      <td class="px-3 py-4 text-sm text-center text-gray-500 whitespace-nowrap"><%= store.status.titleize %></td>
                    </tr>
                  <% end %>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <div class="px-4 py-4 sm:px-6">
    <%= component "page/pagination", pagy: @pagy %>
  </div>
<% end %>
