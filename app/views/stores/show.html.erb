<%= component "page" do |page| %>
  <% page.with_header title: @store.name do |header| %>
    <% header.with_actions([
      {title: "Edit", url: edit_store_path(@store), visible: allowed_to?(:edit?, @store), primary: true}
    ]) %>
  <% end %>
  <%= component "card" do %>
    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Account Number
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= @store.account_number || "Not set" %>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Address
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <address>
            <%= @store.address.line1 %><br>
            <%= @store.address.line2 %><br>
            <%= @store.address.city %>, <%= @store.address.state.name %> <%= @store.address.zip_code %>
          </address>
        </dd>
      </div>
      <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">
          Phone Number
        </dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= number_to_phone(@store.phone_number, area_code: true) || "Not set" %>
        </dd>
      </div>
    </dl>
  <% end %>
  <div class="pb-5 my-4 border-b border-gray-200">
    <h3 class="text-lg font-medium leading-6 text-gray-900">Users</h3>
  </div>
  <div class="flex flex-col">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Email</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Phone Number</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @store.users.each do |user| %>
                <tr>
                  <td class="py-2 pl-4 pr-3 text-sm text-gray-900 whitespace-nowrap sm:pl-6"><%= link_to user.name, user %></td>
                  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= user.email %></td>
                  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= number_to_phone(user.phone_number, area_code: true) %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="pb-5 my-4 border-b border-gray-200">
    <h3 class="text-lg font-medium leading-6 text-gray-900">Promotions</h3>
  </div>
  <div class="flex flex-col">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow-sm ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Products</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Multiplier</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Starts</th>
                <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Ends</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @store.promotions.each do |promotion| %>
                <tr class="<%= promotion.active? ? "bg-green-50" : "" %>">
                  <td class="py-2 pl-4 pr-3 text-sm text-gray-500 whitespace-nowrap sm:pl-6">
                    <% promotion.products.each do |product| %>
                      <%= link_to product.name, product %>
                    <% end %>
                  </td>
                  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= promotion.multiplier %></td>
                  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= local_time promotion.starts_at %></td>
                  <td class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"><%= local_time promotion.ends_at %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
<% end %>
