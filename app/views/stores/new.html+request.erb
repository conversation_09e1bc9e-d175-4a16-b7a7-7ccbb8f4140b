<div class="overflow-scroll bg-white sm:rounded-lg shadow-sm max-h-full sm:max-h-[46rem]">
  <div class="px-4 py-5 sm:p-6">
    <div class="pb-5 mb-2 border-b border-gray-200">
      <h3 class="text-base font-semibold leading-6 text-gray-900">Request Store</h3>
    </div>
    <%= form_with(model: @store) do |form| %>
      <%= render "shared/error_messages", resource: form.object %>
      <div class="space-y-6">
        <%= component "card" do %>
          <div class="grid grid-cols-2 gap-4">
            <div class="col-span-2">
              <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= form.text_field :name, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>
            <div>
              <%= form.label :brands, class: "block text-sm font-medium text-gray-700" %>
              <div class="mx-auto mt-1 w-fit">
                <%= form.collection_radio_buttons :brand_id, Brand.all, :id, :name, include_hidden: false do |b| %>
                  <div class="flex items-start">
                    <%= b.radio_button(class: "focus:ring-zeiss-500 h-4 w-4 text-zeiss-600 border-gray-300 rounded-full") %>
                    <div class="ml-3 text-sm">
                      <%= b.label(class: "font-medium text-gray-700") %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
        <%= component "card" do %>
          <div class="grid grid-cols-3 gap-4">
            <%= form.fields_for :address do |address| %>
              <div class="col-span-3">
                <%= address.label :line1, "Street Address", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= address.text_field :line1, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
              <div class="col-span-3">
                <%= address.label :line2, "Unit/Apartment etc.", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= address.text_field :line2, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
              <div>
                <%= address.label :city, class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= address.text_field :city, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
              <div>
                <%= address.label :state_id, "State", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= address.collection_select :state_id, State.order(:name).where(country: Current.country), :id, :name, {}, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
              <div>
                <%= address.label :zip_code, class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= address.text_field :zip_code, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
              <%= address.hidden_field :country_id, value: Current.country.id %>
            <% end %>
            <div class="col-span-2">
              <%= form.label :phone_number, class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= form.text_field :phone_number, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      <%= form.hidden_field :status, value: :request %>
      <div class="pt-5">
        <div class="flex items-center justify-end gap-x-6">
          <%= link_to "Cancel", root_path, class: "text-sm font-semibold leading-6 text-gray-900" %>
          <%= form.submit "Request Store", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
