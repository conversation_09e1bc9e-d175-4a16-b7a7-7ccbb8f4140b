<!DOCTYPE html>
<html class="h-full">
  <%= render "application/head" %>
  <body class="h-full">
    <div class="flex min-h-full bg-white">
      <div class="flex flex-col justify-center flex-1 px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div class="w-full max-w-sm mx-auto lg:w-96">
          <%= turbo_frame_tag "account" do %>
            <%= yield %>
          <% end %>
        </div>
      </div>
      <div class="relative flex-1 hidden w-0 lg:block">
        <%= image_tag get_random_background("account"), class: "absolute inset-0 h-full w-full object-cover" %>
      </div>
    </div>
    <% flash.each do |type, message| %>
      <%= render Flash::Component.new type: type, message: message %>
    <% end %>
  </body>
</html>
