<!DOCTYPE html>
<html class="h-full antialiased">
  <%= render "application/head" %>
  <body class="h-full">
    <div class="min-h-full bg-gray-100">
      <div x-data="{ sidebarOpen: false }">
        <%= component "sidebar", admin: current_user.admin? || current_user.super_admin? do |sidebar| %>
          <% sidebar.with_links([
          {
            name: "Dashboard",
            url: root_path,
            link_icon: "gauge-simple",
            visible: :dashboard
          },
          {
            name: "Sales",
            url: sales_path,
            link_icon: "cash-register",
            visible: :sale,
          },
          {
            name: "Orders",
            url: orders_path,
            link_icon: "receipt",
            visible: :order
          },
          {
            name: "Point Shop",
            url: shop_path,
            link_icon: "bag-shopping",
            visible: :shop
          },
          {
            name: "Help",
            url: help_path,
            link_icon: "question",
            visible: :dashboard
          }
        ]) %>
          <% sidebar.with_admin_links([
          {
            name: "Users",
            url: users_path,
            link_icon: "users",
            visible: :user
          },
          {
            name: "Products",
            url: products_path,
            link_icon: "boxes-stacked",
            visible: :product
          },
          {
            name: "Promotions",
            url: promotions_path,
            link_icon: "percent",
            visible: :promotion
          },
          {
            name: "Categories",
            url: categories_path,
            link_icon: "sitemap",
            visible: :category
          },
          {
            name: "Regions",
            url: regions_path,
            link_icon: "earth-americas",
            visible: :region
          },
          {
            name: "Stores",
            url: stores_path,
            link_icon: "store",
            visible: :store
          },
          {
            name: "Store Chains",
            url: store_chains_path,
            link_icon: "link",
            visible: :store_chain
          },
          {
            name: "Messages",
            url: messages_path,
            link_icon: "message",
            visible: :message
          },
          {
            name: "Gift Cards",
            url: gift_cards_path,
            link_icon: "gift-card",
            visible: :gift_card
          },
          {
            name: "Reports",
            url: reports_path,
            link_icon: "file-chart-pie",
            visible: :report
          }
        ]) %>
        <% end %>
        <div class="flex flex-col flex-1 md:pl-64">
          <%= component "searchbar" %>
          <%= component "impersonating" %>
          <main class="flex-1">
            <%= yield %>
          </main>
        </div>
      </div>
      <div id="flash">
        <%= render "flash" %>
      </div>
    </div>
  </body>
</html>
