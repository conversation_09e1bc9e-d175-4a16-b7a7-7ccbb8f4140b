<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
  <head>
    <meta charset="utf-8">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
      </style>
    <![endif]-->
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;700&display=swap" rel="stylesheet" media="screen">
    <style>
      /* Your custom CSS resets for email */
      body {
        margin: 0;
        width: 100%;
        padding: 0;
        word-break: break-word;
        -webkit-font-smoothing: antialiased;
      }
      img {
        border: 0;
        max-width: 100%;
        vertical-align: middle;
        line-height: 100%;
      }
      /* Tailwind components that are generated by plugins */
      /**
       * @import here any custom components - classes that you'd want loaded
       * before the Tailwind utilities, so that the utilities could still
       * override them.
      */
      .button {
        display: inline-block;
        color: #ffffff;
        text-decoration: none;
        background-color: #3869d4;
        border-top: 10px solid #3869d4;
        border-right: 18px solid #3869d4;
        border-bottom: 10px solid #3869d4;
        border-left: 18px solid #3869d4;
        border-radius: 3px;
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16);
      }
      .button--green {
        background-color: #22bc66;
        border-top: 10px solid #22bc66;
        border-right: 18px solid #22bc66;
        border-bottom: 10px solid #22bc66;
        border-left: 18px solid #22bc66;
      }
      .button--red {
        background-color: #ff6136;
        border-top: 10px solid #ff6136;
        border-right: 18px solid #ff6136;
        border-bottom: 10px solid #ff6136;
        border-left: 18px solid #ff6136;
      }
      @media (max-width: 600px) {
        .button {
          width: 100% !important;
        }
        .button {
          text-align: center !important;
        }
      }
      .purchase_heading {
        border-bottom-width: 1px;
        border-bottom-color: #eaeaec;
        border-bottom-style: solid;
      }
      .purchase_heading p {
        margin: 0;
        font-size: 12px;
        line-height: 24px;
        color: #85878e;
      }
      .purchase_footer {
        padding-top: 16px;
        vertical-align: middle;
        font-size: 16px;
        border-top-width: 1px;
        border-top-color: #eaeaec;
        border-top-style: solid;
      }
      .body-sub {
        margin-top: 25px;
        border-top-width: 1px;
        padding-top: 25px;
        border-top-color: #eaeaec;
        border-top-style: solid;
      }
      .discount {
        width: 100%;
        background-color: #f4f4f7;
        padding: 24px;
        border: 2px dashed #cbcccf;
      }
      @media (prefers-color-scheme: dark) {
        body, .email-body, .email-body_inner, .email-content, .email-wrapper, .email-masthead, .email-footer {
          background-color: #333333 !important;
        }
        body, .email-body, .email-body_inner, .email-content, .email-wrapper, .email-masthead, .email-footer {
          color: #ffffff !important;
        }
      
        p, ul, ol, blockquote, h1, h2, h3 {
          color: #ffffff !important;
        }
      
        .attributes_content, .discount {
          background-color: #222222 !important;
        }
      
        .email-masthead_name {
          text-shadow: none !important;
        }
      }
      /* Tailwind utility classes */
      .m-0 {
        margin: 0 !important;
      }
      .mx-auto {
        margin-left: auto !important;
        margin-right: auto !important;
      }
      .my-30 {
        margin-top: 30px !important;
        margin-bottom: 30px !important;
      }
      .mt-6 {
        margin-top: 6px !important;
      }
      .mb-20 {
        margin-bottom: 20px !important;
      }
      .mt-0 {
        margin-top: 0 !important;
      }
      .mb-21 {
        margin-bottom: 21px !important;
      }
      .block {
        display: block !important;
      }
      .table {
        display: table !important;
      }
      .hidden {
        display: none !important;
      }
      .w-570 {
        width: 570px !important;
      }
      .w-full {
        width: 100% !important;
      }
      .w-4\/5 {
        width: 80% !important;
      }
      .w-1\/5 {
        width: 20% !important;
      }
      .bg-gray-postmark-lighter {
        background-color: #f2f4f6 !important;
      }
      .bg-white {
        background-color: #ffffff !important;
      }
      .bg-gray-postmark-lightest {
        background-color: #f4f4f7 !important;
      }
      .p-45 {
        padding: 45px !important;
      }
      .p-16 {
        padding: 16px !important;
      }
      .py-25 {
        padding-top: 25px !important;
        padding-bottom: 25px !important;
      }
      .py-35 {
        padding-top: 35px !important;
        padding-bottom: 35px !important;
      }
      .py-10 {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
      }
      .pt-25 {
        padding-top: 25px !important;
      }
      .pb-8 {
        padding-bottom: 8px !important;
      }
      .pr-16 {
        padding-right: 16px !important;
      }
      .pb-16 {
        padding-bottom: 16px !important;
      }
      .text-left {
        text-align: left !important;
      }
      .text-center {
        text-align: center !important;
      }
      .text-right {
        text-align: right !important;
      }
      .font-sans {
        font-family: "Nunito Sans", -apple-system, "Segoe UI", sans-serif !important;
      }
      .text-base {
        font-size: 16px !important;
      }
      .text-xs {
        font-size: 13px !important;
      }
      .text-2xl {
        font-size: 24px !important;
      }
      .text-sm {
        font-size: 14px !important;
      }
      .text-xxs {
        font-size: 12px !important;
      }
      .font-bold {
        font-weight: 700 !important;
      }
      .leading-24 {
        line-height: 24px !important;
      }
      .text-gray-postmark-light {
        color: #a8aaaf !important;
      }
      .text-gray-postmark-darker {
        color: #333333 !important;
      }
      .text-gray-postmark-dark {
        color: #51545e !important;
      }
      .text-blue-postmark {
        color: #3869d4 !important;
      }
      .text-gray-postmark-meta {
        color: #85878e !important;
      }
      .no-underline {
        text-decoration: none !important;
      }
      /* Your custom utility classes */
      .mso-leading-exactly {
        mso-line-height-rule: exactly;
      }
      @media (max-width: 600px) {
        .sm\:w-full {
          width: 100% !important;
        }
      }
    </style>
  </head>
  <body>
    <div role="article" aria-roledescription="email" aria-label="" lang="en">
      <table class="w-full font-sans email-wrapper bg-gray-postmark-lighter" cellpadding="0" cellspacing="0" role="presentation">
        <tr>
          <td align="center">
            <table class="w-full email-content" cellpadding="0" cellspacing="0" role="presentation">
              <tr>
                <td align="center" class="text-base text-center email-masthead py-25">
                  <a href="https://zeisspoints.com" class="text-base font-bold no-underline email-masthead_name text-gray-postmark-light" style="text-shadow: 0 1px 0 #ffffff">
                    ZEISS Points
                  </a>
                </td>
              </tr>
              <%= yield %>
              <tr>
                <td>
                  <table align="center" class="mx-auto text-center email-footer w-570 sm:w-full" cellpadding="0" cellspacing="0" role="presentation">
                    <tr>
                      <td align="center" class="text-base content-cell p-45">
                        <p class="mt-6 mb-20 text-xs text-center leading-24 text-gray-postmark-light">&copy; 2021 ZEISS Points. All rights reserved.</p>
                        <p class="mt-6 mb-20 text-xs text-center leading-24 text-gray-postmark-light">
                          Carl Zeiss Sports Optics
                          <br>
                          35576 Wetzlar
                          <br>
                          Germany
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>
