<!DOCTYPE html>
<html class="h-full antialiased">
  <%= render "application/head" %>
  <body class="flex flex-col h-full min-h-screen font-sans antialiased font-normal leading-normal bg-gray-100">
    <div class="flex-1">
      <main class="min-h-screen bg-center bg-cover" style="background-image: url('<%= image_path("internal_server_error.jpg") %>');">
        <div class="px-4 py-16 mx-auto text-center max-w-7xl sm:px-6 sm:py-24 lg:px-8 lg:py-48">
          <p class="text-sm font-semibold tracking-wide text-gray-400 text-opacity-50 uppercase">500 error</p>
          <h1 class="mt-2 text-4xl font-extrabold tracking-tight text-white sm:text-5xl">Uh oh! Something broke.</h1>
          <p class="mt-2 text-lg font-medium text-gray-400 text-opacity-50">We have been notified and working to fix it as soon as possible.</p>
          <div class="mt-6">
            <a href="/" class="inline-flex items-center px-4 py-2 text-sm font-medium text-black text-opacity-75 bg-white bg-opacity-75 border border-transparent rounded-md sm:bg-opacity-25 sm:hover:bg-opacity-50">
              Go back home
            </a>
          </div>
        </div>
      </main>
    </div>
  </body>
</html>
