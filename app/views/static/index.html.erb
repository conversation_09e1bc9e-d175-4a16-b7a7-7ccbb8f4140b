<div class="lg:grid lg:grid-cols-12 lg:gap-8">
  <div class="px-4 lg:col-span-6 sm:px-6 lg:flex lg:items-center lg:text-left">
    <div>
      <h1 class="mt-4 text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">Take control of your points</h1>
      <p class="mt-3 text-base text-gray-300 sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">If you are an authorized ZEISS Sports Optics or Photo dealer, manage your points by creating an account.</p>
    </div>
  </div>
  <div class="mt-16 sm:mt-24 lg:col-span-6 lg:mt-0">
    <div class="bg-white sm:mx-auto sm:w-full sm:max-w-md sm:rounded-lg sm:overflow-hidden">
      <div class="px-4 py-8 sm:px-10">
        <div>
          <%= form_with url: new_user_registration_path, method: :get, class: "space-y-4" do |f| %>
            <div>
              <%= f.label :store_name, class: "sr-only" %>
              <%= f.text_field :store_name, required: true, placeholder: "Store Name or City", class: "block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
            </div>
            <div>
              <%= f.submit "Find Your Store", class: "flex w-full justify-center rounded-md border border-transparent bg-zeiss-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2" %>
            </div>
          <% end %>
        </div>
        <div class="relative mt-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 text-gray-500 bg-white">Or</span>
          </div>
        </div>
        <div class="mt-6">
          <%= form_with model: resource, as: resource_name, url: session_path(resource_name), class: "space-y-4" do |f| %>
            <div>
              <%= f.label :email, class: "sr-only" %>
              <%= f.email_field :email, auto_focus: true, required: true, autocomplete: :email, placeholder: "Email", class: "block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
            </div>
            <div>
              <%= f.label :password, class: "sr-only" %>
              <%= f.password_field :password, required: true, autocomplete: "current-password", placeholder: "Password", class: "block w-full shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "h-4 w-4 rounded-sm border-gray-300 text-zeiss-600 focus:ring-zeiss-600" %>
                <%= f.label :remember_me, class: "ml-3 block text-sm leading-6 text-gray-700" %>
              </div>
              <div class="text-sm leading-6">
                <%= link_to "Forgot your password?", new_user_password_path, class: "font-semibold text-zeiss-600 hover:text-zeiss-500" %>
              </div>
            </div>
            <div>
              <%= f.submit "Sign In", class: "flex w-full justify-center rounded-md border border-transparent bg-zeiss-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2" %>
            </div>
          <% end %>
        </div>
      </div>
      <div class="px-4 py-6 border-t-2 border-gray-200 bg-gray-50 sm:px-10">
        <p class="text-xs leading-5 text-gray-500">By signing up, you agree to our <%= link_to "Terms", terms_path, class: "font-medium text-gray-900 hover:underline" %> and <%= link_to "Privacy Policy", privacy_path, class: "font-medium text-gray-900 hover:underline" %>.</p>
      </div>
    </div>
  </div>
</div>
