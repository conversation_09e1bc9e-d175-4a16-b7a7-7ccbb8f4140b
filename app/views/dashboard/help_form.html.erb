<main>
  <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
    <%= form_with(model: @help, url: help_path, method: :post) do |f| %>
      <div class="overflow-hidden bg-white divide-y divide-gray-200 rounded-lg shadow-sm">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex flex-wrap items-center justify-between -mt-2 -ml-4 sm:flex-nowrap">
            <div class="mt-2 ml-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                Help
              </h3>
            </div>
          </div>
        </div>
        <div class="px-4 py-5 sm:px-6">
          <div class="space-y-8 divide-y divide-gray-200 sm:space-y-5">
            <div>
              <div class="mt-6 space-y-6 sm:mt-5 sm:space-y-5">
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                  <%= f.label :name, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                  <div class="mt-1 sm:mt-0 sm:col-span-2">
                    <%= f.text_field :name, autocomplete: "name", placeholder: true, class: "max-w-lg block focus:ring-blue-500 focus:border-blue-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                  <%= f.label :email, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                  <div class="mt-1 sm:mt-0 sm:col-span-2">
                    <%= f.email_field :email, autocomplete: "email", placeholder: true, class: "max-w-lg block focus:ring-blue-500 focus:border-blue-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                  <%= f.label :help_type, "Type of Help", class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                  <div class="mt-1 sm:mt-0 sm:col-span-2">
                    <%= f.select :help_type, [['I need help','I need help'], ['I have a question','I have a question'], ['I have a suggestion']], {}, class: "max-w-lg block focus:ring-blue-500 focus:border-blue-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                  <%= f.label :message, class: "block text-sm font-medium text-gray-700 sm:mt-px sm:pt-2" %>
                  <div class="mt-1 sm:mt-0 sm:col-span-2">
                    <%= f.text_area :message, autocomplete: "email", placeholder: true, class: "max-w-lg block focus:ring-blue-500 focus:border-blue-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>
              </div>
              <div class="px-4 py-5 sm:px-6">
                <div class="flex justify-end">
                  <%= f.submit "Send", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-xs text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</main>
