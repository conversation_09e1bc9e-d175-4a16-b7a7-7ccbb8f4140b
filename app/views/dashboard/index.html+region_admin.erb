<%= render Page::Component.new do %>
  <div class="mb-3 lg:flex lg:items-center lg:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Regional Dashboard - <%= current_user.admin_region&.name || "All Regions" %>
      </h2>
      <div class="flex flex-col mt-1 sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <%= icon name: "user-tag", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
          <%= current_user.role.titleize %>
        </div>
        <% if current_user.admin_region.present? %>
          <div class="flex items-center mt-2 text-sm text-gray-500">
            <%= icon name: "earth-americas", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
            <%= current_user.admin_region.name %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Regional Performance Overview -->
  <div class="mb-8">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Regional Performance</h3>
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Total Stores in Region -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-blue-500">
            <%= icon name: "store", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Active Stores</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <% if current_user.admin_region.present? %>
              <%= current_user.admin_region.stores.active.count %>
            <% else %>
              <%= Store.active.count %>
            <% end %>
          </p>
        </dd>
      </div>

      <!-- Active Users in Region -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-green-500">
            <%= icon name: "users", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Active Users</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <% if current_user.admin_region.present? %>
              <%= User.joins(store: [address: [state: :region]]).where(regions: {id: current_user.admin_region.id}, status: :active).count %>
            <% else %>
              <%= User.active_status.count %>
            <% end %>
          </p>
        </dd>
      </div>

      <!-- Pending Sales -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-orange-500">
            <%= icon name: "clock", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Pending Sales</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <% if current_user.admin_region.present? %>
              <%= Sale.pending.where(region: current_user.admin_region).count %>
            <% else %>
              <%= Sale.pending.count %>
            <% end %>
          </p>
        </dd>
      </div>

      <!-- Monthly Sales -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-purple-500">
            <%= icon name: "trending-up", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Sales This Month</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <% if current_user.admin_region.present? %>
              <%= Sale.where(region: current_user.admin_region, created_at: Date.current.beginning_of_month..Date.current.end_of_month).count %>
            <% else %>
              <%= Sale.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count %>
            <% end %>
          </p>
        </dd>
      </div>
    </div>
  </div>

  <!-- Quick Actions for Region Admin -->
  <div class="mb-8">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Management Tools</h3>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <%= link_to users_path, class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "users", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Manage Users</span>
        </div>
      <% end %>
      
      <%= link_to stores_path, class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "store", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Manage Stores</span>
        </div>
      <% end %>
      
      <%= link_to sales_path, class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "dollar-sign", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Review Sales</span>
        </div>
      <% end %>
      
      <%= link_to orders_path, class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "package", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Review Orders</span>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Regional Analytics -->
  <div class="grid grid-cols-1 gap-4 md:grid-cols-2 mb-8">
    <!-- Top Performing Stores -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Top Performing Stores</h3>
        <div class="space-y-3">
          <% top_stores = if current_user.admin_region.present?
                           Store.joins(:sales, address: [state: :region])
                                .where(regions: {id: current_user.admin_region.id})
                                .where(sales: {created_at: Date.current.beginning_of_month..Date.current.end_of_month})
                                .group('stores.name')
                                .order('COUNT(sales.id) DESC')
                                .limit(5)
                                .count
                         else
                           Store.joins(:sales)
                                .where(sales: {created_at: Date.current.beginning_of_month..Date.current.end_of_month})
                                .group('stores.name')
                                .order('COUNT(sales.id) DESC')
                                .limit(5)
                                .count
                         end %>
          <% top_stores.each do |store_name, sales_count| %>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-900"><%= store_name %></span>
              <span class="text-sm text-gray-500"><%= sales_count %> sales</span>
            </div>
          <% end %>
          <% if top_stores.empty? %>
            <p class="text-sm text-gray-500">No sales data available for this month.</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Recent Activity</h3>
        <div class="space-y-3">
          <% recent_sales = if current_user.admin_region.present?
                             Sale.includes(:user, :product)
                                 .where(region: current_user.admin_region)
                                 .order(created_at: :desc)
                                 .limit(5)
                           else
                             Sale.includes(:user, :product)
                                 .order(created_at: :desc)
                                 .limit(5)
                           end %>
          <% recent_sales.each do |sale| %>
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm font-medium text-gray-900"><%= sale.product.name %></p>
                <p class="text-xs text-gray-500">by <%= sale.user.name %></p>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-900"><%= sale.status.titleize %></p>
                <p class="text-xs text-gray-500"><%= time_ago_in_words(sale.created_at) %> ago</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Analytics -->
  <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm">
      <%= pie_chart pending_count_sales_path(format: :json), empty: "No pending sales", title: "Pending Sales by Status" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm">
      <%= pie_chart pending_count_orders_path(format: :json), empty: "No pending orders", title: "Pending Orders by Status" %>
    </div>
  </div>
<% end %>
