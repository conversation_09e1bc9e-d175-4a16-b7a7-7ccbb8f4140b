<%= render Page::Component.new do %>
  <div class="mb-3 lg:flex lg:items-center lg:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Welcome <%= current_user.name %>
      </h2>
      <div class="flex flex-col mt-1 sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <%= icon name: "user-tag", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
          <%= current_user.role.titleize %>
        </div>
        <% if current_user.admin_brand.present? %>
          <div class="flex items-center mt-2 text-sm text-gray-500">
            <%= icon name: "bags-shopping", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
            <%= current_user.admin_brand.name.titleize %>
          </div>
        <% end %>
        <% if current_user.admin_region.present? %>
          <div class="flex items-center mt-2 text-sm text-gray-500">
            <%= icon name: "earth-americas", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
            <%= current_user.admin_region.name %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
    <div class="md:col-span-4">
      <h3 class="text-lg font-medium leading-6 text-gray-900">
        Statistics
      </h3>
      <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-2 xl:grid-cols-4">
        <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
          <dt>
            <div class="absolute p-3 text-center rounded-md bg-zeiss-500">
              <%= icon name: "users", class: "h-6 w-6 text-white" %>
            </div>
            <p class="ml-16 text-sm font-medium text-gray-500 truncate">All Users / New Users Last Month</p>
          </dt>
          <dd class="flex items-baseline pb-2 ml-16">
            <p class="text-2xl font-semibold text-gray-900">
              <%= @total_users %> / <%= @users_in_the_last_month %>
            </p>
          </dd>
        </div>
        <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
          <dt>
            <div class="absolute p-3 text-center rounded-md bg-zeiss-500">
              <%= icon name: "store", class: "text-white h-6 w-6" %>
            </div>
            <p class="ml-16 text-sm font-medium text-gray-500 truncate">All Stores / New Stores Last Month</p>
          </dt>
          <dd class="flex items-baseline pb-2 ml-16">
            <p class="text-2xl font-semibold text-gray-900">
              <%= @total_stores %> / <%= @stores_in_the_last_month %>
            </p>
          </dd>
        </div>
        <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
          <dt>
            <div class="absolute p-3 text-center rounded-md bg-zeiss-500">
              <%= icon name: "dollar-sign", class: "text-white h-6 w-6" %>
            </div>
            <p class="ml-16 text-sm font-medium text-gray-500 truncate">All Sales / New Sales Last Month</p>
          </dt>
          <dd class="flex items-baseline pb-2 ml-16">
            <p class="text-2xl font-semibold text-gray-900">
              <%= @total_sales %> / <%= @sales_in_the_last_month %>
            </p>
          </dd>
        </div>
        <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
          <dt>
            <div class="absolute p-3 text-center rounded-md bg-zeiss-500">
              <%= icon name: "receipt", class: "text-white w-6 h-6" %>
            </div>
            <p class="ml-16 text-sm font-medium text-gray-500 truncate">All Orders / New Orders Last Month</p>
          </dt>
          <dd class="flex items-baseline pb-2 ml-16">
            <p class="text-2xl font-semibold text-gray-900">
              <%= @total_orders %> / <%= @orders_in_the_last_month %>
            </p>
          </dd>
        </div>
      </dl>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @pending_sales_by_region, empty: "No pending sales", title: "Pending Sales" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @pending_orders_by_region, empty: "No pending orders", title: "Pending Orders" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @users_by_region, title: "Users" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @stores_by_region, title: "Stores" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @sales_by_region, empty: "No sales", title: "Sales" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart @orders_by_region, empty: "No orders", title: "Orders" %>
    </div>
  </div>
<% end %>
