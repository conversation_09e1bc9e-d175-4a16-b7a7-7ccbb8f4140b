<%= render Page::Component.new do %>
  <div class="mb-3 lg:flex lg:items-center lg:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        System Administration Dashboard
      </h2>
      <div class="flex flex-col mt-1 sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <%= icon name: "shield-check", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
          Super Administrator
        </div>
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <%= icon name: "globe", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
          Global Access
        </div>
      </div>
    </div>
  </div>

  <!-- System Overview -->
  <div class="mb-8">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">System Overview</h3>
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Total Users -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-blue-500">
            <%= icon name: "users", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Total Users</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <%= User.count %>
          </p>
          <p class="ml-2 text-sm font-medium text-green-600">
            +<%= User.where(created_at: 1.month.ago..Time.current).count %> this month
          </p>
        </dd>
      </div>

      <!-- Total Sales -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-green-500">
            <%= icon name: "chart-line-up", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Total Sales</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <%= Sale.count %>
          </p>
          <p class="ml-2 text-sm font-medium text-green-600">
            +<%= Sale.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count %> this month
          </p>
        </dd>
      </div>

      <!-- Total Points Distributed -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-yellow-500">
            <%= icon name: "coins", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">Points Distributed</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-gray-900">
            <%= number_with_delimiter(Sale.approved.sum(:points)) %>
          </p>
        </dd>
      </div>

      <!-- System Health -->
      <div class="relative px-4 pt-5 pb-2 overflow-hidden bg-white rounded-lg shadow-sm sm:pt-6 sm:px-6">
        <dt>
          <div class="absolute p-3 text-center rounded-md bg-purple-500">
            <%= icon name: "wave-pulse", class: "h-6 w-6 text-white" %>
          </div>
          <p class="ml-16 text-sm font-medium text-gray-500 truncate">System Status</p>
        </dt>
        <dd class="flex items-baseline pb-2 ml-16">
          <p class="text-2xl font-semibold text-green-600">
            Healthy
          </p>
        </dd>
      </div>
    </div>
  </div>

  <!-- Administrative Tools -->
  <div class="mb-8">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Administrative Tools</h3>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <%= link_to "/admin", class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "sliders", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Admin Panel</span>
        </div>
      <% end %>

      <%= link_to "/sidekiq", class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "microchip", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Background Jobs</span>
        </div>
      <% end %>

      <%= link_to "/blazer", class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "chart-bar", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Analytics</span>
        </div>
      <% end %>

      <%= link_to "/pghero", class: "relative block w-full p-6 bg-white border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <div class="text-center">
          <%= icon name: "database", class: "mx-auto h-12 w-12 text-gray-400" %>
          <span class="mt-2 block text-sm font-medium text-gray-900">Database</span>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Multi-Brand Performance -->
  <div class="mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Brand Performance Comparison</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stores</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales (Month)</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @brand_stats.each do |brand_stat| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= brand_stat[:brand].name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= brand_stat[:stores_count] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= brand_stat[:users_count] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= brand_stat[:monthly_sales_count] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= number_with_delimiter(brand_stat[:approved_points]) %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- System Analytics -->
  <div class="grid grid-cols-1 gap-4 md:grid-cols-3 mb-8">
    <!-- User Growth -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">User Growth</h3>
        <div class="space-y-3">
          <% @user_growth_stats.each do |stat| %>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-900"><%= stat[:month_name] %></span>
              <span class="text-sm text-gray-500">+<%= stat[:user_count] %></span>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Recent System Activity</h3>
        <div class="space-y-3">
          <% Sale.includes(:user, :product).order(created_at: :desc).limit(5).each do |sale| %>
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm font-medium text-gray-900"><%= sale.product.name %></p>
                <p class="text-xs text-gray-500">by <%= sale.user.name %></p>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-900"><%= sale.status.titleize %></p>
                <p class="text-xs text-gray-500"><%= time_ago_in_words(sale.created_at) %> ago</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- System Alerts -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">System Alerts</h3>
        <div class="space-y-3">
          <% pending_sales = Sale.pending.count %>
          <% pending_orders = Order.pending.count %>
          <% if pending_sales > 0 %>
            <div class="flex items-center p-3 bg-yellow-50 rounded-md">
              <%= icon name: "triangle-exclamation", class: "h-5 w-5 text-yellow-400 mr-2" %>
              <span class="text-sm text-yellow-800"><%= pending_sales %> sales pending approval</span>
            </div>
          <% end %>
          <% if pending_orders > 0 %>
            <div class="flex items-center p-3 bg-yellow-50 rounded-md">
              <%= icon name: "triangle-exclamation", class: "h-5 w-5 text-yellow-400 mr-2" %>
              <span class="text-sm text-yellow-800"><%= pending_orders %> orders pending approval</span>
            </div>
          <% end %>
          <% if pending_sales == 0 && pending_orders == 0 %>
            <div class="flex items-center p-3 bg-green-50 rounded-md">
              <%= icon name: "circle-check", class: "h-5 w-5 text-green-400 mr-2" %>
              <span class="text-sm text-green-800">All approvals up to date</span>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Global Charts -->
  <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm">
      <%= pie_chart pending_count_sales_path(format: :json), empty: "No pending sales", title: "Global Sales Status" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm">
      <%= pie_chart count_users_path(format: :json), empty: "No users", title: "Users by Region" %>
    </div>
  </div>
<% end %>
