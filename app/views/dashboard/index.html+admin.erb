<%= render Page::Component.new do %>
  <div class="mb-3 lg:flex lg:items-center lg:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Welcome <%= current_user.name %>
      </h2>
      <div class="flex flex-col mt-1 sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <%= icon name: "user-tag", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
          <%= current_user.role.titleize %>
        </div>
        <% if current_user.admin_brand.present? %>
          <div class="flex items-center mt-2 text-sm text-gray-500">
            <%= icon name: "bags-shopping", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
            <%= current_user.admin_brand.name.titleize %>
          </div>
        <% end %>
        <% if current_user.admin_region.present? %>
          <div class="flex items-center mt-2 text-sm text-gray-500">
            <%= icon name: "earth-americas", class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
            <%= current_user.admin_region.name %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
    <div class="md:col-span-4">
      <h3 class="text-lg font-medium leading-6 text-gray-900">
        Statistics
      </h3>
      <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-2 xl:grid-cols-4">
        <%= turbo_frame_tag :users_stats, src: stats_users_path %>
        <%= turbo_frame_tag :stores_stats, src: stats_stores_path %>
        <%= turbo_frame_tag :sales_stats, src: stats_sales_path %>
        <%= turbo_frame_tag :order_stats, src: stats_orders_path %>
      </dl>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart pending_count_sales_path(format: :json), empty: "No pending sales", title: "Pending Sales" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart pending_count_orders_path(format: :json), empty: "No pending orders", title: "Pending Orders" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart count_users_path, title: "Users", empty: "No users in region", title: "Users in Region" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart count_stores_path, title: "Stores", empty: "No stores in region", title: "Stores in Region" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart region_count_sales_path, empty: "No sales", title: "Sales" %>
    </div>
    <div class="p-8 mb-4 bg-white rounded-sm shadow-sm sm:col-span-2">
      <%= pie_chart region_count_orders_path, empty: "No orders", title: "Orders" %>
    </div>
  </div>
<% end %>
