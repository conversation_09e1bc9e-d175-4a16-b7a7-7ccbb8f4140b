<%= component "page" do |page| %>
  <div class="bg-white rounded-lg shadow-sm">
    <% if @promotions&.any? %>
      <div class="p-4 bg-green-50 lg:col-span-2">
        <div class="flex">
          <div class="shrink-0">
            <!-- Heroicon name: solid/check-circle -->
            <svg class="w-5 h-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Earn more points</h3>
            <div class="mt-2 text-sm text-green-700">
              <% @promotions.each do |promotion| %>
                <!-- Promotion ID: <%= promotion.id %> -->
                <p>Earn <%= promotion.multiplier %>x the points when you sell this product until <%= local_time promotion.ends_at %>.</p>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    <div class="max-w-2xl px-4 py-8 mx-auto sm:py-8 sm:px-6 lg:max-w-7xl lg:px-8 lg:grid lg:grid-cols-2 lg:gap-x-8">
      <!-- Product details -->
      <div class="lg:max-w-lg lg:self-end">
        <div class="mt-4">
          <h1 class="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl"><%= @product.name %></h1>
        </div>
        <section aria-labelledby="information-heading" class="mt-4">
          <h2 id="information-heading" class="sr-only">Product information</h2>
          <div class="flex items-center">
            <p class="text-lg text-gray-900 sm:text-xl">Points: <%= @product.points_needed_for_country(Current.country.id) %></p>
          </div>
          <div class="mt-4 space-y-6">
            <p class="text-base text-gray-500"><%= @product.description %></p>
          </div>
        </section>
      </div>
      <!-- Product image -->
      <div class="mt-10 lg:mt-0 lg:col-start-2 lg:row-span-2 lg:self-center">
        <div class="overflow-hidden rounded-lg aspect-w-1 aspect-h-1">
          <%= image_tag @product.image_path, class: "object-cover object-center w-full h-full" %>
        </div>
      </div>
      <!-- Product form -->
      <div class="mt-10 lg:max-w-lg lg:col-start-1 lg:row-start-2 lg:self-start">
        <section aria-labelledby="options-heading">
          <h2 id="options-heading" class="sr-only">Product options</h2>
          <% if current_user.wallet.available_points < @product.points_needed_for_country(Current.country.id) %>
            <div class="p-4 rounded-md bg-red-50">
              <div class="flex">
                <div class="shrink-0">
                  <%= icon name: "times-circle", weight: :solid, class: "h-5 w-5 text-red-500" %>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Not enough points</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>You need <%= @product.points_needed_for_country(Current.country.id) %> points to buy this product.</p>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          <div class="mt-10">
            <%= button_to "Add to cart", line_items_path(product_id: @product.id), class: "flex items-center justify-center w-full px-8 py-3 text-base font-medium text-white bg-zeiss-600 border border-transparent rounded-md hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-zeiss-500", disabled: current_user.wallet.available_points < @product.points_needed_for_country(Current.country.id) %>
          </div>
        </section>
      </div>
    </div>
  </div>
<% end %>
