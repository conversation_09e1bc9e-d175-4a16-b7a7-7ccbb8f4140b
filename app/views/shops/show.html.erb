<%= component "page" do |page| %>
  <% page.with_header title: "Point Shop" %>
  <div class="mt-6 lg:mt-0 lg:col-span-2 xl:col-span-3">
    <%= component "page/filter", name: "Products" do |filter| %>
      <% filter.with_filter_collection(name: "Brand", collection: Brand.all, name_method: :name, value_method: :name, klass: :shop, filter_field: "category_brand_name") %>
      <% filter.with_filter_collection(name: "Category", collection: Category.order(:name), name_method: :name, value_method: :name, klass: :shop, filter_field: "category_name") %>
    <% end %>
    <div class="grid grid-cols-1 gap-y-10 sm:grid-cols-2 gap-x-6 lg:grid-cols-3 xl:grid-cols-5 xl:gap-x-8">
      <%= render partial: "product", collection: @products %>
    </div>
    <div class="mt-4">
      <%= component "page/pagination", pagy: @pagy %>
    </div>
  </div>
<% end %>
