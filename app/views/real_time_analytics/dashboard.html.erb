<%= component "page" do |page| %>
  <% page.with_header title: "Real-Time Analytics" do %>
    <div class="flex items-center space-x-4">
      <%= link_to reports_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "arrow-left", class: "h-4 w-4 mr-2" %>
        Back to Reports
      <% end %>
      
      <button 
        onclick="refreshAllData()"
        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "refresh-cw", class: "h-4 w-4 mr-2" %>
        Refresh All
      </button>
      
      <div class="flex items-center space-x-2">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="ml-2 text-sm text-gray-600">Live</span>
        </div>
        <span class="text-sm text-gray-400">|</span>
        <span class="text-sm text-gray-600">Auto-refresh: 30s</span>
      </div>
    </div>
  <% end %>

  <!-- Live Metrics Cards -->
  <div id="live-metrics" class="mb-8">
    <%= render "live_metrics", metrics: @live_metrics %>
  </div>

  <!-- Real-time Charts and Data -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Sales Stream -->
    <div id="sales-stream" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <%= render "sales_stream", data: @sales_stream %>
    </div>

    <!-- User Activity (Admin only) -->
    <% if current_user.admin? || current_user.super_admin? %>
      <div id="user-activity" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <%= render "user_activity", activity: @user_activity %>
      </div>
    <% else %>
      <!-- Personal Performance for Regular Users -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Your Performance Today</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Sales Made</span>
            <span class="text-lg font-semibold text-gray-900"><%= current_user.sales.where(created_at: Date.current.beginning_of_day..Time.current).count %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Points Earned</span>
            <span class="text-lg font-semibold text-gray-900"><%= current_user.sales.approved.where(created_at: Date.current.beginning_of_day..Time.current).sum(:points) %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Pending Approvals</span>
            <span class="text-lg font-semibold text-yellow-600"><%= current_user.sales.pending.count %></span>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- System Health (Super Admin only) -->
  <% if current_user.super_admin? %>
    <div id="system-health" class="mb-8">
      <%= render "system_health", health: @system_health %>
    </div>
  <% end %>

  <!-- Activity Feed -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Live Activity Feed</h3>
      <p class="text-sm text-gray-600 mt-1">Real-time updates from your system</p>
    </div>
    
    <div class="p-6">
      <div id="activity-feed" class="space-y-3">
        <!-- Activity items will be populated via JavaScript -->
        <div class="flex items-center text-sm text-gray-500">
          <div class="w-2 h-2 bg-gray-300 rounded-full mr-3"></div>
          <span>Waiting for live updates...</span>
        </div>
      </div>
    </div>
  </div>
<% end %>

<!-- Auto-refresh and WebSocket connection -->
<script>
  let refreshInterval;
  let activityFeed = [];
  
  document.addEventListener('DOMContentLoaded', function() {
    // Start auto-refresh
    startAutoRefresh();
    
    // Initialize activity feed
    initializeActivityFeed();
    
    // Connect to WebSocket for real-time updates
    connectToAnalyticsChannel();
  });
  
  function startAutoRefresh() {
    refreshInterval = setInterval(() => {
      refreshAllData();
    }, 30000); // 30 seconds
  }
  
  function stopAutoRefresh() {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
  }
  
  function refreshAllData() {
    // Show loading indicator
    showRefreshIndicator();
    
    // Refresh live metrics
    fetch('<%= live_metrics_real_time_analytics_path(format: :turbo_stream) %>', {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    // Refresh sales stream
    fetch('<%= sales_stream_real_time_analytics_path(format: :turbo_stream) %>', {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    <% if current_user.admin? || current_user.super_admin? %>
      // Refresh user activity
      fetch('<%= user_activity_real_time_analytics_path(format: :turbo_stream) %>', {
        method: 'GET',
        headers: {
          'Accept': 'text/vnd.turbo-stream.html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
    <% end %>
    
    <% if current_user.super_admin? %>
      // Refresh system health
      fetch('<%= system_health_real_time_analytics_path(format: :turbo_stream) %>', {
        method: 'GET',
        headers: {
          'Accept': 'text/vnd.turbo-stream.html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
    <% end %>
  }
  
  function showRefreshIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center';
    indicator.innerHTML = `
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      Refreshing data...
    `;
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      if (indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }, 2000);
  }
  
  function initializeActivityFeed() {
    // Initialize with some sample activities
    addActivityItem('System started monitoring', 'info');
    addActivityItem('Real-time analytics enabled', 'success');
  }
  
  function addActivityItem(message, type = 'info') {
    const feed = document.getElementById('activity-feed');
    const timestamp = new Date().toLocaleTimeString();
    
    const colorMap = {
      'info': 'bg-blue-500',
      'success': 'bg-green-500',
      'warning': 'bg-yellow-500',
      'error': 'bg-red-500'
    };
    
    const item = document.createElement('div');
    item.className = 'flex items-center text-sm text-gray-700 animate-fade-in';
    item.innerHTML = `
      <div class="w-2 h-2 ${colorMap[type]} rounded-full mr-3"></div>
      <span class="flex-1">${message}</span>
      <span class="text-xs text-gray-500">${timestamp}</span>
    `;
    
    // Add to beginning of feed
    if (feed.firstChild) {
      feed.insertBefore(item, feed.firstChild);
    } else {
      feed.appendChild(item);
    }
    
    // Keep only last 10 items
    while (feed.children.length > 10) {
      feed.removeChild(feed.lastChild);
    }
  }
  
  function connectToAnalyticsChannel() {
    // This would connect to ActionCable for real-time updates
    // For now, we'll simulate with periodic updates
    console.log('Connected to analytics channel');
  }
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
  });
</script>

<style>
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
</style>
