<div class="px-6 py-4 border-b border-gray-200">
  <h3 class="text-lg font-semibold text-gray-900">User Activity</h3>
  <p class="text-sm text-gray-600 mt-1">Recent logins and registrations</p>
</div>

<div class="p-6">
  <!-- Online Users Count -->
  <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3"></div>
      <span class="text-sm font-medium text-green-900">
        <%= pluralize(activity[:online_users], 'user') %> online now
      </span>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Logins -->
    <div>
      <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Logins</h4>
      <div class="space-y-3">
        <% if activity[:recent_logins].any? %>
          <% activity[:recent_logins].each do |login| %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                  <%= login[:name].first.upcase %>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900"><%= login[:name] %></p>
                  <p class="text-xs text-gray-600"><%= login[:role] %></p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-gray-500"><%= login[:time] %> ago</p>
              </div>
            </div>
          <% end %>
        <% else %>
          <p class="text-sm text-gray-500">No recent logins</p>
        <% end %>
      </div>
    </div>

    <!-- Recent Registrations -->
    <div>
      <h4 class="text-sm font-medium text-gray-900 mb-3">New Registrations</h4>
      <div class="space-y-3">
        <% if activity[:recent_registrations].any? %>
          <% activity[:recent_registrations].each do |registration| %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                  <%= registration[:name].first.upcase %>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900"><%= registration[:name] %></p>
                  <p class="text-xs text-gray-600"><%= registration[:store] || 'No store assigned' %></p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-gray-500"><%= registration[:time] %> ago</p>
              </div>
            </div>
          <% end %>
        <% else %>
          <p class="text-sm text-gray-500">No new registrations today</p>
        <% end %>
      </div>
    </div>
  </div>
</div>
