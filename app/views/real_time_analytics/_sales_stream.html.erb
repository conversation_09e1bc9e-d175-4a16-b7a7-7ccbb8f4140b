<div class="px-6 py-4 border-b border-gray-200">
  <h3 class="text-lg font-semibold text-gray-900">Sales Stream (24h)</h3>
  <p class="text-sm text-gray-600 mt-1">Hourly sales activity</p>
</div>

<div class="p-6">
  <!-- Summary Stats -->
  <div class="grid grid-cols-3 gap-4 mb-6">
    <div class="text-center">
      <p class="text-2xl font-bold text-gray-900"><%= data[:total] %></p>
      <p class="text-sm text-gray-600">Total Sales</p>
    </div>
    <div class="text-center">
      <p class="text-2xl font-bold text-gray-900"><%= data[:average] %></p>
      <p class="text-sm text-gray-600">Avg/Hour</p>
    </div>
    <div class="text-center">
      <p class="text-2xl font-bold text-gray-900"><%= data[:peak_hour][:time] if data[:peak_hour] %></p>
      <p class="text-sm text-gray-600">Peak Hour</p>
    </div>
  </div>

  <!-- Chart Container -->
  <div class="h-64">
    <%= render Charts::LineChartComponent.new(
      data: {
        labels: data[:hours].map { |h| h[:time] },
        datasets: [{
          label: "Sales",
          data: data[:hours].map { |h| h[:sales] },
          borderColor: "#3b82f6",
          backgroundColor: "rgba(59, 130, 246, 0.1)",
          tension: 0.4,
          fill: true
        }]
      },
      height: "250px",
      chart_id: "sales-stream-chart",
      title: nil,
      x_axis_label: "Hour",
      y_axis_label: "Sales Count",
      smooth: true,
      fill: true,
      colors: ["#3b82f6"]
    ) %>
  </div>

  <!-- Recent Activity -->
  <div class="mt-6 pt-6 border-t border-gray-200">
    <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Activity</h4>
    <div class="space-y-2">
      <% if data[:hours].any? %>
        <% data[:hours].last(5).reverse.each do |hour| %>
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-600"><%= hour[:time] %></span>
            <span class="font-medium text-gray-900">
              <%= pluralize(hour[:sales], 'sale') %>
            </span>
          </div>
        <% end %>
      <% else %>
        <p class="text-sm text-gray-500">No recent activity</p>
      <% end %>
    </div>
  </div>
</div>
