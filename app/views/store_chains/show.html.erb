<%= component "page" do |page| %>
  <% page.with_header title: @store_chain.name do |header| %>
    <% header.with_actions([
      {title: "Edit", url: edit_store_chain_path(@store_chain), primary: true},
    ]) %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_button title: "Delete", url: store_chain_path(@store_chain), visible: allowed_to?(:destroy?, @store_chain), icon_name: "trash", delete: true %>
    <% end %>
  <% end %>
  <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
    <div class="overflow-hidden bg-white shadow-sm sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200">
        <% @store_chain.stores.each do |store| %>
          <li>
            <%= link_to store, class: "block hover:bg-gray-50" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium truncate text-zeiss-600"><%= store.name %></p>
                  <div class="flex ml-2 shrink-0">
                    <p class="inline-flex px-2 text-xs font-semibold leading-5 rounded-full bg-zeiss-100 text-zeiss-800"><%= store.users_count %></p>
                  </div>
                </div>
                <div class="mt-2 sm:flex sm:justify-between">
                  <div class="sm:flex">
                    <p class="flex items-center text-sm text-gray-500">
                      <%= icon name: "location-dot", weight: :solid, class: "shrink-0 mr-1.5 h-5 w-5 text-gray-400" %>
                      <%= store.street %>, <%= store.city %>, <%= store.state.name %>
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
    </div>
  </div>
<% end %>
