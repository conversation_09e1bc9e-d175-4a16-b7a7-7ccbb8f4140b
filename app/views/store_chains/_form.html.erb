<% content_for :css do %>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slim-select/1.27.0/slimselect.min.css" />
<% end %>
<%= form_with model: store_chain do |form| %>
  <div class="bg-white rounded-lg shadow-sm">
    <div class="px-4 py-5 sm:px-6">
      <div>
        <div>
          <h3 class="text-lg font-medium leading-6 text-gray-900">Chain</h3>
        </div>
        <%= render "shared/error_messages", resource: form.object %>
        <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-4">
            <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.text_field :name, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
          <div class="sm:col-span-4">
            <%= form.label :store_ids, "Stores", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.collection_select :store_ids, Store.order(:name), :id, :name_with_city, {include_hidden: false}, multiple: true, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md", "x-slimselect": "{hideSelectedOption: true, closeOnSelect: false}" %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="px-4 py-5 sm:px-6 bg-gray-50">
      <div class="flex justify-end space-x-3">
        <%= link_to "Cancel", store_chains_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      </div>
    </div>
  </div>
<% end %>
