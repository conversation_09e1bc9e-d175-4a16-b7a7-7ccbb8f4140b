<%= component "page" do |page| %>
  <% page.with_header title: "Store Chains" do |header| %>
    <% header.with_actions([
      { title: "New Chain", url: new_store_chain_path, primary: true }
    ]) %>
  <% end %>
  <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
    <div class="flex flex-col">
      <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          <div class="overflow-hidden border-b border-gray-200 shadow-sm sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
                  <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"># of Stores</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @chains.each do |chain| %>
                  <tr>
                    <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                      <%= link_to chain.name || "Blank", chain %>
                    </td>
                    <td class="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap">
                      <%= chain.stores_count %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
