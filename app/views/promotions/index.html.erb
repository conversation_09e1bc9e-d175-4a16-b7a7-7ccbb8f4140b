<%= component "page" do |page| %>
  <% page.with_header title: "Promotions" do |header| %>
    <% header.with_actions([
      {title: "New Promotion", url: new_promotion_path, primary: true, visible: allowed_to?(:create?, Promotion)},
    ]) %>
  <% end %>
  <%= render PromotionCalendar.new(self, {events: @promotions, attribute: :starts_at, end_attribute: :ends_at}) do |date, promotions| %>
    <% if date == Time.zone.today %>
      <%= time_tag date, format: "%d", class: "flex h-6 w-6 items-center justify-center rounded-full bg-zeiss-600 font-semibold text-white" %>
    <% else %>
      <%= time_tag date, format: "%d" %>
    <% end %>
    <ol>
      <% promotions.each do |promotion| %>
        <li>
          <%= link_to promotion, class: "group flex" do %>
            <p class="flex-auto font-medium text-gray-900 truncate group-hover:text-zeiss-600"><%= promotion.name %></p>
          <% end %>
        </li>
      <% end %>
    </ol>
  <% end %>
<% end %>
