<% content_for :css do %>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slim-select/2.8.1/slimselect.min.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
<% end %>
<%= render "shared/error_messages", resource: form.object %>
<div class="grid grid-cols-1 sm:grid-cols-2 gap-y-6 gap-x-4">
  <div>
    <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1">
      <%= form.text_field :name, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
    </div>
  </div>
  <div>
    <div class="flex items-center justify-between">
      <h2 class="text-sm font-medium text-gray-700">Multiplier</h2>
    </div>
    <fieldset class="mt-2">
      <legend class="sr-only">Choose promotional point multiplier</legend>
      <div class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
        <div class="flex items-center">
          <%= form.radio_button :multiplier, "1.5", class: "h-4 w-4 border-gray-300 text-zeiss-600 focus:ring-zeiss-500" %>
          <%= form.label :multiplier, class: "ml-3 block text-sm font-medium text-gray-700" do %>
            1.5x
          <% end %>
        </div>
        <div class="flex items-center">
          <%= form.radio_button :multiplier, "2", class: "h-4 w-4 border-gray-300 text-zeiss-600 focus:ring-zeiss-500" %>
          <%= form.label :multiplier, class: "ml-3 block text-sm font-medium text-gray-700" do %>
            2x
          <% end %>
        </div>
      </div>
    </fieldset>
  </div>
  <div class="sm:col-span-2">
    <%= form.label :dates, class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1">
      <%= form.text_field :dates, class: "shadow-xs focus:ring-zeiss-500 focus:border-zeiss-500 hidden w-full sm:text-sm border-gray-300 rounded-md", "x-flatpickr": "{inline: true, dateFormat: 'Z', minDate: '#{Time.current.utc.iso8601}', mode: 'range', showMonths: 2}" %>
    </div>
  </div>
  <div>
    <%= form.label :location_sgids, "Locations", class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1">
      <%= form.grouped_collection_select :location_sgids, [StoreChain, Store.includes(:address)], :order_by_name, :group_name, :to_sgid, :name_with_city, {}, "x-slimselect": true, multiple: true %>
    </div>
  </div>
  <div>
    <%= form.label :products, class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1">
      <%= form.collection_select :product_ids, Product.order(:name), :id, :name, {}, multiple: true, "x-slimselect": "{hideSelectedOption: true}" %>
    </div>
  </div>
  <div>
    <%= form.submit class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
  </div>
</div>
