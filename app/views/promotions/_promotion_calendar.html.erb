<div class="lg:flex lg:h-full lg:flex-col">
  <header class="relative z-20 flex items-center justify-between px-6 py-4 border-b border-gray-200 lg:flex-none">
    <h1 class="text-lg font-semibold text-gray-900">
      <%= t('date.month_names')[start_date.month] %> <%= start_date.year %>
    </h1>
    <div class="flex items-center">
      <div class="flex items-center rounded-md shadow-xs md:items-stretch">
        <%= link_to calendar.url_for_previous_view, class: "flex items-center justify-center rounded-l-md border border-r-0 border-gray-300 bg-white py-2 pl-3 pr-4 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:bg-gray-50" do %>
          <span class="sr-only"><%= t("simple_calendar.previous", default: "Previous") %></span>
          <%= icon name: "chevron-left", class: "w-5 h-5" %>
        <% end %>
        <%= link_to "Today", promotions_path, class: "hidden border-t border-b border-gray-300 bg-white px-3.5 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 focus:relative items-center md:flex" %>
        <span class="relative w-px h-5 -mx-px bg-gray-300 md:hidden"></span>
        <%= link_to calendar.url_for_next_view, class: "flex items-center justify-center rounded-r-md border border-l-0 border-gray-300 bg-white py-2 pl-4 pr-3 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:text-gray-50" do %>
          <span class="sr-only"><%= t("simple_calendar.next", default: "Next") %></span>
          <%= icon name: "chevron-right", class: "w-5 h-5" %>
        <% end %>
      </div>
    </div>
  </header>
  <div class="rounded-lg shadow-sm ring-1 ring-black ring-opacity-5 lg:flex lg:flex-auto lg:flex-col">
    <div class="grid grid-cols-7 gap-px text-xs font-semibold leading-6 text-center text-gray-700 bg-gray-200 border-b border-gray-300 rounded-lg lg:flex-none">
      <div class="py-2 bg-white rounded-tl-lg">M<span class="sr-only sm:not-sr-only">on</span></div>
      <div class="py-2 bg-white">T<span class="sr-only sm:not-sr-only">ue</span></div>
      <div class="py-2 bg-white">W<span class="sr-only sm:not-sr-only">ed</span></div>
      <div class="py-2 bg-white">T<span class="sr-only sm:not-sr-only">hu</span></div>
      <div class="py-2 bg-white">F<span class="sr-only sm:not-sr-only">ri</span></div>
      <div class="py-2 bg-white">S<span class="sr-only sm:not-sr-only">at</span></div>
      <div class="py-2 bg-white rounded-tr-lg">S<span class="sr-only sm:not-sr-only">un</span></div>
    </div>
    <div class="flex text-xs leading-6 text-gray-700 bg-gray-200 lg:flex-auto">
      <div class="hidden w-full lg:grid lg:grid-cols-7 lg:grid-rows-6 lg:gap-px">
        <% date_range.each do |day| %>
          <div class="<%= calendar.classes_for(day) %>">
            <% passed_block.call day, sorted_events.fetch(day, []) %>
          </div>
        <% end %>
      </div>
    </div>
    <div class="grid w-full grid-cols-7 grid-rows-6 gap-px isolate lg:hidden">
      <% date_range.each do |day| %>
        <%= button_to %>
      <% end %>
    </div>
  </div>
</div>
