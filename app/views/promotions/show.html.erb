<%= component "page" do |page| %>
  <% page.with_header title: @promotion.name do |header| %>
    <% header.with_menu do |menu| %>
      <% menu.with_item_link(name: "Delete", url: promotion_path(@promotion), visible: allowed_to?(:destroy?, @promotion), data: {turbo_method: "delete"}) %>
    <% end %>
  <% end %>
  <div class="space-y-6">
    <%= render Card::Component.new do %>
      <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Starts
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= local_time @promotion.starts_at %>
          </dd>
        </div>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Ends
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= local_time @promotion.ends_at %>
          </dd>
        </div>
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">
            Point Multiplier
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= @promotion.multiplier %>x
          </dd>
        </div>
      </dl>
    <% end %>
    <%= component "card" do |card| %>
      <% card.with_header title: "Stores" %>
      <% @promotion.promotion_locations.each do |store| %>
        <%= link_to store.promotable.name, store.promotable, class: "block" %>
      <% end %>
    <% end %>
    <%= component "card" do |card| %>
      <% card.with_header title: "Products" %>
      <% @promotion.products.each do |product| %>
        <%= link_to product.name, product, class: "block" %>
      <% end %>
    <% end %>
  </div>
<% end %>
