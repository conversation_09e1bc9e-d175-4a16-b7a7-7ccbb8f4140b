<%= component "page" do |page| %>
  <% page.with_header title: "#{@target_user == current_user ? 'My' : @target_user.name + "'s"} Performance Report" do %>
    <div class="flex items-center space-x-4">
      <%= link_to reports_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "arrow-left", class: "h-4 w-4 mr-2" %>
        Back to Reports
      <% end %>
      
      <!-- User Selector (for admins) -->
      <% if current_user.admin? || current_user.super_admin? %>
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">User:</label>
          <select 
            onchange="changeUser(this.value)"
            class="rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 text-sm"
          >
            <option value="<%= current_user.id %>" <%= 'selected' if @target_user == current_user %>>
              My Performance
            </option>
            <% User.where.not(id: current_user.id).limit(20).each do |user| %>
              <option value="<%= user.id %>" <%= 'selected' if @target_user == user %>>
                <%= user.name %> (<%= user.role.humanize %>)
              </option>
            <% end %>
          </select>
        </div>
      <% end %>
      
      <!-- Date Range Selector -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Period:</label>
        <select 
          onchange="updateDateRange(this.value)"
          class="rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 text-sm"
        >
          <option value="7d" <%= 'selected' if @date_range == '7d' %>>Last 7 Days</option>
          <option value="30d" <%= 'selected' if @date_range == '30d' %>>Last 30 Days</option>
          <option value="90d" <%= 'selected' if @date_range == '90d' %>>Last 90 Days</option>
          <option value="1y" <%= 'selected' if @date_range == '1y' %>>Last Year</option>
        </select>
      </div>
      
      <!-- Export Options -->
      <div class="relative" x-data="{ open: false }">
        <button 
          @click="open = !open"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "download", class: "h-4 w-4 mr-2" %>
          Export Report
          <%= icon name: "chevron-down", class: "h-4 w-4 ml-2" %>
        </button>
        
        <div 
          x-show="open" 
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
        >
          <div class="py-1">
            <%= link_to export_user_performance_path(@target_user, format: :xlsx, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              Excel Report
            <% end %>
            <%= link_to export_user_performance_path(@target_user, format: :csv, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              CSV Data
            <% end %>
            <%= link_to export_user_performance_path(@target_user, format: :pdf, date_range: @date_range), 
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
              PDF Report
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- User Profile Summary -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-16 h-16 bg-zeiss-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
          <%= @performance_data[:user_profile][:name].first.upcase %>
        </div>
        <div class="ml-4">
          <h2 class="text-xl font-bold text-gray-900"><%= @performance_data[:user_profile][:name] %></h2>
          <p class="text-sm text-gray-600">
            <%= @performance_data[:user_profile][:role] %>
            <% if @performance_data[:user_profile][:store] %>
              • <%= @performance_data[:user_profile][:store] %>
            <% end %>
            <% if @performance_data[:user_profile][:region] %>
              • <%= @performance_data[:user_profile][:region] %>
            <% end %>
          </p>
          <p class="text-xs text-gray-500 mt-1">
            Member since <%= @performance_data[:user_profile][:registration_date].strftime('%B %Y') %>
            <% if @performance_data[:user_profile][:last_login] %>
              • Last login <%= time_ago_in_words(@performance_data[:user_profile][:last_login]) %> ago
            <% end %>
          </p>
        </div>
      </div>
      <div class="text-right">
        <div class="flex items-center space-x-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-zeiss-600">
              <%= @performance_data[:user_profile][:profile_completion] %>%
            </p>
            <p class="text-xs text-gray-600">Profile Complete</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-green-600">
              <%= @performance_data[:performance_overview][:current_period][:current_balance] %>
            </p>
            <p class="text-xs text-gray-600">Current Balance</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Overview -->
  <div id="performance-overview" class="mb-8">
    <%= render "overview", data: @performance_data[:performance_overview], user: @target_user %>
  </div>

  <!-- Goal Tracking -->
  <div id="goal-tracking" class="mb-8">
    <%= render "goal_tracking", data: @performance_data[:goal_tracking], user: @target_user %>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Activity Tracking -->
    <div id="activity-tracking">
      <%= render "activity_tracking", data: @performance_data[:activity_tracking], user: @target_user %>
    </div>

    <!-- Achievements -->
    <div id="achievements">
      <%= render "achievements", data: @performance_data[:achievements], user: @target_user %>
    </div>
  </div>

  <!-- Performance Trends -->
  <div id="performance-trends" class="mb-8">
    <%= render "performance_trends", data: @performance_data[:performance_trends], user: @target_user %>
  </div>

  <!-- Comparative Analysis (Admin only) -->
  <% if (current_user.admin? || current_user.super_admin?) && @performance_data[:comparative_analysis].any? %>
    <div id="comparative-analysis" class="mb-8">
      <%= render "comparative_analysis", data: @performance_data[:comparative_analysis], user: @target_user %>
    </div>
  <% end %>

  <!-- Recommendations -->
  <div id="recommendations" class="mb-8">
    <%= render "recommendations", recommendations: @performance_data[:recommendations], user: @target_user %>
  </div>

  <!-- Detailed Metrics -->
  <div id="detailed-metrics">
    <%= render "detailed_metrics", data: @performance_data[:detailed_metrics], user: @target_user %>
  </div>
<% end %>

<script>
  function changeUser(userId) {
    const url = new URL(window.location);
    url.pathname = `/user_performance/${userId}`;
    window.location.href = url.toString();
  }
  
  function updateDateRange(dateRange) {
    const url = new URL(window.location);
    url.searchParams.set('date_range', dateRange);
    window.location.href = url.toString();
  }
  
  // Auto-refresh data every 10 minutes
  setInterval(() => {
    refreshPerformanceData();
  }, 600000);
  
  function refreshPerformanceData() {
    // Refresh key components
    const userId = '<%= @target_user.id %>';
    const dateRange = '<%= @date_range %>';
    
    fetch(`/user_performance/${userId}/overview?date_range=${dateRange}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`/user_performance/${userId}/goal_tracking?date_range=${dateRange}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    fetch(`/user_performance/${userId}/activity_tracking?date_range=${dateRange}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
  }
</script>
