<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-gray-900">Goal Tracking</h3>
    <% if current_user == user || current_user.admin? || current_user.super_admin? %>
      <button 
        onclick="openGoalEditor()"
        class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "edit", class: "h-3 w-3 mr-1" %>
        Edit Goals
      </button>
    <% end %>
  </div>

  <div class="space-y-6">
    <!-- Monthly Sales Goal -->
    <div class="goal-item">
      <div class="flex justify-between items-center mb-2">
        <h4 class="text-sm font-medium text-gray-900">Monthly Sales Goal</h4>
        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= goal_status_class(data[:monthly_sales][:status]) %>">
          <%= data[:monthly_sales][:status].humanize %>
        </span>
      </div>
      
      <div class="flex justify-between text-sm text-gray-600 mb-2">
        <span><%= data[:monthly_sales][:current] %> / <%= data[:monthly_sales][:goal] %> sales</span>
        <span><%= data[:monthly_sales][:days_remaining] %> days remaining</span>
      </div>
      
      <div class="w-full bg-gray-200 rounded-full h-3">
        <div 
          class="<%= goal_progress_class(data[:monthly_sales][:status]) %> h-3 rounded-full transition-all duration-300"
          style="width: <%= [data[:monthly_sales][:progress], 100].min %>%"
        ></div>
      </div>
      
      <div class="mt-2 text-right">
        <span class="text-sm font-medium text-gray-900"><%= data[:monthly_sales][:progress] %>%</span>
      </div>
    </div>

    <!-- Monthly Points Goal -->
    <div class="goal-item">
      <div class="flex justify-between items-center mb-2">
        <h4 class="text-sm font-medium text-gray-900">Monthly Points Goal</h4>
        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= goal_status_class(data[:monthly_points][:status]) %>">
          <%= data[:monthly_points][:status].humanize %>
        </span>
      </div>
      
      <div class="flex justify-between text-sm text-gray-600 mb-2">
        <span><%= number_with_delimiter(data[:monthly_points][:current]) %> / <%= number_with_delimiter(data[:monthly_points][:goal]) %> points</span>
        <span><%= data[:monthly_points][:days_remaining] %> days remaining</span>
      </div>
      
      <div class="w-full bg-gray-200 rounded-full h-3">
        <div 
          class="<%= goal_progress_class(data[:monthly_points][:status]) %> h-3 rounded-full transition-all duration-300"
          style="width: <%= [data[:monthly_points][:progress], 100].min %>%"
        ></div>
      </div>
      
      <div class="mt-2 text-right">
        <span class="text-sm font-medium text-gray-900"><%= data[:monthly_points][:progress] %>%</span>
      </div>
    </div>

    <!-- Quarterly Sales Goal -->
    <div class="goal-item">
      <div class="flex justify-between items-center mb-2">
        <h4 class="text-sm font-medium text-gray-900">Quarterly Sales Goal</h4>
        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= goal_status_class(data[:quarterly_sales][:status]) %>">
          <%= data[:quarterly_sales][:status].humanize %>
        </span>
      </div>
      
      <div class="flex justify-between text-sm text-gray-600 mb-2">
        <span><%= data[:quarterly_sales][:current] %> / <%= data[:quarterly_sales][:goal] %> sales</span>
        <span><%= data[:quarterly_sales][:days_remaining] %> days remaining</span>
      </div>
      
      <div class="w-full bg-gray-200 rounded-full h-3">
        <div 
          class="<%= goal_progress_class(data[:quarterly_sales][:status]) %> h-3 rounded-full transition-all duration-300"
          style="width: <%= [data[:quarterly_sales][:progress], 100].min %>%"
        ></div>
      </div>
      
      <div class="mt-2 text-right">
        <span class="text-sm font-medium text-gray-900"><%= data[:quarterly_sales][:progress] %>%</span>
      </div>
    </div>
  </div>

  <!-- Goal Summary -->
  <div class="mt-6 pt-6 border-t border-gray-200">
    <div class="grid grid-cols-3 gap-4 text-center">
      <div>
        <p class="text-2xl font-bold text-green-600">
          <%= data.values.count { |goal| goal[:status] == 'exceeded' || goal[:status] == 'ahead' } %>
        </p>
        <p class="text-sm text-gray-600">Goals On Track</p>
      </div>
      <div>
        <p class="text-2xl font-bold text-yellow-600">
          <%= data.values.count { |goal| goal[:status] == 'on_track' } %>
        </p>
        <p class="text-sm text-gray-600">Goals In Progress</p>
      </div>
      <div>
        <p class="text-2xl font-bold text-red-600">
          <%= data.values.count { |goal| goal[:status] == 'behind' } %>
        </p>
        <p class="text-sm text-gray-600">Goals Behind</p>
      </div>
    </div>
  </div>
</div>

<!-- Goal Editor Modal -->
<div id="goal-editor-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Goals</h3>
      
      <%= form_with url: update_goals_user_performance_path(user), method: :patch, local: false, class: "space-y-4" do |form| %>
        <div>
          <%= form.label "goals[monthly_sales]", "Monthly Sales Goal", class: "block text-sm font-medium text-gray-700" %>
          <%= form.number_field "goals[monthly_sales]", 
              value: data[:monthly_sales][:goal], 
              min: 1, 
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 sm:text-sm" %>
        </div>
        
        <div>
          <%= form.label "goals[monthly_points]", "Monthly Points Goal", class: "block text-sm font-medium text-gray-700" %>
          <%= form.number_field "goals[monthly_points]", 
              value: data[:monthly_points][:goal], 
              min: 1, 
              step: 100,
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 sm:text-sm" %>
        </div>
        
        <div>
          <%= form.label "goals[quarterly_sales]", "Quarterly Sales Goal", class: "block text-sm font-medium text-gray-700" %>
          <%= form.number_field "goals[quarterly_sales]", 
              value: data[:quarterly_sales][:goal], 
              min: 1, 
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 sm:text-sm" %>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
          <button 
            type="button" 
            onclick="closeGoalEditor()"
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
          >
            Cancel
          </button>
          <%= form.submit "Update Goals", 
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  function openGoalEditor() {
    document.getElementById('goal-editor-modal').classList.remove('hidden');
  }
  
  function closeGoalEditor() {
    document.getElementById('goal-editor-modal').classList.add('hidden');
  }
  
  // Close modal when clicking outside
  document.getElementById('goal-editor-modal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeGoalEditor();
    }
  });
</script>

<%
  def goal_status_class(status)
    case status
    when 'exceeded'
      'bg-green-100 text-green-800'
    when 'ahead'
      'bg-blue-100 text-blue-800'
    when 'on_track'
      'bg-yellow-100 text-yellow-800'
    when 'behind'
      'bg-red-100 text-red-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def goal_progress_class(status)
    case status
    when 'exceeded'
      'bg-green-500'
    when 'ahead'
      'bg-blue-500'
    when 'on_track'
      'bg-yellow-500'
    when 'behind'
      'bg-red-500'
    else
      'bg-gray-500'
    end
  end
%>
