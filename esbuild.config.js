#!/usr/bin/env node

const esbuild = require('esbuild')
const rails = require('esbuild-rails')
const path = require('path')

const watch = process.argv.includes('--watch')
const minify = process.env.NODE_ENV === 'production'

const config = {
  entryPoints: ['application.js'],
  bundle: true,
  outdir: path.join(process.cwd(), 'app/assets/builds'),
  absWorkingDir: path.join(process.cwd(), 'app/javascript'),
  watch: watch,
  minify: minify,
  sourcemap: true,
  format: 'esm',
  target: ['es2020'],
  publicPath: '/assets',

  // Define global variables
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },

  // Loader configuration
  loader: {
    '.js': 'js',
    '.ts': 'ts',
    '.jsx': 'jsx',
    '.tsx': 'tsx',
    '.css': 'css',
    '.png': 'file',
    '.jpg': 'file',
    '.jpeg': 'file',
    '.gif': 'file',
    '.svg': 'file',
  },

  plugins: [
    rails(),
    // Add build completion logging
    {
      name: 'build-logger',
      setup(build) {
        build.onEnd(result => {
          if (result.errors.length > 0) {
            console.error('❌ JavaScript build failed with errors:')
            result.errors.forEach(error => console.error(error))
          } else {
            console.log('✅ JavaScript build completed successfully')
          }
        })
      },
    }
  ]
}

if (watch) {
  esbuild.context(config).then(ctx => {
    ctx.watch()
    console.log('👀 Watching for JavaScript changes...')
  }).catch(err => {
    console.error('❌ ESBuild watch failed:', err)
    process.exit(1)
  })
} else {
  esbuild.build(config).catch(err => {
    console.error('❌ ESBuild failed:', err)
    process.exit(1)
  })
}
