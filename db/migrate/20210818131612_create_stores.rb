class CreateStores < ActiveRecord::Migration[6.1]
  def change
    create_table :stores do |t|
      t.string :name, null: false
      t.string :account_number
      t.string :city
      t.string :street
      t.string :unit
      t.string :zip
      t.string :phone_number
      t.boolean :verified
      t.integer :old_store_id

      t.references :state, null: false, index: true
      t.references :account_type, null: false
      t.references :account_channel, null: false

      t.timestamps
    end
  end
end
