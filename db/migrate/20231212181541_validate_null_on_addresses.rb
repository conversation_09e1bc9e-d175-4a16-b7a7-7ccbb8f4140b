class ValidateNullOnAddresses < ActiveRecord::Migration[7.1]
  def up
    validate_check_constraint :addresses, name: "addresses_country_id_null"
    change_column_null :addresses, :country_id, false
    remove_check_constraint :addresses, name: "addresses_country_id_null"
  end

  def down
    add_check_constraint :addresses, "country_id IS NOT NULL", name: "addresses_country_id_null", validate: false
    change_column_null :addresses, :country_id, true
  end
end
