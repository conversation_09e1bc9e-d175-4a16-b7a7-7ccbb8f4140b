class CreateSales < ActiveRecord::Migration[6.1]
  def change
    create_table :sales do |t|
      t.string :serial_number
      t.date :sold_at
      t.text :notes
      t.integer :points
      t.integer :status, default: 0
      t.date :approved_at

      t.references :admin
      t.references :user, null: false, index: true
      t.references :product, null: false, index: true

      t.timestamps
    end
  end
end
