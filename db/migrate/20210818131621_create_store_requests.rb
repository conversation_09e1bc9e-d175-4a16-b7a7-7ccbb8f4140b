class CreateStoreRequests < ActiveRecord::Migration[6.1]
  def change
    create_table :store_requests do |t|
      t.string :name
      t.string :store_name
      t.string :street
      t.string :city
      t.string :zip
      t.string :email
      t.string :manager_name
      t.integer :status, default: 0

      t.references :state, null: false, index: true
      t.references :site, null: false

      t.timestamps
    end
  end
end
