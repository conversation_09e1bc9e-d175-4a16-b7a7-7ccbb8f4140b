class AddCompositeIndexesToSalesAndOrders < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!

  def change
    # Sales table composite indexes for common query patterns
    add_index :sales, [:status, :created_at], algorithm: :concurrently
    add_index :sales, [:brand_id, :status, :created_at], algorithm: :concurrently
    add_index :sales, [:user_id, :status, :created_at], algorithm: :concurrently
    add_index :sales, [:sold_at, :status], algorithm: :concurrently
    add_index :sales, [:region_id, :status, :created_at], algorithm: :concurrently

    # Orders table composite indexes for common query patterns
    add_index :orders, [:status, :created_at], algorithm: :concurrently
    add_index :orders, [:brand_id, :status, :created_at], algorithm: :concurrently
    add_index :orders, [:user_id, :status, :created_at], algorithm: :concurrently
    add_index :orders, [:region_id, :status, :created_at], algorithm: :concurrently

    # Additional indexes for reporting and analytics
    add_index :sales, [:created_at, :status], algorithm: :concurrently
    add_index :orders, [:approved_at], algorithm: :concurrently, where: "approved_at IS NOT NULL"
    add_index :orders, [:declined_at], algorithm: :concurrently, where: "declined_at IS NOT NULL"
  end
end
