class AdjustOrderColumns < ActiveRecord::Migration[7.1]
  def change
    safety_assured {
      remove_column :orders, :ship_to_home
      change_column :orders, :points, :integer, default: 0, null: false
      change_column :orders, :ship_to, :integer, default: 0
      change_column :orders, :brand_id, :bigint, null: false
      change_column :orders, :region_id, :bigint, null: false
      change_column_null :orders, :ship_to, false, 0
    }
  end
end
