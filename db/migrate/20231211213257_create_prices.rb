class CreatePrices < ActiveRecord::Migration[7.1]
  def change
    create_table :prices do |t|
      t.integer :msrp_in_cents, default: 0, null: false
      t.integer :points_needed, default: 0, null: false
      t.integer :points_earned, default: 0, null: false

      t.references :country
      t.references :product

      t.index [:country_id, :product_id], unique: true

      t.timestamps
    end
  end
end
