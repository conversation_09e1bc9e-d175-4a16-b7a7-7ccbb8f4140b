class AddCounterCaches < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!

  def change
    # Add counter caches for frequently accessed counts

    # User counter caches
    add_column :users, :sales_count, :integer, default: 0, null: false
    add_column :users, :orders_count, :integer, default: 0, null: false
    add_column :users, :approved_sales_count, :integer, default: 0, null: false
    add_column :users, :pending_sales_count, :integer, default: 0, null: false

    # Brand counter caches
    add_column :brands, :sales_count, :integer, default: 0, null: false
    add_column :brands, :orders_count, :integer, default: 0, null: false
    add_column :brands, :approved_sales_count, :integer, default: 0, null: false

    # Product counter caches
    add_column :products, :sales_count, :integer, default: 0, null: false
    add_column :products, :line_items_count, :integer, default: 0, null: false

    # Region counter caches
    add_column :regions, :sales_count, :integer, default: 0, null: false
    add_column :regions, :orders_count, :integer, default: 0, null: false

    # Order counter caches
    add_column :orders, :line_items_count, :integer, default: 0, null: false

    # Add indexes for counter cache columns that might be queried
    add_index :users, :sales_count, algorithm: :concurrently
    add_index :users, :orders_count, algorithm: :concurrently
    add_index :brands, :sales_count, algorithm: :concurrently
    add_index :products, :sales_count, algorithm: :concurrently
  end
end
