class AddIndexesToUsers < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!

  def change
    # Add indexes for frequently queried fields
    add_index :users, :status, algorithm: :concurrently, if_not_exists: true
    add_index :users, :role, algorithm: :concurrently, if_not_exists: true
    add_index :users, :last_seen, algorithm: :concurrently, if_not_exists: true
    add_index :users, [:status, :role], algorithm: :concurrently, if_not_exists: true
    add_index :users, :approved, algorithm: :concurrently, if_not_exists: true
    add_index :users, :email_frequency, algorithm: :concurrently, if_not_exists: true

    # Add database constraints for data integrity (without validation for safety)
    add_check_constraint :users, "email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'",
      name: "valid_email_format", validate: false
    add_check_constraint :users, "phone_number ~ '^[+]?[1-9][0-9]{0,15}$'",
      name: "valid_phone_format", validate: false
  end
end
