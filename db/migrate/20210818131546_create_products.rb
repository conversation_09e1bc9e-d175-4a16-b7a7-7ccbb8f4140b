class CreateProducts < ActiveRecord::Migration[6.1]
  def change
    create_table :products do |t|
      t.string :name
      t.text :description
      t.decimal :msrp, precision: 10, scale: 2
      t.integer :points_needed
      t.integer :points_earned
      t.string :sku
      t.boolean :active, default: true
      t.jsonb :image_data, default: {}

      t.references :category, null: false

      t.timestamps
    end
  end
end
