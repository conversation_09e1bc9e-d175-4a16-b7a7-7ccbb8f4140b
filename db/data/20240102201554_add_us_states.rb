# frozen_string_literal: true

class AddUsStates < ActiveRecord::Migration[7.1]
  def up
    country = Country.find_by(abbreviation: "US")

    states = []

    region = Region.find_by(name: "SE")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Alabama", abbreviation: "AL"},
      {name: "Florida", abbreviation: "FL"},
      {name: "Georgia", abbreviation: "GA"},
      {name: "Kentucky", abbreviation: "KY"},
      {name: "Mississippi", abbreviation: "MS"},
      {name: "North Carolina", abbreviation: "NC"},
      {name: "South Carolina", abbreviation: "SC"},
      {name: "Puerto Rico", abbreviation: "PR"},
      {name: "Tennessee", abbreviation: "TN"}
    ])

    region = Region.find_by(name: "NE")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Connecticut", abbreviation: "CT"},
      {name: "Delaware", abbreviation: "DE"},
      {name: "District of Columbia", abbreviation: "DC"},
      {name: "Maryland", abbreviation: "MD"},
      {name: "Massachusetts", abbreviation: "MA"},
      {name: "New Hampshire", abbreviation: "NH"},
      {name: "Maine", abbreviation: "ME"},
      {name: "New Jersey", abbreviation: "NJ"},
      {name: "New York", abbreviation: "NY"},
      {name: "Pennsylvania", abbreviation: "PA"},
      {name: "Rhode Island", abbreviation: "RI"},
      {name: "Vermont", abbreviation: "VT"},
      {name: "Virginia", abbreviation: "VA"},
      {name: "West Virgina", abbreviation: "WV"}
    ])

    region = Region.find_by(name: "NW")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Alaska", abbreviation: "AK"},
      {name: "Idaho", abbreviation: "ID"},
      {name: "Montana", abbreviation: "MT"},
      {name: "Nebraska", abbreviation: "NE"},
      {name: "North Dakota", abbreviation: "ND"},
      {name: "South Dakota", abbreviation: "SD"},
      {name: "Oregon", abbreviation: "OR"},
      {name: "Washington", abbreviation: "WA"},
      {name: "Wyoming", abbreviation: "WY"}
    ])

    region = Region.find_by(name: "UMW")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Illinois", abbreviation: "IL"},
      {name: "Indiana", abbreviation: "IN"},
      {name: "Iowa", abbreviation: "IA"},
      {name: "Michigan", abbreviation: "MI"},
      {name: "Minnesota", abbreviation: "MN"},
      {name: "Ohio", abbreviation: "OH"},
      {name: "Wisconsin", abbreviation: "WI"}
    ])

    region = Region.find_by(name: "TALO")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Arkansas", abbreviation: "AR"},
      {name: "Kansas", abbreviation: "KS"},
      {name: "Louisiana", abbreviation: "LA"},
      {name: "Missouri", abbreviation: "MO"},
      {name: "Oklahoma", abbreviation: "OK"},
      {name: "Texas", abbreviation: "TX"}
    ])

    region = Region.find_by(name: "SW")
    states << State.create_with(region_id: region.id, country_id: country.id).new([
      {name: "Arizona", abbreviation: "AZ"},
      {name: "California", abbreviation: "CA"},
      {name: "Colorado", abbreviation: "CO"},
      {name: "Hawaii", abbreviation: "HI"},
      {name: "Nevada", abbreviation: "NV"},
      {name: "New Mexico", abbreviation: "NM"},
      {name: "Utah", abbreviation: "UT"}
    ])

    State.import! states.flatten, on_duplicate_key_update: {conflict_target: [:abbreviation], columns: [:country_id]}
  end

  def down
    raise ActiveRecord::IrreversibleMigration
  end
end
