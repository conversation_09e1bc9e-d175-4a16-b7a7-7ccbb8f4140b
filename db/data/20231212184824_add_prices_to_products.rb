# frozen_string_literal: true

class AddPricesToProducts < ActiveRecord::Migration[7.1]
  def up
    country = Country.find_by(abbreviation: "US")

    Product.all.each do |product|
      product.prices.create(country: country, msrp_in_cents: product.msrp * 100, points_needed: product.points_needed, points_earned: product.points_earned)
      product.upc = "829576019311" if product.upc.blank?
      product.save!
    end
  end

  def down
    raise ActiveRecord::IrreversibleMigration
  end
end
