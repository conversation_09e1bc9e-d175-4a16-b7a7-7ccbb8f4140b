# frozen_string_literal: true

class PopulateCounterCaches < ActiveRecord::Migration[7.1]
  def up
    # Populate User counter caches
    User.find_each do |user|
      User.reset_counters(user.id, :sales, :orders)

      # Update conditional counter caches manually
      approved_sales_count = user.sales.approved.count
      pending_sales_count = user.sales.pending.count

      user.update_columns(
        approved_sales_count: approved_sales_count,
        pending_sales_count: pending_sales_count
      )
    end

    # Populate Brand counter caches
    Brand.find_each do |brand|
      Brand.reset_counters(brand.id, :sales, :orders)

      # Update conditional counter caches manually
      approved_sales_count = brand.sales.approved.count

      brand.update_columns(
        approved_sales_count: approved_sales_count
      )
    end

    # Populate Product counter caches
    Product.find_each do |product|
      Product.reset_counters(product.id, :sales, :line_items)
    end

    # Populate Region counter caches
    Region.find_each do |region|
      Region.reset_counters(region.id, :sales, :orders)
    end

    # Populate Order counter caches
    Order.find_each do |order|
      Order.reset_counters(order.id, :line_items)
    end

    puts "Counter caches populated successfully!"
  end

  def down
    # Reset all counter cache columns to 0
    User.update_all(
      sales_count: 0,
      orders_count: 0,
      approved_sales_count: 0,
      pending_sales_count: 0
    )

    Brand.update_all(
      sales_count: 0,
      orders_count: 0,
      approved_sales_count: 0
    )

    Product.update_all(
      sales_count: 0,
      line_items_count: 0
    )

    Region.update_all(
      sales_count: 0,
      orders_count: 0
    )

    Order.update_all(
      line_items_count: 0
    )
  end
end
