# frozen_string_literal: true

class CreateRegions < ActiveRecord::Migration[7.1]
  def up
    Region.find_or_create_by!(name: "SE")
    Region.find_or_create_by!(name: "NE")
    Region.find_or_create_by!(name: "SW")
    Region.find_or_create_by!(name: "NW")
    Region.find_or_create_by!(name: "TALO")
    Region.find_or_create_by!(name: "UMW")
  end

  def down
    raise ActiveRecord::IrreversibleMigration
  end
end
