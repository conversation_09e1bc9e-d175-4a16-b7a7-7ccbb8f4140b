# frozen_string_literal: true

class NormalizePhoneNumbers < ActiveRecord::Migration[7.1]
  def up
    # Temporarily remove the phone format constraint to allow normalization
    remove_check_constraint :users, name: "valid_phone_format" if check_constraint_exists?(:users, name: "valid_phone_format")

    normalize_phone_numbers

    # Add back the constraint with updated pattern that allows numbers starting with 0
    add_check_constraint :users, "phone_number ~ '^[+]?[0-9]{1,16}$'", name: "valid_phone_format", validate: false

    puts "Phone numbers normalized successfully!"
    puts "Updated phone format constraint to allow numbers starting with 0"
  end

  def down
    # This migration is irreversible as we cannot restore the original phone number formats
    raise ActiveRecord::IrreversibleMigration
  end

  private

  def normalize_phone_numbers
    puts "Normalizing phone numbers for existing records..."

    user_count = 0
    store_count = 0

    # Normalize User phone numbers
    User.find_each do |user|
      if user.phone_number.present?
        original_phone = user.phone_number
        normalized_phone = normalize_phone_number(original_phone)

        if normalized_phone != original_phone
          user.update_column(:phone_number, normalized_phone)
          user_count += 1
        end
      end
    end

    # Normalize Store phone numbers
    Store.find_each do |store|
      if store.phone_number.present?
        original_phone = store.phone_number
        normalized_phone = normalize_phone_number(original_phone)

        if normalized_phone != original_phone
          store.update_column(:phone_number, normalized_phone)
          store_count += 1
        end
      end
    end

    puts "Normalized #{user_count} user phone numbers"
    puts "Normalized #{store_count} store phone numbers"
  end

  def normalize_phone_number(phone)
    return phone if phone.blank?

    # Remove all non-digit characters (same logic as the model normalization)
    phone.delete("^0-9")
  end
end
