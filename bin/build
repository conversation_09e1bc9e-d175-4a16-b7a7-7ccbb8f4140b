#!/usr/bin/env bash

# ZeissPoints Build Script
# This script builds both CSS and JavaScript assets for production

set -e

echo "🏗️  Building ZeissPoints assets..."

# Build JavaScript with ESBuild
echo "📦 Building JavaScript..."
NODE_ENV=production node esbuild.config.js

# Build CSS with Tailwind
echo "🎨 Building CSS..."
rails tailwindcss:build

# Precompile Rails assets
echo "🚀 Precompiling Rails assets..."
rails assets:precompile

echo "✅ Build completed successfully!"
echo "📁 Assets are ready in app/assets/builds/"
