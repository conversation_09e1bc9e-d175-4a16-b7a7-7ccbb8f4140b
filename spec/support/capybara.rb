require "capybara/cuprite"

Capybara.disable_animation = true
Capybara.server = :puma

WebMock.disable_net_connect!(allow_localhost: true)

Capybara.register_driver :cuprite do |app|
  Capybara::Cuprite::Driver.new(app, window_size: [1200, 800], inspector: ENV["INSPECTOR"])
end

Capybara.javascript_driver = :cuprite

Capybara.configure do |config|
  config.automatic_label_click = true
  config.default_max_wait_time = 5 # seconds
  config.default_driver = :cuprite
  config.disable_animation = true
  config.ignore_hidden_elements = true
  config.test_id = "data-testid"
end

RSpec.configure do |config|
  config.before(:each, type: :system) do
    driven_by :cuprite
  end
end
