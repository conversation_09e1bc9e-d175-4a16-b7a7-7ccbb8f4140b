module ControllerMacros
  # def login_user(user)
  #   # before(:each) do
  #     @request.env["devise.mapping"] = Devise.mappings[:user]
  #     # user = FactoryBot.create(:user)
  #     user.confirm #only if account is confirmable
  #     sign_in user
  #   # end
  # end
  def login_as(user)
    # before do
    # @request.env["devise.mapping"] = Devise.mappings[:user]
    # subject.sign_in user # was sign_in user
    sign_in(user)
    # end
  end
end

RSpec.configure do |config|
  config.include ControllerMacros, type: :system
end
