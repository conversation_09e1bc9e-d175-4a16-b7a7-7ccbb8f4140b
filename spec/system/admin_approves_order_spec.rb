require "rails_helper"

RSpec.describe "Admin approves order", type: :system do
  let(:country) { create(:country, abbreviation: "US") }
  let(:brand) { create(:brand) }
  let(:region) { create(:region) }
  let(:store) { create(:store, brand: brand, region: region) }
  let(:user) { create(:user, store: store) }
  let(:admin) { create(:admin) }
  let(:product) { create(:product) }
  let(:price) { create(:price, product: product, country: country) }
  let(:order) do
    create(:order,
      user: user,
      brand: brand,
      region: region,
      status: "pending",
      points: 100,
      notes: "Test order")
  end
  let!(:line_item) { create(:line_item, order: order, product: product, quantity: 1, points: 100) }
  let!(:wallet) { create(:wallet, user: user, balance: 1000) }

  before do
    allow(Current).to receive(:country).and_return(country)
    price # Ensure price exists
  end

  scenario "admin approves a pending order" do
    # Log in as admin
    login_as(admin)

    # Visit the order page
    visit order_path(order)

    # Verify order is pending
    expect(page).to have_content("pending")

    # Approve the order
    click_button "Approve"

    # Verify success message
    expect(page).to have_content("Order was successfully updated")

    # Verify order status is now approved
    expect(page).to have_content("approved")

    # Verify admin name is shown
    expect(page).to have_content(admin.name)

    # Verify approval timestamp is shown (month should be visible)
    expect(page).to have_content(Time.zone.now.strftime("%B"))

    # Verify user's wallet was debited
    user.wallet.reload
    expect(user.wallet.balance).to eq(900) # 1000 - 100
  end

  scenario "admin declines a pending order" do
    # Log in as admin
    login_as(admin)

    # Visit the order page
    visit order_path(order)

    # Verify order is pending
    expect(page).to have_content("pending")

    # Decline the order
    click_button "Decline"

    # Verify success message
    expect(page).to have_content("Order was successfully updated")

    # Verify order status is now declined
    expect(page).to have_content("declined")

    # Verify admin name is shown
    expect(page).to have_content(admin.name)

    # Verify user's wallet was not debited
    user.wallet.reload
    expect(user.wallet.balance).to eq(1000) # Unchanged
  end

  scenario "user receives notification when order is approved" do
    # Set up mailer expectations
    expect {
      # Log in as admin
      login_as(admin)

      # Visit the order page
      visit order_path(order)

      # Approve the order
      click_button "Approve"
    }.to have_enqueued_job.on_queue("mailers")
  end

  scenario "user can see their approved order" do
    # First approve the order as admin
    login_as(admin)
    visit order_path(order)
    click_button "Approve"

    # Then log in as user
    login_as(user)

    # Visit orders page
    visit orders_path

    # Verify approved order is visible
    expect(page).to have_content(product.name)
    expect(page).to have_content("approved")

    # Visit the specific order
    click_link product.name

    # Verify order details
    expect(page).to have_content(product.name)
    expect(page).to have_content("approved")
    expect(page).to have_content(admin.name)
    expect(page).to have_content("Test order")
  end

  scenario "admin can process an approved order" do
    # First approve the order
    login_as(admin)
    visit order_path(order)
    click_button "Approve"

    # Then process the order
    click_button "Process"

    # Fill in SAP ID
    fill_in "SAP ID", with: "SAP12345"
    click_button "Save"

    # Verify success message
    expect(page).to have_content("Order was successfully updated")

    # Verify order status is now processed
    expect(page).to have_content("processed")

    # Verify SAP ID is shown
    expect(page).to have_content("SAP12345")
  end
end
