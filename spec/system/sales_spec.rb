require "rails_helper"

RSpec.describe "Sales", type: :system do
  let(:country) { create(:country, abbreviation: "US") }
  let(:price) { create(:price, country: country) }
  let(:brand) { create(:brand) }
  let(:region) { create(:region) }
  # Create a state in the region to ensure proper association chain
  let(:state) { create(:state, region: region, country: country) }
  # Create store with brand only first
  let(:store) { create(:store, brand: brand) }
  # Ensure the store has a valid address with state and country
  let(:user) { create(:user, store: store) }

  before do
    login_as(user)
    create(:product, name: "Testers", prices: [price])
    visit new_sale_path
  end

  scenario "with valid inputs" do
    # Select product - using the correct selector based on the form structure
    # Looking at the form.html.erb, the product selector is likely within a div with sale_product_id
    within "#product" do
      click_button

      within "div[role='listbox'] ul" do
        find("li").click
      end
    end

    # Enter serial number
    fill_in "Serial number", with: "3782539273561"

    # Select date
    within "#sold_on" do
      find(".form-control").click

      date = Time.current

      within find(:xpath, "/html/body/div[contains(@class, 'flatpickr-calendar')]") do
        within ".flatpickr-months > .flatpickr-month > .flatpickr-current-month" do
          find("select.flatpickr-monthDropdown-months > option:nth-child(#{date.month})").select_option
          find("input.cur-year").set(date.year)
        end

        within find(".flatpickr-innerContainer > .flatpickr-rContainer > .flatpickr-days") do
          find(".flatpickr-day:not(.prevMonthDay):not(.nextMonthDay)", text: date.day, exact_text: true).click
        end
      end
    end

    # Select origin
    choose "Internet"

    # Add notes
    fill_in "Notes", with: "Customer was very satisfied"

    # Submit form
    click_button "Create Sale"

    # Verify success
    expect(page).to have_content("Sale was successfully created.")

    # Verify sale details page shows correct information
    expect(page).to have_content("Testers")
    expect(page).to have_content("3782539273561")
    expect(page).to have_content("Internet")
    expect(page).to have_content("Customer was very satisfied")
    expect(page).to have_content("pending")
  end

  scenario "with no product" do
    fill_in "Serial number", with: "3782539273561"

    within "#sold_on" do
      find(".form-control").click

      date = Time.current

      within find(:xpath, "/html/body/div[contains(@class, 'flatpickr-calendar')]") do
        within ".flatpickr-months > .flatpickr-month > .flatpickr-current-month" do
          find("select.flatpickr-monthDropdown-months > option:nth-child(#{date.month})").select_option
          find("input.cur-year").set(date.year)
        end

        within find(".flatpickr-innerContainer > .flatpickr-rContainer > .flatpickr-days") do
          find(".flatpickr-day:not(.prevMonthDay):not(.nextMonthDay)", text: date.day, exact_text: true).click
        end
      end
    end

    choose "Internet"

    click_button "Create Sale"
    expect(page).to have_content("Product must exist")
  end

  scenario "with no date" do
    within "#sale_product_id" do
      click_button

      within "div[role='listbox'] ul" do
        find("li").click
      end
    end

    fill_in "Serial number", with: "3782539273561"

    choose "Internet"

    click_button "Create Sale"
    expect(page).to have_content("Sold at can't be blank")
  end

  scenario "with invalid serial number" do
    within "#product" do
      click_button

      within "div[role='listbox'] ul" do
        find("li").click
      end
    end

    # Enter too short serial number
    fill_in "Serial number", with: "123456"

    within "#sold_on" do
      find(".form-control").click

      date = Time.current

      within find(:xpath, "/html/body/div[contains(@class, 'flatpickr-calendar')]") do
        within ".flatpickr-months > .flatpickr-month > .flatpickr-current-month" do
          find("select.flatpickr-monthDropdown-months > option:nth-child(#{date.month})").select_option
          find("input.cur-year").set(date.year)
        end

        within find(".flatpickr-innerContainer > .flatpickr-rContainer > .flatpickr-days") do
          find(".flatpickr-day:not(.prevMonthDay):not(.nextMonthDay)", text: date.day, exact_text: true).click
        end
      end
    end

    choose "Internet"

    click_button "Create Sale"
    expect(page).to have_content("Serial number is too short")
  end

  scenario "with future date" do
    within "#product" do
      click_button

      within "div[role='listbox'] ul" do
        find("li").click
      end
    end

    fill_in "Serial number", with: "3782539273561"

    within "#sold_on" do
      find(".form-control").click

      date = 1.month.from_now

      within find(:xpath, "/html/body/div[contains(@class, 'flatpickr-calendar')]") do
        within ".flatpickr-months > .flatpickr-month > .flatpickr-current-month" do
          find("select.flatpickr-monthDropdown-months > option:nth-child(#{date.month})").select_option
          find("input.cur-year").set(date.year)
        end

        within find(".flatpickr-innerContainer > .flatpickr-rContainer > .flatpickr-days") do
          find(".flatpickr-day:not(.prevMonthDay):not(.nextMonthDay)", text: date.day, exact_text: true).click
        end
      end
    end

    choose "Internet"

    click_button "Create Sale"
    expect(page).to have_content("Sold at must be on or before today")
  end
end
