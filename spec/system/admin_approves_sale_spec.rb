require "rails_helper"

RSpec.describe "Admin approves sale", type: :system do
  let(:country) { create(:country, abbreviation: "US") }
  let(:price) { create(:price, country: country) }
  let(:brand) { create(:brand) }
  let(:region) { create(:region) }
  let(:store) { create(:store, brand: brand, region: region) }
  let(:user) { create(:user, store: store) }
  let(:admin) { create(:admin) }
  let(:product) { create(:product, prices: [price]) }
  let(:sale) { create(:sale, user: user, product: product, brand: brand, region: region, status: "pending") }

  before do
    allow(Current).to receive(:country).and_return(country)
  end

  scenario "admin approves a pending sale" do
    # Log in as admin
    login_as(admin)

    # Visit the sale page
    visit sale_path(sale)

    # Verify sale is pending
    expect(page).to have_content("pending")

    # Approve the sale
    click_button "Approve"

    # Verify success message
    expect(page).to have_content("Sale was successfully updated")

    # Verify sale status is now approved
    expect(page).to have_content("approved")

    # Verify admin name is shown
    expect(page).to have_content(admin.name)

    # Verify approval timestamp is shown
    expect(page).to have_content(Time.zone.now.strftime("%B"))
  end

  scenario "admin declines a pending sale" do
    # Log in as admin
    login_as(admin)

    # Visit the sale page
    visit sale_path(sale)

    # Verify sale is pending
    expect(page).to have_content("pending")

    # Decline the sale
    click_button "Decline"

    # Verify success message
    expect(page).to have_content("Sale was successfully updated")

    # Verify sale status is now declined
    expect(page).to have_content("declined")

    # Verify admin name is shown
    expect(page).to have_content(admin.name)
  end

  scenario "user receives notification when sale is approved" do
    # Set up mailer expectations
    expect {
      # Log in as admin
      login_as(admin)

      # Visit the sale page
      visit sale_path(sale)

      # Approve the sale
      click_button "Approve"
    }.to have_enqueued_job.on_queue("mailers")

    # Check that the notification was sent to the user
    expect(UpdatedSaleNotifier).to have_been_delivered_to(user)
  end

  scenario "user can see their approved sale" do
    # First approve the sale as admin
    login_as(admin)
    visit sale_path(sale)
    click_button "Approve"

    # Then log in as user
    login_as(user)

    # Visit sales page
    visit sales_path

    # Verify approved sale is visible
    expect(page).to have_content(product.name)
    expect(page).to have_content("approved")

    # Visit the specific sale
    click_link product.name

    # Verify sale details
    expect(page).to have_content(product.name)
    expect(page).to have_content(sale.serial_number)
    expect(page).to have_content("approved")
    expect(page).to have_content(admin.name)
  end
end
