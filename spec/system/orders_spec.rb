require "rails_helper"

RSpec.describe "Orders", type: :system do
  let(:user) { create(:user) }
  let(:country) { create(:country, abbreviation: "US") }
  let(:brand) { create(:brand) }
  let(:region) { create(:region) }
  # Create a state in the region to ensure proper association chain
  let(:state) { create(:state, region: region) }
  # Create store with state instead of region to avoid the read-only association
  let(:store) { create(:store, brand: brand, state: state) }
  let(:category) { create(:category, brand: brand) }
  let(:product) { create(:product, category: category) }
  let(:price) { create(:price, product: product, country: country) }

  before do
    # Set up user with store and wallet
    user.update(store: store)
    create(:wallet, user: user, balance: 1000)

    # Set up Current.country
    allow(Current).to receive(:country).and_return(country)

    # Create product with price
    price

    # Log in
    login_as(user)
  end

  describe "Creating a new order" do
    it "allows a user to add products to cart and place an order" do
      # Visit shop page
      visit shop_path

      # Add product to cart
      within "#product_#{product.id}" do
        click_button "Add to Cart"
      end

      # Verify product was added to cart
      expect(page).to have_content("Item added to cart")

      # Go to cart
      visit cart_path
      expect(page).to have_content(product.name)

      # Proceed to checkout
      click_button "Checkout"

      # Fill in order details
      fill_in "Notes", with: "Please deliver ASAP"
      select "Home", from: "Ship to"

      # Submit order
      click_button "Place Order"

      # Verify order was created
      expect(page).to have_content("Order was successfully created")

      # Verify order details
      expect(page).to have_content(product.name)
      expect(page).to have_content("pending")
      expect(page).to have_content("Please deliver ASAP")

      # Verify cart is empty after order placement
      visit cart_path
      expect(page).not_to have_content(product.name)

      # Verify order appears in orders list
      visit orders_path
      expect(page).to have_content("pending")
    end
  end

  describe "Admin creating an order for a user" do
    let(:admin) { create(:user, :admin) }

    before do
      login_as(admin)
    end

    it "allows an admin to create an order for a user" do
      # Visit new order page for user
      visit new_user_order_path(user)

      # Add product to admin cart
      within "#product_#{product.id}" do
        click_button "Add"
      end

      # Verify product was added to admin cart
      expect(page).to have_content(product.name)

      # Fill in order details
      fill_in "Notes", with: "Admin created order"

      # Submit order
      click_button "Create Order"

      # Verify order was created
      expect(page).to have_content("Order was successfully created")

      # Verify order details
      expect(page).to have_content(product.name)
      expect(page).to have_content("approved")
      expect(page).to have_content("Admin created order")

      # Verify order appears in orders list
      visit orders_path
      expect(page).to have_content(user.name)
    end
  end
end
