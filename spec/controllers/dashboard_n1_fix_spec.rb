require 'rails_helper'

RSpec.describe "Dashboard N+1 Query Fix", type: :controller do
  controller(DashboardController) do
    # Override authentication for testing
    def authenticate_user!
      # Skip authentication
    end
    
    def current_user
      @current_user ||= User.new(role: :super_admin)
    end
  end

  describe "N+1 Query Optimization" do
    before do
      # Create test data directly in database to avoid model callbacks
      @brand1 = Brand.create!(name: "Brand 1")
      @brand2 = Brand.create!(name: "Brand 2")
      
      @store1 = Store.create!(name: "Store 1", brand: @brand1)
      @store2 = Store.create!(name: "Store 2", brand: @brand1)
      @store3 = Store.create!(name: "Store 3", brand: @brand2)
      
      # Create users with minimal data
      @user1 = User.create!(
        email: "<EMAIL>", 
        password: "password123", 
        first_name: "User", 
        last_name: "One", 
        store: @store1, 
        phone_number: "1111111111"
      )
      @user2 = User.create!(
        email: "<EMAIL>", 
        password: "password123", 
        first_name: "User", 
        last_name: "Two", 
        store: @store2, 
        phone_number: "2222222222"
      )
      @user3 = User.create!(
        email: "<EMAIL>", 
        password: "password123", 
        first_name: "User", 
        last_name: "Three", 
        store: @store3, 
        phone_number: "3333333333"
      )
      
      # Create regions
      @region1 = Region.create!(name: "Region 1")
      @region2 = Region.create!(name: "Region 2")
      
      # Create categories
      @category1 = Category.create!(name: "Category 1", brand: @brand1)
      @category2 = Category.create!(name: "Category 2", brand: @brand2)
      
      # Create products
      @product1 = Product.create!(
        name: "Product 1", 
        description: "Test product 1", 
        category: @category1, 
        sku: "SKU1", 
        upc: "036000291452"
      )
      @product2 = Product.create!(
        name: "Product 2", 
        description: "Test product 2", 
        category: @category2, 
        sku: "SKU2", 
        upc: "012000161155"
      )
      
      # Create sales using direct SQL to avoid callbacks
      timestamp = Time.current.strftime("%Y-%m-%d %H:%M:%S")
      sql = "INSERT INTO sales (brand_id, region_id, user_id, product_id, status, points, sold_at, serial_number, created_at, updated_at) VALUES " \
            "(#{@brand1.id}, #{@region1.id}, #{@user1.id}, #{@product1.id}, 1, 100, '#{timestamp}', '1234567890', '#{timestamp}', '#{timestamp}'), " \
            "(#{@brand1.id}, #{@region1.id}, #{@user2.id}, #{@product1.id}, 1, 150, '#{timestamp}', '1234567891', '#{timestamp}', '#{timestamp}'), " \
            "(#{@brand2.id}, #{@region2.id}, #{@user3.id}, #{@product2.id}, 1, 200, '#{timestamp}', '1234567892', '#{timestamp}', '#{timestamp}')"
      ActiveRecord::Base.connection.execute(sql)
    end

    it "computes brand statistics efficiently" do
      controller = DashboardController.new
      
      # Test the optimized method
      brand_stats = controller.send(:compute_brand_statistics)
      
      expect(brand_stats).to be_an(Array)
      expect(brand_stats.length).to eq(2)
      
      brand1_stat = brand_stats.find { |stat| stat[:brand].id == @brand1.id }
      brand2_stat = brand_stats.find { |stat| stat[:brand].id == @brand2.id }
      
      expect(brand1_stat[:stores_count]).to eq(2)
      expect(brand1_stat[:users_count]).to eq(2)
      expect(brand1_stat[:approved_points]).to eq(250) # 100 + 150
      
      expect(brand2_stat[:stores_count]).to eq(1)
      expect(brand2_stat[:users_count]).to eq(1)
      expect(brand2_stat[:approved_points]).to eq(200)
    end

    it "computes user growth statistics efficiently" do
      controller = DashboardController.new
      
      # Test the optimized method
      user_growth_stats = controller.send(:compute_user_growth_statistics)
      
      expect(user_growth_stats).to be_an(Array)
      expect(user_growth_stats.length).to eq(6) # 6 months
      
      # Check structure
      user_growth_stats.each do |stat|
        expect(stat).to have_key(:month_start)
        expect(stat).to have_key(:month_name)
        expect(stat).to have_key(:user_count)
        expect(stat[:user_count]).to be_a(Integer)
      end
    end

    it "executes minimal queries for brand statistics" do
      controller = DashboardController.new
      
      # Count queries executed
      query_count = 0
      ActiveSupport::Notifications.subscribe "sql.active_record" do |*args|
        query_count += 1
      end
      
      controller.send(:compute_brand_statistics)
      
      # Should execute only a few aggregate queries, not N+1
      expect(query_count).to be < 10 # Much less than N+1 would be
    end

    it "demonstrates the difference between old and new approaches" do
      # Simulate old N+1 approach
      old_query_count = 0
      ActiveSupport::Notifications.subscribe "sql.active_record" do |*args|
        old_query_count += 1
      end
      
      # Old approach (simulated)
      Brand.includes(:stores, :users, :sales).each do |brand|
        brand.stores.count
        brand.users.count
        brand.sales.approved.sum(:points)
      end
      
      old_queries = old_query_count
      
      # Reset counter
      new_query_count = 0
      ActiveSupport::Notifications.subscribe "sql.active_record" do |*args|
        new_query_count += 1
      end
      
      # New approach
      controller = DashboardController.new
      controller.send(:compute_brand_statistics)
      
      new_queries = new_query_count - old_query_count
      
      # New approach should use significantly fewer queries
      expect(new_queries).to be < old_queries
      puts "Old approach: #{old_queries} queries"
      puts "New approach: #{new_queries} queries"
      puts "Improvement: #{((old_queries - new_queries).to_f / old_queries * 100).round(1)}% reduction"
    end
  end
end
