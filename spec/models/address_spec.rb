# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string
#  city             :string           not null
#  latitude         :float
#  line1            :string           not null
#  line2            :string
#  longitude        :float
#  zip_code         :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable             (addressable_type,addressable_id)
#  index_addresses_on_country_id              (country_id)
#  index_addresses_on_latitude_and_longitude  (latitude,longitude)
#  index_addresses_on_state_id                (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#
require "rails_helper"

RSpec.describe Address, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:address1) }
    it { is_expected.to validate_presence_of(:city) }
    it { is_expected.to validate_presence_of(:zip_code) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:addressable).optional }
    it { is_expected.to belong_to(:state) }
    it { is_expected.to belong_to(:country) }
  end

  describe "Delegations" do
    it { is_expected.to delegate_method(:name).to(:state).with_prefix(true) }
    it { is_expected.to delegate_method(:name).to(:country).with_prefix(true) }
  end

  describe "Geocoding" do
    let(:address) { build(:address, latitude: nil, longitude: nil) }

    it "geocodes the address before validation" do
      expect(address).to receive(:geocode)
      address.valid?
    end

    it "sets latitude and longitude" do
      # Skip actual geocoding in test
      allow(address).to receive(:geocode).and_return([40.7128, -74.0060])
      address.valid?
      expect(address.latitude).to eq(40.7128)
      expect(address.longitude).to eq(-74.0060)
    end
  end

  describe "#full_address" do
    let(:state) { build(:state, abbreviation: "NY") }
    let(:country) { build(:country, abbreviation: "US") }
    let(:address) do
      build(:address,
        address1: "123 Main St",
        address2: "Apt 4B",
        city: "New York",
        state: state,
        country: country,
        zip_code: "10001")
    end

    it "returns a formatted full address" do
      expect(address.full_address).to eq("123 Main St, Apt 4B, New York, NY 10001, US")
    end

    it "handles nil address2" do
      address.address2 = nil
      expect(address.full_address).to eq("123 Main St, New York, NY 10001, US")
    end
  end
end
