# == Schema Information
#
# Table name: notes
#
#  id            :bigint           not null, primary key
#  noteable_type :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  noteable_id   :bigint
#  user_id       :bigint
#
# Indexes
#
#  index_notes_on_noteable  (noteable_type,noteable_id)
#  index_notes_on_user_id   (user_id)
#
require "rails_helper"

RSpec.describe Note, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:content) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:noteable).optional }
    it { is_expected.to belong_to(:user).optional }
  end

  describe "Scopes" do
    let!(:note1) { create(:note, created_at: 1.day.ago) }
    let!(:note2) { create(:note, created_at: 2.days.ago) }
    let!(:note3) { create(:note, created_at: 3.days.ago) }

    it "orders by created_at desc by default" do
      expect(Note.all.to_a).to eq([note1, note2, note3])
    end
  end

  describe "#to_s" do
    let(:note) { build(:note, content: "This is a test note") }

    it "returns the note content" do
      expect(note.to_s).to eq("This is a test note")
    end
  end
end
