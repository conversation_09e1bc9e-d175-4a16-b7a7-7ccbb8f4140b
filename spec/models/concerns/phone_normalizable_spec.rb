require "rails_helper"

RSpec.describe PhoneNormalizable, type: :model do
  let(:dummy_class) do
    Class.new do
      include PhoneNormalizable
    end
  end

  let(:dummy_instance) { dummy_class.new }

  describe ".normalize_phone_number" do
    it "removes all non-digit characters" do
      expect(dummy_class.normalize_phone_number("(*************")).to eq("5551234567")
      expect(dummy_class.normalize_phone_number("******-123-4567")).to eq("15551234567")
      expect(dummy_class.normalize_phone_number("************ ext 123")).to eq("5551234567123")
      expect(dummy_class.normalize_phone_number("************")).to eq("5551234567")
      expect(dummy_class.normalize_phone_number("(022) 235-48")).to eq("02223548")
    end

    it "handles edge cases" do
      expect(dummy_class.normalize_phone_number("")).to eq("")
      expect(dummy_class.normalize_phone_number(nil)).to be_nil
      expect(dummy_class.normalize_phone_number("abc")).to eq("")
      expect(dummy_class.normalize_phone_number("123")).to eq("123")
    end

    it "preserves already normalized numbers" do
      expect(dummy_class.normalize_phone_number("5551234567")).to eq("5551234567")
      expect(dummy_class.normalize_phone_number("15551234567")).to eq("15551234567")
      expect(dummy_class.normalize_phone_number("02223548")).to eq("02223548")
    end
  end

  describe "#normalize_phone_number" do
    it "works as an instance method" do
      expect(dummy_instance.normalize_phone_number("(*************")).to eq("5551234567")
    end
  end
end
