require "rails_helper"

RSpec.describe UserValidations, type: :model do
  let(:user) { build(:user) }

  describe "validations" do
    it "validates presence of required fields" do
      user.first_name = nil
      user.last_name = nil
      user.phone_number = nil

      expect(user).not_to be_valid
      expect(user.errors[:first_name]).to include("can't be blank")
      expect(user.errors[:last_name]).to include("can't be blank")
      expect(user.errors[:phone_number]).to include("can't be blank")
    end

    it "validates phone number format" do
      user.phone_number = "invalid-phone"
      expect(user).not_to be_valid
      expect(user.errors[:phone_number]).to include("must be a valid phone number")

      user.phone_number = "1234567890"
      expect(user).to be_valid

      user.phone_number = "+1234567890"
      expect(user).to be_valid

      user.phone_number = "0234567890"
      expect(user).to be_valid
    end

    it "validates email format" do
      user.email = "invalid-email"
      expect(user).not_to be_valid
      expect(user.errors[:email]).to include("must be a valid email address")

      user.email = "<EMAIL>"
      expect(user).to be_valid
    end

    it "validates status inclusion" do
      expect { user.status = "invalid_status" }.to raise_error(ArgumentError)
    end
  end

  describe "status transitions" do
    it "allows valid status transitions" do
      user.status = "new"
      user.save!

      user.status = "active"
      expect(user).to be_valid

      user.save!
      user.status = "inactive"
      expect(user).to be_valid
    end

    it "prevents invalid status transitions" do
      user.status = "new"
      user.save!

      user.status = "deleted"
      expect(user).not_to be_valid
      expect(user.errors[:status]).to include("cannot transition from new to deleted")
    end
  end

  describe "admin role validations" do
    it "validates admin_region assignment" do
      region = create(:region)
      user.admin_region = region
      user.role = "regular_user"

      expect(user).not_to be_valid
      expect(user.errors[:admin_region]).to include("can only be assigned to region admins or higher")

      user.role = "region_admin"
      expect(user).to be_valid
    end

    it "validates admin_brand assignment" do
      brand = create(:brand)
      user.admin_brand = brand
      user.role = "region_admin"

      expect(user).not_to be_valid
      expect(user.errors[:admin_brand]).to include("can only be assigned to admins or super admins")

      user.role = "admin"
      expect(user).to be_valid
    end
  end
end
