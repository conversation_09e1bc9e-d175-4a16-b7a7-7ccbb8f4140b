# == Schema Information
#
# Table name: prices
#
#  id            :bigint           not null, primary key
#  msrp_in_cents :integer          default(0), not null
#  points_earned :integer          default(0), not null
#  points_needed :integer          default(0), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint
#  product_id    :bigint
#
# Indexes
#
#  index_prices_on_country_id                 (country_id)
#  index_prices_on_country_id_and_product_id  (country_id,product_id) UNIQUE
#  index_prices_on_product_id                 (product_id)
#
require "rails_helper"

RSpec.describe Price, type: :model do
  describe "validations" do
    subject { build(:price) }
    it { is_expected.to validate_uniqueness_of(:country_id).scoped_to(:product_id) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:product) }
    it { is_expected.to belong_to(:country) }
  end
end
