# == Schema Information
#
# Table name: adjustments
#
#  id         :bigint           not null, primary key
#  kind       :integer          default("debit")
#  notes      :text             not null
#  points     :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_adjustments_on_admin_id  (admin_id)
#  index_adjustments_on_user_id   (user_id)
#
require "rails_helper"

RSpec.describe Adjustment, type: :model do
  describe "Associations" do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to belong_to(:admin).class_name("User") }
    it { is_expected.to have_one(:activity).dependent(:destroy) }
  end

  describe "Validations" do
    it { is_expected.to validate_presence_of(:notes) }
    it { is_expected.to validate_presence_of(:user) }
    it { is_expected.to validate_presence_of(:admin) }
  end
end
