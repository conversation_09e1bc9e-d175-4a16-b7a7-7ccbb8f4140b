# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  notes         :text
#  origin        :integer          default("internet")
#  points        :integer
#  serial_number :string
#  sold_at       :datetime
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint
#  product_id    :bigint           not null
#  promotion_id  :bigint
#  region_id     :bigint
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_admin_id                             (admin_id)
#  index_sales_on_brand_id                             (brand_id)
#  index_sales_on_brand_id_and_status_and_created_at   (brand_id,status,created_at)
#  index_sales_on_created_at_and_status                (created_at,status)
#  index_sales_on_product_id                           (product_id)
#  index_sales_on_promotion_id                         (promotion_id)
#  index_sales_on_region_id                            (region_id)
#  index_sales_on_region_id_and_status_and_created_at  (region_id,status,created_at)
#  index_sales_on_sold_at_and_status                   (sold_at,status)
#  index_sales_on_status_and_created_at                (status,created_at)
#  index_sales_on_user_id                              (user_id)
#  index_sales_on_user_id_and_status_and_created_at    (user_id,status,created_at)
#
require "rails_helper"

RSpec.describe Sale, type: :model do
  describe "Validations" do
    subject { build(:sale) }
    it { is_expected.to validate_presence_of(:serial_number) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_presence_of(:sold_at) }
    it { is_expected.to validate_uniqueness_of(:serial_number).case_insensitive }
    it { is_expected.to validate_numericality_of(:serial_number).only_integer }
    it { is_expected.to validate_length_of(:serial_number).is_at_least(7) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to belong_to(:product) }
    it { is_expected.to belong_to(:admin).class_name("User").optional }
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to belong_to(:region) }
    it { is_expected.to belong_to(:promotion).optional }
    it { is_expected.to have_one(:activity).dependent(:destroy) }
    it { is_expected.to have_many(:notations).dependent(:destroy).class_name("Note") }
    it { is_expected.to accept_nested_attributes_for(:notations) }
  end

  it { is_expected.to define_enum_for(:status).with_values(pending: 0, approved: 1, declined: 2) }
  it { is_expected.to define_enum_for(:origin).with_values(internet: 0, in_store: 1) }

  describe "Delegations" do
    let(:sale) { build(:sale) }

    it { is_expected.to delegate_method(:name).to(:product).with_prefix(true) }
    it { is_expected.to delegate_method(:name).to(:user).with_prefix(true) }
    it { is_expected.to delegate_method(:name).to(:admin).with_prefix(true).allow_nil }
    it { is_expected.to delegate_method(:name).to(:brand).with_prefix(true) }
  end

  describe "Attachments" do
    it { is_expected.to have_one_attached(:receipt) }
  end

  describe "Scopes" do
    it "has a meilisearch_import scope" do
      expect(Sale.meilisearch_import.to_sql).to include("INNER JOIN")
    end
  end

  describe "#name" do
    let(:user) { build(:user, first_name: "John", last_name: "Doe") }
    let(:sale) { build(:sale, user: user, sold_at: Date.new(2023, 1, 15)) }

    it "returns a formatted string with sold_at date and user name" do
      expect(sale.name).to eq("Sale on 2023-01-15 by John Doe")
    end
  end

  describe "Callbacks" do
    let(:sale) { build(:sale) }

    it "assigns points before validation" do
      expect(sale).to receive(:assign_points)
      sale.valid?
    end

    it "notifies admins after creation" do
      sale = build(:sale)
      expect(sale).to receive(:notify_admins)
      sale.save
    end
  end
end
