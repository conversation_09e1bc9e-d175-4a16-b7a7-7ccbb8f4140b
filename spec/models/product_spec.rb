# == Schema Information
#
# Table name: products
#
#  id               :bigint           not null, primary key
#  active           :boolean          default(TRUE)
#  description      :text
#  gift_card        :boolean          default(FALSE)
#  gift_card_value  :integer          default(0)
#  image_data       :jsonb
#  line_items_count :integer          default(0), not null
#  msrp             :decimal(10, 2)
#  name             :string
#  points_earned    :integer
#  points_needed    :integer
#  sales_count      :integer          default(0), not null
#  sku              :string
#  upc              :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  category_id      :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sales_count  (sales_count)
#
require "rails_helper"

RSpec.describe Product, type: :model do
  describe "Validations" do
    it "is valid with valid attributes" do
      product = build(:product)
      product.valid?
      expect(product).to be_valid
    end

    it "is not valid without a name" do
      product = build(:product, name: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a description" do
      product = build(:product, description: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a sku" do
      product = build(:product, sku: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a category" do
      product = build(:product, category_id: nil)
      product.valid?
      expect(product).to be_invalid
    end
  end

  describe "Associations" do
    it { is_expected.to belong_to(:category) }
    it { is_expected.to have_one(:brand).through(:category) }
    it { is_expected.to have_many(:line_items) }
    it { is_expected.to have_many(:orders).through(:line_items) }
    it { is_expected.to have_many(:sales) }
    it { is_expected.to have_many(:prices).dependent(:destroy) }
    it { is_expected.to have_and_belong_to_many(:promotions) }
  end
end
