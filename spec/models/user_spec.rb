# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved               :boolean          default(FALSE)
#  approved_at            :date
#  approved_sales_count   :integer          default(0), not null
#  avatar_data            :jsonb
#  city                   :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  email_frequency        :integer          default("daily")
#  encrypted_password     :string           default(""), not null
#  failed_email           :boolean          default(FALSE)
#  first_name_ciphertext  :text
#  last_name              :string
#  last_seen              :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  orders_count           :integer          default(0), not null
#  pending_sales_count    :integer          default(0), not null
#  phone_number           :string
#  points_earned          :integer
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular_user")
#  sales_count            :integer          default(0), not null
#  settings               :jsonb
#  sign_in_count          :integer          default(0), not null
#  slug                   :string
#  status                 :integer          default("new")
#  store_zip              :string
#  street_ciphertext      :text
#  time_zone              :string
#  unconfirmed_email      :string
#  username               :string
#  zip_ciphertext         :text
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  admin_brand_id         :bigint
#  admin_region_id        :bigint
#  old_user_id            :integer
#  state_id               :bigint
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_admin_brand_id        (admin_brand_id)
#  index_users_on_admin_region_id       (admin_region_id)
#  index_users_on_approved              (approved)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_email_frequency       (email_frequency)
#  index_users_on_last_seen             (last_seen)
#  index_users_on_orders_count          (orders_count)
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_role                  (role)
#  index_users_on_sales_count           (sales_count)
#  index_users_on_slug                  (slug) UNIQUE
#  index_users_on_state_id              (state_id)
#  index_users_on_status                (status)
#  index_users_on_status_and_role       (status,role)
#  index_users_on_store_id              (store_id)
#
require "rails_helper"

RSpec.describe User, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
    it { is_expected.to validate_presence_of(:phone_number) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:store).optional }
    it { is_expected.to belong_to(:admin_region).class_name("Region").optional }
    it { is_expected.to belong_to(:admin_brand).class_name("Brand").optional }
    it { is_expected.to have_one(:wallet) }
    it { is_expected.to have_many(:orders) }
    it { is_expected.to have_many(:sales) }
    it { is_expected.to have_many(:adjustments) }
  end

  describe "Activity tracking" do
    let(:user) { create(:user) }

    describe "#recently_active?" do
      it "returns true for recently active users" do
        user.update!(last_seen: 1.day.ago)
        expect(user.recently_active?).to be true
      end

      it "returns false for inactive users" do
        user.update!(last_seen: 2.months.ago)
        expect(user.recently_active?).to be false
      end

      it "returns false for users who never logged in" do
        user.update!(last_seen: nil)
        expect(user.recently_active?).to be false
      end
    end

    describe "#activity_status" do
      it "returns 'never_logged_in' for users who never signed in" do
        user.update!(last_sign_in_at: nil)
        expect(user.activity_status).to eq("never_logged_in")
      end

      it "returns 'recently_active' for recently active users" do
        user.update!(last_seen: 1.day.ago, last_sign_in_at: 1.day.ago)
        expect(user.activity_status).to eq("recently_active")
      end

      it "returns 'inactive' for long inactive users" do
        user.update!(last_seen: 4.months.ago, last_sign_in_at: 4.months.ago)
        expect(user.activity_status).to eq("inactive")
      end

      it "returns 'moderately_active' for moderately active users" do
        user.update!(last_seen: 45.days.ago, last_sign_in_at: 45.days.ago)
        expect(user.activity_status).to eq("moderately_active")
      end
    end
  end

  describe "Performance methods" do
    let(:user) { create(:user) }

    describe "#cached_available_balance" do
      it "caches the available balance" do
        expect(Rails.cache).to receive(:fetch).with("user_#{user.id}_available_balance", expires_in: 5.minutes)
        user.cached_available_balance
      end
    end
  end

  describe "Avatar handling" do
    let(:user) { create(:user) }

    describe "#avatar_url" do
      it "returns default avatar path when no avatar attached" do
        expect(user.avatar_url).to include("avatar-missing.svg")
      end
    end
  end
end
