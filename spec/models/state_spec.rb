# == Schema Information
#
# Table name: states
#
#  id           :bigint           not null, primary key
#  abbreviation :string
#  name         :string
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  country_id   :bigint
#  region_id    :bigint           not null
#
# Indexes
#
#  index_states_on_abbreviation  (abbreviation) UNIQUE
#  index_states_on_country_id    (country_id)
#  index_states_on_region_id     (region_id)
#
require "rails_helper"

RSpec.describe State, type: :model do
  describe "Validations" do
    subject { build(:state) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:abbreviation) }
    it { is_expected.to validate_uniqueness_of(:abbreviation) }
  end

  describe "Relationships" do
    it { is_expected.to have_many(:addresses) }
    it { is_expected.to belong_to(:country) }
    it { is_expected.to belong_to(:region) }
  end
end
