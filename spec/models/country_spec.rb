# == Schema Information
#
# Table name: countries
#
#  id           :bigint           not null, primary key
#  abbreviation :string           not null
#  currency     :string
#  name         :string           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
require "rails_helper"

RSpec.describe Country, type: :model do
  describe "Validations" do
    subject { build(:country) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:abbreviation) }
    it { is_expected.to validate_uniqueness_of(:abbreviation) }
  end

  describe "Associations" do
    it { is_expected.to have_many(:states) }
    it { is_expected.to have_many(:addresses) }
    it { is_expected.to have_many(:prices) }
    it { is_expected.to have_many(:products).through(:prices) }
  end

  describe "#to_s" do
    let(:country) { build(:country, name: "United States") }

    it "returns the country name" do
      expect(country.to_s).to eq("United States")
    end
  end

  describe "default scope" do
    let!(:country_a) { create(:country, name: "Australia") }
    let!(:country_b) { create(:country, name: "Brazil") }
    let!(:country_c) { create(:country, name: "Canada") }

    it "orders countries by name" do
      expect(Country.all.to_a).to eq([country_a, country_b, country_c])
    end
  end
end
