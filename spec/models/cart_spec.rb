# == Schema Information
#
# Table name: carts
#
#  id         :bigint           not null, primary key
#  subtotal   :integer          default(0)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
require "rails_helper"

RSpec.describe Cart, type: :model do
  describe "Associations" do
    it { is_expected.to have_many(:line_items).dependent(:destroy) }
    it { is_expected.to have_many(:products).through(:line_items) }
  end

  describe "#add_product" do
    let(:cart) { create(:cart) }
    let(:product) { create(:product) }

    context "when product is not in cart" do
      it "creates a new line item" do
        expect {
          cart.add_product(product)
        }.to change { cart.line_items.count }.by(1)
      end

      it "sets quantity to 1" do
        line_item = cart.add_product(product)
        expect(line_item.quantity).to eq(1)
      end
    end

    context "when product is already in cart" do
      before do
        cart.add_product(product)
      end

      it "does not create a new line item" do
        expect {
          cart.add_product(product)
        }.not_to change { cart.line_items.count }
      end

      it "increases the quantity of the existing line item" do
        line_item = cart.line_items.find_by(product_id: product.id)
        expect {
          cart.add_product(product)
        }.to change { line_item.reload.quantity }.from(1).to(2)
      end
    end
  end

  describe "#total_points" do
    let(:cart) { create(:cart) }
    let(:product1) { create(:product, points_needed: 50) }
    let(:product2) { create(:product, points_needed: 75) }

    before do
      cart.add_product(product1)
      cart.add_product(product1)
      cart.add_product(product2)
    end

    it "calculates the total points of all line items" do
      # 50 * 2 + 75 * 1 = 175
      expect(cart.total_points).to eq(175)
    end

    it "returns 0 for an empty cart" do
      empty_cart = create(:cart)
      expect(empty_cart.total_points).to eq(0)
    end
  end

  describe "#empty!" do
    let(:cart) { create(:cart) }
    let(:product) { create(:product) }

    before do
      cart.add_product(product)
      cart.add_product(product)
    end

    it "removes all line items" do
      expect {
        cart.empty!
      }.to change { cart.line_items.count }.from(1).to(0)
    end
  end
end
