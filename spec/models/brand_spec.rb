# == Schema Information
#
# Table name: brands
#
#  id                   :bigint           not null, primary key
#  approved_sales_count :integer          default(0), not null
#  description          :text
#  name                 :string           not null
#  optics_sales_count   :integer
#  orders_count         :integer          default(0), not null
#  photo_sales_count    :integer
#  sales_count          :integer          default(0), not null
#  users_count          :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_brands_on_name         (name)
#  index_brands_on_sales_count  (sales_count)
#
require "rails_helper"

RSpec.describe Brand, type: :model do
  describe "Validations" do
    subject { build(:brand) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  describe "Associations" do
    it { is_expected.to have_many(:stores) }
    it { is_expected.to have_many(:categories) }
    it { is_expected.to have_many(:users).through(:stores) }
    it { is_expected.to have_many(:messages) }
    it { is_expected.to have_many(:sales) }
    it { is_expected.to have_many(:orders) }
  end
end
