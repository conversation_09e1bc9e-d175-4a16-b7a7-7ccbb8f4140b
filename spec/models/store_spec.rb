# == Schema Information
#
# Table name: stores
#
#  id                 :bigint           not null, primary key
#  account_number     :string
#  city               :string
#  name               :string           not null
#  phone_number       :string
#  status             :integer          default("active"), not null
#  street             :string
#  unit               :string
#  users_count        :integer          default(0), not null
#  verified           :boolean
#  zip                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  account_channel_id :bigint
#  account_type_id    :bigint
#  brand_id           :bigint
#  old_store_id       :integer
#  state_id           :bigint
#  store_chain_id     :bigint
#
# Indexes
#
#  index_stores_on_account_channel_id  (account_channel_id)
#  index_stores_on_account_type_id     (account_type_id)
#  index_stores_on_brand_id            (brand_id)
#  index_stores_on_state_id            (state_id)
#  index_stores_on_store_chain_id      (store_chain_id)
#
require "rails_helper"

RSpec.describe Store, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to belong_to(:store_chain).optional }
    it { is_expected.to have_many(:users) }
    it { is_expected.to have_many(:messages) }
    it { is_expected.to have_many(:promotion_locations).dependent(:destroy) }
    it { is_expected.to have_many(:promotions).through(:promotion_locations) }
    it { is_expected.to have_one(:address) }
    it { is_expected.to have_one(:state).through(:address) }
    it { is_expected.to have_one(:region).through(:state) }
  end
end
