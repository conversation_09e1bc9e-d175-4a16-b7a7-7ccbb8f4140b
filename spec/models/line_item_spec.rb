# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  gift_card  :boolean          default(FALSE), not null
#  points     :integer          default(0)
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
require "rails_helper"

RSpec.describe LineItem, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:quantity) }
    it { is_expected.to validate_numericality_of(:quantity).is_greater_than(0) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:product).optional }
    it { is_expected.to belong_to(:cart).optional }
    it { is_expected.to belong_to(:order).optional }
  end

  describe "Delegations" do
    it { is_expected.to delegate_method(:name).to(:product).with_prefix(true) }
    it { is_expected.to delegate_method(:points_needed).to(:product).with_prefix(false) }
  end

  describe "#total_points" do
    let(:product) { build(:product, points_needed: 50) }
    let(:line_item) { build(:line_item, product: product, quantity: 3) }

    it "calculates the total points based on quantity and product points" do
      expect(line_item.total_points).to eq(150)
    end

    it "returns 0 if product is nil" do
      line_item.product = nil
      expect(line_item.total_points).to eq(0)
    end
  end

  describe "Callbacks" do
    let(:product) { build(:product, points_needed: 50) }
    let(:line_item) { build(:line_item, product: product, quantity: 3, points: nil) }

    it "sets points before validation" do
      expect {
        line_item.valid?
      }.to change { line_item.points }.from(nil).to(150)
    end
  end
end
