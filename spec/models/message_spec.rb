# == Schema Information
#
# Table name: messages
#
#  id               :bigint           not null, primary key
#  messageable_type :string
#  read             :boolean          default(FALSE)
#  read_at          :datetime
#  send_on          :datetime
#  sent_at          :datetime
#  subject          :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  messageable_id   :bigint
#
# Indexes
#
#  index_messages_on_messageable  (messageable_type,messageable_id)
#
require "rails_helper"

RSpec.describe Message, type: :model do
  describe "Validations" do
    it "is valid with valid attributes" do
      message = build(:message)
      message.valid?
      expect(message).to be_valid
    end

    it "is not valid without subject" do
      message = build(:message, subject: nil)
      message.valid?
      expect(message).to be_invalid
    end

    it "is not valid without body" do
      message = build(:message, body: nil)
      message.valid?
      expect(message).to be_invalid
    end
  end

  describe "Relationships" do
    it "belongs to messageable" do
      message = Message.reflect_on_association(:messageable)
      expect(message.macro).to eq(:belongs_to)
    end

    it "has_and_belongs_to_many users" do
      message = Message.reflect_on_association(:users)
      expect(message.macro).to eq(:has_and_belongs_to_many)
    end
  end
end
