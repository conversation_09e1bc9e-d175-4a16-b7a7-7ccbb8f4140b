# == Schema Information
#
# Table name: reports
#
#  id         :bigint           not null, primary key
#  frequency  :integer          default("monthly")
#  key        :string
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
require "rails_helper"

RSpec.describe Report, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:key) }
    it { is_expected.to validate_uniqueness_of(:key) }
  end

  describe "Enums" do
    it { is_expected.to define_enum_for(:frequency).with_values(hourly: 0, daily: 1, weekly: 2, monthly: 3) }
  end

  describe "Scopes" do
    let!(:hourly_report) { create(:report, frequency: :hourly) }
    let!(:daily_report) { create(:report, frequency: :daily) }
    let!(:weekly_report) { create(:report, frequency: :weekly) }
    let!(:monthly_report) { create(:report, frequency: :monthly) }

    it "filters by hourly frequency" do
      expect(Report.hourly).to include(hourly_report)
      expect(Report.hourly).not_to include(daily_report, weekly_report, monthly_report)
    end

    it "filters by daily frequency" do
      expect(Report.daily).to include(daily_report)
      expect(Report.daily).not_to include(hourly_report, weekly_report, monthly_report)
    end

    it "filters by weekly frequency" do
      expect(Report.weekly).to include(weekly_report)
      expect(Report.weekly).not_to include(hourly_report, daily_report, monthly_report)
    end

    it "filters by monthly frequency" do
      expect(Report.monthly).to include(monthly_report)
      expect(Report.monthly).not_to include(hourly_report, daily_report, weekly_report)
    end
  end

  describe "#generate" do
    let(:report) { build(:report, key: "sales_summary") }

    it "calls the appropriate generator class" do
      generator_class = class_double("Reports::SalesSummary")
      stub_const("Reports::SalesSummary", generator_class)

      expect(generator_class).to receive(:generate)
      report.generate
    end

    it "raises an error for unknown report types" do
      report.key = "unknown_report"
      expect { report.generate }.to raise_error(NameError)
    end
  end
end
