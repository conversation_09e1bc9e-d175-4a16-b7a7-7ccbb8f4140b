# == Schema Information
#
# Table name: fulfillments
#
#  id          :bigint           not null, primary key
#  begins_at   :datetime
#  ends_at     :datetime
#  order_count :integer
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
require "rails_helper"

RSpec.describe Fulfillment, type: :model do
  describe "Validations" do
    it "is valid with valid attributes" do
      fulfillment = build(:fulfillment)
      fulfillment.valid?
      expect(fulfillment).to be_valid
    end

    it "is not valid without a begins_at" do
      fulfillment = build(:fulfillment, begins_at: nil)
      fulfillment.valid?
      expect(fulfillment).to be_invalid
    end

    it "is not valid without a ends_at" do
      fulfillment = build(:fulfillment, ends_at: nil)
      fulfillment.valid?
      expect(fulfillment).to be_invalid
    end

    it "is not valid without a order_count" do
      fulfillment = build(:fulfillment, order_count: nil)
      fulfillment.valid?
      expect(fulfillment).to be_invalid
    end
  end
end
