# == Schema Information
#
# Table name: store_chains
#
#  id           :bigint           not null, primary key
#  name         :string
#  stores_count :integer          default(0)
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
require "rails_helper"

RSpec.describe StoreChain, type: :model do
  describe "Validations" do
    it "is valid with valid attributes" do
      store_chain = build(:store_chain)
      store_chain.valid?
      expect(store_chain).to be_valid
    end

    it "is not valid without name" do
      store_chain = build(:store_chain, name: nil)
      store_chain.valid?
      expect(store_chain).to be_invalid
    end
  end

  describe "Relationships" do
    it "has many stores" do
      store_chain = StoreChain.reflect_on_association(:stores)
      expect(store_chain.macro).to eq(:has_many)
    end

    it "has many users" do
      store_chain = StoreChain.reflect_on_association(:users)
      expect(store_chain.macro).to eq(:has_many)
    end
  end
end
