# == Schema Information
#
# Table name: categories
#
#  id              :bigint           not null, primary key
#  description     :text
#  name            :string
#  products_count  :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  brand_id        :bigint           not null
#  old_category_id :integer
#
# Indexes
#
#  index_categories_on_brand_id  (brand_id)
#
require "rails_helper"

RSpec.describe Category, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to have_many(:products) }
  end
end
