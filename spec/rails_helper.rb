# This file is copied to spec/ when you run 'rails generate rspec:install'
require "spec_helper"
ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?

require "rspec/rails"

# Disable MeiliSearch in tests to avoid indexing issues
begin
  require "meilisearch-rails"
  # Completely disable MeiliSearch indexing in tests
  MeiliSearch::Rails.configuration[:meilisearch_url] = nil
  MeiliSearch::Rails.configuration[:auto_index] = false
  MeiliSearch::Rails.configuration[:auto_remove] = false
rescue LoadError
  # MeiliSearch not available, skip
end

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end
RSpec.configure do |config|
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  Dir[Rails.root.join("spec/support/**/*.rb")].each { |f| require f }

  # Database Cleaner configuration
  config.before(:suite) do
    DatabaseCleaner.clean_with(:truncation)
  end

  config.before(:each) do
    DatabaseCleaner.strategy = :transaction
  end

  # Configure DatabaseCleaner strategy for JavaScript-enabled tests
  config.before(:each, js: true) do
    DatabaseCleaner.strategy = :truncation
  end

  # Configure DatabaseCleaner strategy for system tests
  config.before(:each, type: :system) do
    # Use transaction strategy for shared tests (faster)
    # Use truncation strategy for JavaScript tests (more reliable)
    DatabaseCleaner.strategy = if Capybara.current_driver == :rack_test
      :transaction
    else
      [:truncation, {except: %w[countries states]}]
    end
  end

  config.before(:each, type: :feature) do
    # Same strategy as system tests
    DatabaseCleaner.strategy = if Capybara.current_driver == :rack_test
      :transaction
    else
      [:truncation, {except: %w[countries states]}]
    end
  end

  config.before(:each) do
    DatabaseCleaner.start
  end

  config.append_after(:each) do
    DatabaseCleaner.clean
  end

  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include Devise::Test::IntegrationHelpers, type: :system
  config.include Devise::Test::IntegrationHelpers, type: :feature
  config.include ControllerMacros, type: :controller
  config.include ActiveSupport::Testing::TimeHelpers
  config.include ActiveJob::TestHelper, type: :job

  # Factory Bot configuration
  config.include FactoryBot::Syntax::Methods

  # Disable MeiliSearch indexing in tests
  config.before(:each) do
    # Stub meilisearch methods to prevent indexing errors
    allow_any_instance_of(User).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Sale).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Store).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Product).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(User).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Sale).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Store).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Product).to receive(:ms_remove_from_index!).and_return(true)
  end
end
