require "rails_helper"

RSpec.describe WeeklyReportsJob, type: :job do
  describe "#perform" do
    let!(:weekly_report1) { create(:report, frequency: :weekly) }
    let!(:weekly_report2) { create(:report, frequency: :weekly) }
    let!(:daily_report) { create(:report, frequency: :daily) }

    it "generates all weekly reports" do
      expect(weekly_report1).to receive(:generate)
      expect(weekly_report2).to receive(:generate)
      expect(daily_report).not_to receive(:generate)

      allow(Report).to receive(:weekly).and_return([weekly_report1, weekly_report2])

      WeeklyReportsJob.new.perform
    end

    it "sends notification after generating reports" do
      allow_any_instance_of(Report).to receive(:generate)

      expect(ReportMailer).to receive(:weekly_reports_completed).and_return(double(deliver_later: true))

      WeeklyReportsJob.new.perform
    end

    it "handles errors during report generation" do
      allow(weekly_report1).to receive(:generate).and_raise(StandardError.new("Test error"))
      allow(weekly_report2).to receive(:generate)
      allow(Report).to receive(:weekly).and_return([weekly_report1, weekly_report2])

      expect(Rails.logger).to receive(:error).with(/Error generating report/)

      # Should not raise error
      expect { WeeklyReportsJob.new.perform }.not_to raise_error

      # Should still process other reports
      expect(weekly_report2).to have_received(:generate)
    end
  end
end
