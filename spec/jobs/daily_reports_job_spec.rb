require "rails_helper"

RSpec.describe DailyReportsJob, type: :job do
  describe "#perform" do
    let!(:daily_report1) { create(:report, frequency: :daily) }
    let!(:daily_report2) { create(:report, frequency: :daily) }
    let!(:weekly_report) { create(:report, frequency: :weekly) }

    it "generates all daily reports" do
      expect(daily_report1).to receive(:generate)
      expect(daily_report2).to receive(:generate)
      expect(weekly_report).not_to receive(:generate)

      allow(Report).to receive(:daily).and_return([daily_report1, daily_report2])

      DailyReportsJob.new.perform
    end

    it "sends notification after generating reports" do
      allow_any_instance_of(Report).to receive(:generate)

      expect(ReportMailer).to receive(:daily_reports_completed).and_return(double(deliver_later: true))

      DailyReportsJob.new.perform
    end

    it "handles errors during report generation" do
      allow(daily_report1).to receive(:generate).and_raise(StandardError.new("Test error"))
      allow(daily_report2).to receive(:generate)
      allow(Report).to receive(:daily).and_return([daily_report1, daily_report2])

      expect(Rails.logger).to receive(:error).with(/Error generating report/)

      # Should not raise error
      expect { DailyReportsJob.new.perform }.not_to raise_error

      # Should still process other reports
      expect(daily_report2).to have_received(:generate)
    end
  end
end
