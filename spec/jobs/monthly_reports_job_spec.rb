require "rails_helper"

RSpec.describe MonthlyReportsJob, type: :job do
  describe "#perform" do
    let!(:monthly_report1) { create(:report, frequency: :monthly) }
    let!(:monthly_report2) { create(:report, frequency: :monthly) }
    let!(:daily_report) { create(:report, frequency: :daily) }

    it "generates all monthly reports" do
      expect(monthly_report1).to receive(:generate)
      expect(monthly_report2).to receive(:generate)
      expect(daily_report).not_to receive(:generate)

      allow(Report).to receive(:monthly).and_return([monthly_report1, monthly_report2])

      MonthlyReportsJob.new.perform
    end

    it "sends notification after generating reports" do
      allow_any_instance_of(Report).to receive(:generate)

      expect(ReportMailer).to receive(:monthly_reports_completed).and_return(double(deliver_later: true))

      MonthlyReportsJob.new.perform
    end

    it "handles errors during report generation" do
      allow(monthly_report1).to receive(:generate).and_raise(StandardError.new("Test error"))
      allow(monthly_report2).to receive(:generate)
      allow(Report).to receive(:monthly).and_return([monthly_report1, monthly_report2])

      expect(Rails.logger).to receive(:error).with(/Error generating report/)

      # Should not raise error
      expect { MonthlyReportsJob.new.perform }.not_to raise_error

      # Should still process other reports
      expect(monthly_report2).to have_received(:generate)
    end
  end
end
