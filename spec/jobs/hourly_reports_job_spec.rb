require "rails_helper"

RSpec.describe HourlyReportsJob, type: :job do
  describe "#perform" do
    let!(:hourly_report1) { create(:report, frequency: :hourly) }
    let!(:hourly_report2) { create(:report, frequency: :hourly) }
    let!(:daily_report) { create(:report, frequency: :daily) }

    it "generates all hourly reports" do
      expect(hourly_report1).to receive(:generate)
      expect(hourly_report2).to receive(:generate)
      expect(daily_report).not_to receive(:generate)

      allow(Report).to receive(:hourly).and_return([hourly_report1, hourly_report2])

      HourlyReportsJob.new.perform
    end

    it "sends notification after generating reports" do
      allow_any_instance_of(Report).to receive(:generate)

      expect(ReportMailer).to receive(:hourly_reports_completed).and_return(double(deliver_later: true))

      HourlyReportsJob.new.perform
    end

    it "handles errors during report generation" do
      allow(hourly_report1).to receive(:generate).and_raise(StandardError.new("Test error"))
      allow(hourly_report2).to receive(:generate)
      allow(Report).to receive(:hourly).and_return([hourly_report1, hourly_report2])

      expect(Rails.logger).to receive(:error).with(/Error generating report/)

      # Should not raise error
      expect { HourlyReportsJob.new.perform }.not_to raise_error

      # Should still process other reports
      expect(hourly_report2).to have_received(:generate)
    end
  end
end
