# == Schema Information
#
# Table name: prices
#
#  id            :bigint           not null, primary key
#  msrp_in_cents :integer          default(0), not null
#  points_earned :integer          default(0), not null
#  points_needed :integer          default(0), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint
#  product_id    :bigint
#
# Indexes
#
#  index_prices_on_country_id                 (country_id)
#  index_prices_on_country_id_and_product_id  (country_id,product_id) UNIQUE
#  index_prices_on_product_id                 (product_id)
#
FactoryBot.define do
  factory :price do
    msrp_in_cents { Faker::Number.number(digits: 8) }
    points_earned { 50 }
    points_needed { 500 }
    country
    product
  end
end
