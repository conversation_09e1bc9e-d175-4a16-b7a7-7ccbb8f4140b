# == Schema Information
#
# Table name: cart_products
#
#  id            :bigint           not null, primary key
#  gift_card     :boolean          default(FALSE)
#  points        :integer          default(0)
#  points_to_use :integer          default(0)
#  quantity      :integer          default(1)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  cart_id       :bigint           not null
#  product_id    :bigint           not null
#
# Indexes
#
#  index_cart_products_on_cart_id     (cart_id)
#  index_cart_products_on_product_id  (product_id)
#
FactoryBot.define do
  factory :cart_product do
    quantity { Faker::Number.decimal_part(digits: 2) }
    cart
    product
  end
end
