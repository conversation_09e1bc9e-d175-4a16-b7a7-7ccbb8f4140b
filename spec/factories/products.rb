# == Schema Information
#
# Table name: products
#
#  id               :bigint           not null, primary key
#  active           :boolean          default(TRUE)
#  description      :text
#  gift_card        :boolean          default(FALSE)
#  gift_card_value  :integer          default(0)
#  image_data       :jsonb
#  line_items_count :integer          default(0), not null
#  msrp             :decimal(10, 2)
#  name             :string
#  points_earned    :integer
#  points_needed    :integer
#  sales_count      :integer          default(0), not null
#  sku              :string
#  upc              :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  category_id      :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sales_count  (sales_count)
#
FactoryBot.define do
  factory :product do
    name { Faker::Commerce.product_name }
    msrp { Faker::Commerce.price }
    description { Faker::Lorem.paragraph }
    sku { Faker::Number.number(digits: 10) }
    upc { "740035015968" }
    category

    after(:create) do |product|
      create_list(:price, 2, product: product)
    end
  end
end
