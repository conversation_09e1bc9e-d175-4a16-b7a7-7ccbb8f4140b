# == Schema Information
#
# Table name: fulfillments
#
#  id          :bigint           not null, primary key
#  begins_at   :datetime
#  ends_at     :datetime
#  order_count :integer
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
FactoryBot.define do
  factory :fulfillment do
    begins_at { Date.today }
    ends_at { Date.today + 5.days }
    order_count { Faker::Number.non_zero_digit }
  end
end
