# == Schema Information
#
# Table name: regions
#
#  id           :bigint           not null, primary key
#  name         :string           not null
#  orders_count :integer          default(0), not null
#  sales_count  :integer          default(0), not null
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  admin_id     :bigint
#
# Indexes
#
#  index_regions_on_admin_id  (admin_id)
#
FactoryBot.define do
  factory :region do
    name { Faker::Address.state }
  end
end
