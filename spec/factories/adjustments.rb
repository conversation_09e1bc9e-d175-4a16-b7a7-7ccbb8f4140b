# == Schema Information
#
# Table name: adjustments
#
#  id         :bigint           not null, primary key
#  kind       :integer          default("debit")
#  notes      :text             not null
#  points     :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_adjustments_on_admin_id  (admin_id)
#  index_adjustments_on_user_id   (user_id)
#
FactoryBot.define do
  factory :adjustment do
    notes { Faker::Lorem.sentence }
    points { Faker::Number.decimal_part(digits: 2) }
    user
    association :admin, factory: :user
  end
end
