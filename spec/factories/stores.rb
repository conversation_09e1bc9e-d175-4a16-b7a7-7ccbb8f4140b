# == Schema Information
#
# Table name: stores
#
#  id                 :bigint           not null, primary key
#  account_number     :string
#  city               :string
#  name               :string           not null
#  phone_number       :string
#  status             :integer          default("active"), not null
#  street             :string
#  unit               :string
#  users_count        :integer          default(0), not null
#  verified           :boolean
#  zip                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  account_channel_id :bigint
#  account_type_id    :bigint
#  brand_id           :bigint
#  old_store_id       :integer
#  state_id           :bigint
#  store_chain_id     :bigint
#
# Indexes
#
#  index_stores_on_account_channel_id  (account_channel_id)
#  index_stores_on_account_type_id     (account_type_id)
#  index_stores_on_brand_id            (brand_id)
#  index_stores_on_state_id            (state_id)
#  index_stores_on_store_chain_id      (store_chain_id)
#
FactoryBot.define do
  factory :store do
    name { Faker::Company.name }
    phone_number { Faker::PhoneNumber.phone_number }
    brand

    # Transient attributes for controlling associations
    transient do
      region { nil }
      state { nil }
      country { nil }
      with_address { true }
    end

    # Create address after store is saved
    after(:create) do |store, evaluator|
      if evaluator.with_address
        # Determine the state to use
        state_to_use = evaluator.state

        if state_to_use.nil? && evaluator.region.present?
          # If region is provided but no state, create a state in that region
          state_to_use = evaluator.region.states.first || create(:state, region: evaluator.region)
        end

        # Determine the country to use
        country_to_use = evaluator.country || state_to_use&.country || create(:country)

        # If state is still nil, create one with the country
        state_to_use ||= create(:state, country: country_to_use)

        # Create the address with the determined state and country
        store.create_address(
          line1: Faker::Address.street_address,
          city: Faker::Address.city,
          zip_code: Faker::Address.zip_code,
          state: state_to_use,
          country: country_to_use
        )
      end
    end
  end
end
