# == Schema Information
#
# Table name: states
#
#  id           :bigint           not null, primary key
#  abbreviation :string
#  name         :string
#  users_count  :integer          default(0), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  country_id   :bigint
#  region_id    :bigint           not null
#
# Indexes
#
#  index_states_on_abbreviation  (abbreviation) UNIQUE
#  index_states_on_country_id    (country_id)
#  index_states_on_region_id     (region_id)
#
FactoryBot.define do
  factory :state do
    name { Faker::Address.state }

    # Use a sequence instead of Faker::Address.unique.state_abbr to avoid RetryLimitExceeded
    sequence(:abbreviation) do |n|
      # Use real state abbreviations for the first 50 (covers all US states)
      real_abbrs = %w[AL AK AZ AR CA CO CT DE FL GA HI ID IL IN IA KS KY LA ME MD MA MI MN MS MO MT
        NE NV NH NJ NM NY NC ND OH OK OR PA RI SC SD TN TX UT VT VA WA WV WI WY
        DC PR VI]

      if n <= real_abbrs.size
        real_abbrs[n - 1]
      else
        # For additional states, generate two-letter codes that won't conflict
        # with real state abbreviations
        ("AA".."ZZ").to_a[n - real_abbrs.size - 1]
      end
    end

    region
    country
  end
end
