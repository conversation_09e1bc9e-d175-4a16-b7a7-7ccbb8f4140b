# == Schema Information
#
# Table name: messages
#
#  id               :bigint           not null, primary key
#  messageable_type :string
#  read             :boolean          default(FALSE)
#  read_at          :datetime
#  send_on          :datetime
#  sent_at          :datetime
#  subject          :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  messageable_id   :bigint
#
# Indexes
#
#  index_messages_on_messageable  (messageable_type,messageable_id)
#
FactoryBot.define do
  factory :message do
    subject { Faker::Lorem.sentence }
    body { Faker::Lorem.paragraph }
  end
end
