# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  gift_card  :boolean          default(FALSE), not null
#  points     :integer          default(0)
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
FactoryBot.define do
  factory :line_item do
  end
end
