# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  approved_at      :date
#  declined_at      :datetime
#  line_items_count :integer          default(0), not null
#  notes            :text
#  points           :integer          default(0), not null
#  ship_to          :integer          default("home"), not null
#  status           :integer          default("pending")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  admin_id         :bigint
#  ahoy_visit_id    :bigint
#  brand_id         :bigint           not null
#  region_id        :bigint           not null
#  sap_id           :string
#  user_id          :bigint           not null
#
# Indexes
#
#  index_orders_on_admin_id                             (admin_id)
#  index_orders_on_approved_at                          (approved_at) WHERE (approved_at IS NOT NULL)
#  index_orders_on_brand_id                             (brand_id)
#  index_orders_on_brand_id_and_status_and_created_at   (brand_id,status,created_at)
#  index_orders_on_declined_at                          (declined_at) WHERE (declined_at IS NOT NULL)
#  index_orders_on_region_id                            (region_id)
#  index_orders_on_region_id_and_status_and_created_at  (region_id,status,created_at)
#  index_orders_on_status_and_created_at                (status,created_at)
#  index_orders_on_user_id                              (user_id)
#  index_orders_on_user_id_and_status_and_created_at    (user_id,status,created_at)
#
FactoryBot.define do
  factory :order do
    approved_at {} # Time.now
    declined_at {} # Time.now
    notes {} # Faker::Lorem.sentence
    ship_to { ["work", "home"].sample }
    sap_id { "1" }
    user
    brand
    region
    points { Faker::Number.decimal_part(digits: 2) }
  end
end
