# == Schema Information
#
# Table name: notes
#
#  id            :bigint           not null, primary key
#  noteable_type :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  noteable_id   :bigint
#  user_id       :bigint
#
# Indexes
#
#  index_notes_on_noteable  (noteable_type,noteable_id)
#  index_notes_on_user_id   (user_id)
#
FactoryBot.define do
  factory :note do
    noteable_type { 'Order' }
    user_id { create(:user).id }
    noteable_id { create(:order).id }
  end

  # factory :sale_note do
  #   noteable_type { ['Order', 'Sale'].sample }
  #   user_id { create(:user).id }
  #   noteable_id {  }
  # end
end
