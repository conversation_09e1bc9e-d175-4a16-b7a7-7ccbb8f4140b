# == Schema Information
#
# Table name: categories
#
#  id              :bigint           not null, primary key
#  description     :text
#  name            :string
#  products_count  :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  brand_id        :bigint           not null
#  old_category_id :integer
#
# Indexes
#
#  index_categories_on_brand_id  (brand_id)
#
FactoryBot.define do
  factory :category do
    name { Faker::Lorem.word }
    description { Faker::Lorem.sentence }
    brand
  end
end
