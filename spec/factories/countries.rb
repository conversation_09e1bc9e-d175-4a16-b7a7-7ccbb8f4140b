# == Schema Information
#
# Table name: countries
#
#  id           :bigint           not null, primary key
#  abbreviation :string           not null
#  currency     :string
#  name         :string           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
FactoryBot.define do
  factory :country do
    name { Faker::Address.country }

    # Use a sequence instead of Faker::Address.unique.country_code to avoid RetryLimitExceeded
    sequence(:abbreviation) do |n|
      # Use real country codes for the first set of countries
      real_country_codes = %w[
        US CA MX GB FR DE IT ES JP CN IN BR AU RU ZA KR AR CL CO PE VE NZ SG
        MY TH ID PH VN HK TW PL SE NO FI DK IS NL BE CH AT PT IE GR TR IL SA
        AE QA KW BH OM JO LB EG MA ZW NG KE TZ UG ET ZM MZ MG
      ]

      if n <= real_country_codes.size
        real_country_codes[n - 1]
      else
        # For additional countries, generate two-letter codes that won't conflict
        # with real country codes (using less common combinations)
        custom_codes = %w[
          AA BB CC DD EE FF GG HH II JJ KK LL MM NN OO PP QQ RR SS TT UU VV WW XX YY ZZ
          AB AC AD AE AF AG AH AI AJ AK AL AM AN AO AP AQ AR AS AT AU AV AW AX AY AZ
        ]
        custom_codes[n - real_country_codes.size - 1] || "X#{n}"
      end
    end

    # Add currency based on country code
    currency do |country|
      case country.abbreviation
      when "US" then "USD"
      when "CA" then "CAD"
      when "GB" then "GBP"
      when "EU" then "EUR"
      when "JP" then "JPY"
      when "CN" then "CNY"
      when "AU" then "AUD"
      else "USD" # Default to USD for other countries
      end
    end
  end
end
