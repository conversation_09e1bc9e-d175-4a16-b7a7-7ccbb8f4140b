# == Schema Information
#
# Table name: store_requests
#
#  id           :bigint           not null, primary key
#  city         :string
#  email        :string
#  manager_name :string
#  name         :string
#  status       :integer          default("pending")
#  store_name   :string
#  street       :string
#  zip          :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  brand_id     :bigint
#  state_id     :bigint           not null
#
# Indexes
#
#  index_store_requests_on_brand_id  (brand_id)
#  index_store_requests_on_state_id  (state_id)
#
FactoryBot.define do
  # factory :brand, aliases: [:brand]

  factory :store_request do
    name { Faker::Name.name }
    store_name { Faker::Name.name }
    street { Faker::Address.street_address }
    city { Faker::Address.city }
    zip { Faker::Address.zip_code }
    manager_name { Faker::Name.name }
    email { Faker::Internet.email }
    state
    brand
    # association :state, factory: :state
    # association :brand, factory: :brand
  end
end
