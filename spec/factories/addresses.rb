# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string
#  city             :string           not null
#  latitude         :float
#  line1            :string           not null
#  line2            :string
#  longitude        :float
#  zip_code         :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable             (addressable_type,addressable_id)
#  index_addresses_on_country_id              (country_id)
#  index_addresses_on_latitude_and_longitude  (latitude,longitude)
#  index_addresses_on_state_id                (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#
FactoryBot.define do
  factory :address do
    line1 { Faker::Address.street_address }
    line2 { Faker::Address.secondary_address }
    city { Faker::Address.city }
    zip_code { Faker::Address.zip_code }

    # Create associations before validation
    before(:create) do |address|
      # Ensure state exists
      address.state ||= create(:state)
      # Ensure country exists and matches state's country
      address.country ||= address.state.country
    end
  end
end
