# == Schema Information
#
# Table name: brands
#
#  id                   :bigint           not null, primary key
#  approved_sales_count :integer          default(0), not null
#  description          :text
#  name                 :string           not null
#  optics_sales_count   :integer
#  orders_count         :integer          default(0), not null
#  photo_sales_count    :integer
#  sales_count          :integer          default(0), not null
#  users_count          :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_brands_on_name         (name)
#  index_brands_on_sales_count  (sales_count)
#
FactoryBot.define do
  factory :brand do
    name { Faker::Company.unique.name }
  end
end
