# == Schema Information
#
# Table name: wallets
#
#  id              :bigint           not null, primary key
#  current_balance :integer          default(0)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_wallets_on_user_id  (user_id)
#
FactoryBot.define do
  factory :wallet do
    current_balance { 5000 }
    user
  end
end
