{"name": "app_name", "version": "1.0.0", "dependencies": {"@alpinejs/focus": "^3.14.9", "@alpinejs/mask": "^3.14.9", "@alpinejs/persist": "^3.14.9", "@alpinejs/ui": "^3.14.9", "@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^8.0.13", "@rails/actioncable": "^8.0.200", "@rails/actiontext": "^8.0.200", "@rails/activestorage": "^8.0.200", "@rails/request.js": "^0.0.12", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "ahoy.js": "^0.4.4", "alpinejs": "^3.14.9", "chart.js": "^4.4.7", "chartkick": "^5.0.1", "croppie": "^2.6.5", "dropzone": "^6.0.0-beta.2", "flatpickr": "^4.6.13", "local-time": "^3.0.3", "quagga": "^0.12.1", "slim-select": "^2.11.0", "tailwindcss": "^4.1.8", "tippy.js": "^6.3.7", "trix": "^2.1.15", "underscore": "^1.13.7"}, "scripts": {"build": "esbuild app/javascript/*.* --bundle --sourcemap --format=esm --outdir=app/assets/builds --public-path=/assets", "build:dev": "esbuild app/javascript/application.js --bundle --sourcemap --format=esm --outdir=app/assets/builds --public-path=/assets --watch", "build:css": "tailwindcss -i ./app/assets/stylesheets/application.tailwind.css -o ./app/assets/builds/application.css --watch"}, "devDependencies": {"@types/ahoy.js": "^0.4.2", "@types/alpinejs": "^3.13.11", "@types/alpinejs__focus": "^3.13.4", "@types/alpinejs__mask": "^3.13.4", "@types/alpinejs__persist": "^3.13.4", "@types/croppie": "^2.6.4", "@types/dropzone": "^5.7.9", "@types/rails__actioncable": "^6.1.11", "@types/rails__activestorage": "^8.0.0", "@types/rails__request.js": "^0.0.0", "@types/underscore": "^1.13.0", "esbuild": "^0.25.5", "esbuild-rails": "^1.0.7"}}