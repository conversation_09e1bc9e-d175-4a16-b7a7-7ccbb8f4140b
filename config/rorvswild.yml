production:
  api_key: TDK24ZYvxfPGTRaBxmbE
  # ignore_requests: # Do not monitor the following actions
  #   - SecretController#index
  # ignore_jobs: # Do not monitor the following jobs
  #   - SecretJob
  # ignore_exceptions: # Do not record the following exceptions
  #   - ActionController::RoutingError  # By default to ignore 404
  # ignore_plugins:
  #   - ActionController
  #   - ActionMailer
  #   - ActionView
  #   - ActiveJob
  #   - ActiveRecord
  #   - DelayedJob
  #   - Elasticsearch
  #   - Mongo
  #   - NetHttp
  #   - Redis
  #   - Resque
  #   - Sidekiq
  # logger: log/rorvswild.log # By default it uses Rails.logger or Logger.new(STDOUT)
  # # Deployment tracking is working without any actions from your part if the Rails app
  # # is inside a Git repositoriy, is deployed via Capistrano.
  # # In the other cases, you can provide the following details.
  # deployment:
  #   revision: <%= "Anything that will return the deployment version" %> # Mandatory
  #   description: <%= "Eventually if you have a description such as a Git message" %>
  #   author: <%= "Author's name of the deployment" %>
    #   email: <%= "<EMAIL>" %>
