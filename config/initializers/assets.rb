# Be sure to restart your server when you modify this file.

# Version of your assets, change this if you want to expire all your assets.
Rails.application.config.assets.version = "1.0"

# Add additional assets to the asset load path.
# Rails.application.config.assets.paths << Emoji.images_path

# Rails 8 Asset Pipeline Optimizations
Rails.application.config.assets.configure do |env|
  # Enable gzip compression
  env.gzip = Rails.env.production?

  # Optimize asset compilation
  env.cache = Rails.env.production? ?
    Sprockets::Cache::FileStore.new("#{Rails.root}/tmp/cache/assets") :
    Sprockets::Cache::MemoryStore.new
end

# Precompile additional assets for Rails 8
Rails.application.config.assets.precompile += %w[
  admin.css
  admin.js
  application.css
  application.js
]

# Enable Rails 8 asset optimizations
if Rails.env.production?
  Rails.application.config.assets.compile = false
  Rails.application.config.assets.digest = true
  Rails.application.config.assets.compress = true
end
