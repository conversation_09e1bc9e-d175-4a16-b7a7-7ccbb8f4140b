if Rails.env.test?
  Geocoder.configure(lookup: :test, ip_lookup: :test)

  Geocoder::Lookup::Test.set_default_stub(
    [
      {
        "coordinates" => [40.7143528, -74.0059731],
        "address" => "New York, NY, USA",
        "state" => "New York",
        "state_code" => "NY",
        "country" => "United States",
        "country_code" => "US"
      }
    ]
  )
else
  Geocoder.configure(
    # Geocoding options
    # timeout: 3,                 # geocoding service timeout (secs)
    lookup: :mapbox,         # name of geocoding service (symbol)
    ip_lookup: :ipinfo_io,      # name of IP address geocoding service (symbol)
    # language: :en,              # ISO-639 language code
    # use_https: false,           # use HTTPS for lookup requests? (if supported)
    # http_proxy: nil,            # HTTP proxy server (user:pass@host:port)
    # https_proxy: nil,           # HTTPS proxy server (user:pass@host:port)
    cache: Geocoder::CacheStore::Generic.new(Rails.cache, {}),                 # cache object (must respond to #[], #[]=, and #del)

    # Exceptions that should not be rescued by default
    # (if you want to implement custom error handling);
    # supports SocketError and Timeout::Error
    # always_raise: [],

    # Calculation options
    # units: :mi,                 # :km for kilometers or :mi for miles
    # distances: :linear          # :spherical or :linear

    # Cache configuration
    # cache_options: {
    #   expiration: 2.days,
    #   prefix: 'geocoder:'
    # }

    mapbox: {
      api_key: Rails.application.credentials.mapbox.api_key               # API key for geocoding service
    },
    ipinfo_io: {
      api_key: Rails.application.credentials.ipinfo.api_key
    }
  )
end
