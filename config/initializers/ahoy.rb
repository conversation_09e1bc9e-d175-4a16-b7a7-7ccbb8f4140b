class Ahoy::Store < Ahoy::DatabaseStore
  def authenticate(data)
  end
end

# set to true for JavaScript tracking
Ahoy.api = false

# set to true for geocoding (and add the geocoder gem to your Gemfile)
# we recommend configuring local geocoding as well
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = false

Ahoy.user_method = :true_user

Ahoy.server_side_visits = :when_needed
Ahoy.mask_ips = true
Ahoy.cookies = :none
