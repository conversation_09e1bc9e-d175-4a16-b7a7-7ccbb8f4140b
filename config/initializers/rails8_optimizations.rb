# Rails 8 Specific Optimizations and Configurations

Rails.application.configure do
  # Enable Rails 8 performance improvements

  # Optimize Active Record for Rails 8
  config.active_record.yaml_column_permitted_classes = [
    Symbol, Date, Time, DateTime, ActiveSupport::TimeWithZone, ActiveSupport::TimeZone,
    BigDecimal, IPAddr, Regexp
  ]

  # Enable automatic shard swapping (if using multiple databases)
  # config.active_record.shard_selector = { lock: true }

  # Optimize Action Cable for Rails 8
  config.action_cable.precompile_assets = true if Rails.env.production?

  # Enable Rails 8 view caching improvements
  config.action_view.cache_template_loading = Rails.env.production?

  # Optimize Active Job for Rails 8
  config.active_job.verbose_enqueue_logs = Rails.env.development?

  # Enable Rails 8 logging improvements
  config.log_file_size = 100.megabytes if Rails.env.production?
  config.log_file_rotation = 10 if Rails.env.production?

  # Rails 8 security enhancements
  config.force_ssl = Rails.env.production?
  if Rails.env.production?
    config.ssl_options = {
      redirect: {
        exclude: ->(request) {
          request.path == "/up" || request.path.start_with?("/rails/active_storage")
        }
      }
    }
  end

  # Enable Rails 8 middleware optimizations
  config.middleware.use Rack::Deflater if Rails.env.production?

  # Optimize session store for Rails 8
  if Rails.env.production?
    config.session_store :cookie_store,
      key: "_zeisspoints_session",
      secure: true,
      httponly: true,
      same_site: :lax,
      expire_after: 2.weeks
  end
end

# Rails 8 Action Controller optimizations
ActionController::Base.class_eval do
  # Enable Rails 8 request/response optimizations
  if Rails.env.production?
    # Enable ETag generation for better caching
    etag { current_user&.id }

    # Enable conditional GET requests
    before_action :set_cache_headers, if: -> { request.get? && !current_user&.admin? }

    private

    def set_cache_headers
      expires_in 5.minutes, public: false, must_revalidate: true
    end
  end
end
