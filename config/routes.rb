# == Route Map
#
#                                   Prefix Verb   URI Pattern                                                                                       Controller#Action
#                         new_user_session GET    /users/sign_in(.:format)                                                                          users/sessions#new
#                             user_session POST   /users/sign_in(.:format)                                                                          users/sessions#create
#                     destroy_user_session DELETE /users/sign_out(.:format)                                                                         users/sessions#destroy
#                        new_user_password GET    /users/password/new(.:format)                                                                     users/reset#new
#                       edit_user_password GET    /users/password/edit(.:format)                                                                    users/reset#edit
#                            user_password PATCH  /users/password(.:format)                                                                         users/reset#update
#                                          PUT    /users/password(.:format)                                                                         users/reset#update
#                                          POST   /users/password(.:format)                                                                         users/reset#create
#                 cancel_user_registration GET    /users/cancel(.:format)                                                                           users/registrations#cancel
#                    new_user_registration GET    /users/sign_up(.:format)                                                                          users/registrations#new
#                   edit_user_registration GET    /users/edit(.:format)                                                                             users/registrations#edit
#                        user_registration PATCH  /users(.:format)                                                                                  users/registrations#update
#                                          PUT    /users(.:format)                                                                                  users/registrations#update
#                                          DELETE /users(.:format)                                                                                  users/registrations#destroy
#                                          POST   /users(.:format)                                                                                  users/registrations#create
#                    new_user_confirmation GET    /users/confirmation/new(.:format)                                                                 devise/confirmations#new
#                        user_confirmation GET    /users/confirmation(.:format)                                                                     devise/confirmations#show
#                                          POST   /users/confirmation(.:format)                                                                     devise/confirmations#create
#                    edit_account_password GET    /account/password/edit(.:format)                                                                  account/passwords#edit
#                         account_password PATCH  /account/password(.:format)                                                                       account/passwords#update
#                                          PUT    /account/password(.:format)                                                                       account/passwords#update
#               edit_account_notifications GET    /account/notifications/edit(.:format)                                                             account/notifications#edit
#                    account_notifications PATCH  /account/notifications(.:format)                                                                  account/notifications#update
#                                          PUT    /account/notifications(.:format)                                                                  account/notifications#update
#                                     cart GET    /carts/:id(.:format)                                                                              carts#show
#                                          DELETE /carts/:id(.:format)                                                                              carts#destroy
#                            add_line_item POST   /line_items/:id/add(.:format)                                                                     line_items#add
#                         remove_line_item POST   /line_items/:id/remove(.:format)                                                                  line_items#remove
#                               line_items POST   /line_items(.:format)                                                                             line_items#create
#                                line_item GET    /line_items/:id(.:format)                                                                         line_items#show
#                                          DELETE /line_items/:id(.:format)                                                                         line_items#destroy
#                 stop_impersonating_users POST   /users/stop_impersonating(.:format)                                                               users#stop_impersonating
#                         user_adjustments POST   /users/:user_id/adjustments(.:format)                                                             users/adjustments#create
#                      new_user_adjustment GET    /users/:user_id/adjustments/new(.:format)                                                         users/adjustments#new
#                              user_orders POST   /users/:user_id/orders(.:format)                                                                  users/orders#create
#                           new_user_order GET    /users/:user_id/orders/new(.:format)                                                              users/orders#new
#                             approve_user POST   /users/:id/approve(.:format)                                                                      users#approve
#                             decline_user POST   /users/:id/decline(.:format)                                                                      users#decline
#                         impersonate_user POST   /users/:id/impersonate(.:format)                                                                  users#impersonate
#                              ledger_user GET    /users/:id/ledger(.:format)                                                                       users#ledger
#                               audit_user GET    /users/:id/audit(.:format)                                                                        users#audit
#                              count_users GET    /users/count(.:format)                                                                            users#count
#                          user_activities GET    /users/:user_id/activities(.:format)                                                              activities#index
#                             import_users POST   /users/import(.:format)                                                                           users#import
#                              stats_users GET    /users/stats(.:format)                                                                            users#stats
#                                    users GET    /users(.:format)                                                                                  users#index
#                                edit_user GET    /users/:id/edit(.:format)                                                                         users#edit
#                                     user GET    /users/:id(.:format)                                                                              users#show
#                                          PATCH  /users/:id(.:format)                                                                              users#update
#                                          PUT    /users/:id(.:format)                                                                              users#update
#                                          DELETE /users/:id(.:format)                                                                              users#destroy
#                        read_notification GET    /notifications/:id/read(.:format)                                                                 notifications#read
#                                     shop GET    /shop(.:format)                                                                                   shops#show
#                            shops_product GET    /shops/products/:id(.:format)                                                                     shops/products#show
#                             search_index GET    /search(.:format)                                                                                 search#index
#                             store_chains GET    /store_chains(.:format)                                                                           store_chains#index
#                                          POST   /store_chains(.:format)                                                                           store_chains#create
#                          new_store_chain GET    /store_chains/new(.:format)                                                                       store_chains#new
#                         edit_store_chain GET    /store_chains/:id/edit(.:format)                                                                  store_chains#edit
#                              store_chain GET    /store_chains/:id(.:format)                                                                       store_chains#show
#                                          PATCH  /store_chains/:id(.:format)                                                                       store_chains#update
#                                          PUT    /store_chains/:id(.:format)                                                                       store_chains#update
#                                          DELETE /store_chains/:id(.:format)                                                                       store_chains#destroy
#                             count_stores GET    /stores/count(.:format)                                                                           stores#count
#                            import_stores POST   /stores/import(.:format)                                                                          stores#import
#                             stats_stores GET    /stores/stats(.:format)                                                                           stores#stats
#                                   stores GET    /stores(.:format)                                                                                 stores#index
#                                          POST   /stores(.:format)                                                                                 stores#create
#                                new_store GET    /stores/new(.:format)                                                                             stores#new
#                               edit_store GET    /stores/:id/edit(.:format)                                                                        stores#edit
#                                    store GET    /stores/:id(.:format)                                                                             stores#show
#                                          PATCH  /stores/:id(.:format)                                                                             stores#update
#                                          PUT    /stores/:id(.:format)                                                                             stores#update
#                                          DELETE /stores/:id(.:format)                                                                             stores#destroy
#                                  regions GET    /regions(.:format)                                                                                regions#index
#                                          POST   /regions(.:format)                                                                                regions#create
#                               new_region GET    /regions/new(.:format)                                                                            regions#new
#                              edit_region GET    /regions/:id/edit(.:format)                                                                       regions#edit
#                                   region GET    /regions/:id(.:format)                                                                            regions#show
#                                          PATCH  /regions/:id(.:format)                                                                            regions#update
#                                          PUT    /regions/:id(.:format)                                                                            regions#update
#                                          DELETE /regions/:id(.:format)                                                                            regions#destroy
#                        import_categories POST   /categories/import(.:format)                                                                      categories#import
#                               categories GET    /categories(.:format)                                                                             categories#index
#                                          POST   /categories(.:format)                                                                             categories#create
#                             new_category GET    /categories/new(.:format)                                                                         categories#new
#                            edit_category GET    /categories/:id/edit(.:format)                                                                    categories#edit
#                                 category GET    /categories/:id(.:format)                                                                         categories#show
#                                          PATCH  /categories/:id(.:format)                                                                         categories#update
#                                          PUT    /categories/:id(.:format)                                                                         categories#update
#                                          DELETE /categories/:id(.:format)                                                                         categories#destroy
#                      pending_count_sales GET    /sales/pending_count(.:format)                                                                    sales#pending_count
#                       region_count_sales GET    /sales/region_count(.:format)                                                                     sales#region_count
#                             import_sales POST   /sales/import(.:format)                                                                           sales#import
#                              stats_sales GET    /sales/stats(.:format)                                                                            sales#stats
#                                    sales GET    /sales(.:format)                                                                                  sales#index
#                                          POST   /sales(.:format)                                                                                  sales#create
#                                 new_sale GET    /sales/new(.:format)                                                                              sales#new
#                                edit_sale GET    /sales/:id/edit(.:format)                                                                         sales#edit
#                                     sale GET    /sales/:id(.:format)                                                                              sales#show
#                                          PATCH  /sales/:id(.:format)                                                                              sales#update
#                                          PUT    /sales/:id(.:format)                                                                              sales#update
#                                          DELETE /sales/:id(.:format)                                                                              sales#destroy
#                     pending_count_orders GET    /orders/pending_count(.:format)                                                                   orders#pending_count
#                      region_count_orders GET    /orders/region_count(.:format)                                                                    orders#region_count
#                             stats_orders GET    /orders/stats(.:format)                                                                           orders#stats
#                                   orders GET    /orders(.:format)                                                                                 orders#index
#                                          POST   /orders(.:format)                                                                                 orders#create
#                               edit_order GET    /orders/:id/edit(.:format)                                                                        orders#edit
#                                    order GET    /orders/:id(.:format)                                                                             orders#show
#                                          PATCH  /orders/:id(.:format)                                                                             orders#update
#                                          PUT    /orders/:id(.:format)                                                                             orders#update
#                                          DELETE /orders/:id(.:format)                                                                             orders#destroy
#                             sale_product GET    /products/:id/sale(.:format)                                                                      products#sale
#                          search_products POST   /products/search(.:format)                                                                        products#search
#                           product_prices POST   /products/:product_id/prices(.:format)                                                            prices#create
#                        new_product_price GET    /products/:product_id/prices/new(.:format)                                                        prices#new
#                       edit_product_price GET    /products/:product_id/prices/:id/edit(.:format)                                                   prices#edit
#                            product_price PATCH  /products/:product_id/prices/:id(.:format)                                                        prices#update
#                                          PUT    /products/:product_id/prices/:id(.:format)                                                        prices#update
#                                          DELETE /products/:product_id/prices/:id(.:format)                                                        prices#destroy
#                          import_products POST   /products/import(.:format)                                                                        products#import
#                                 products GET    /products(.:format)                                                                               products#index
#                                          POST   /products(.:format)                                                                               products#create
#                              new_product GET    /products/new(.:format)                                                                           products#new
#                             edit_product GET    /products/:id/edit(.:format)                                                                      products#edit
#                                  product GET    /products/:id(.:format)                                                                           products#show
#                                          PATCH  /products/:id(.:format)                                                                           products#update
#                                          PUT    /products/:id(.:format)                                                                           products#update
#                                          DELETE /products/:id(.:format)                                                                           products#destroy
#             pending_count_store_requests GET    /store_requests/pending_count(.:format)                                                           store_requests#pending_count
#              region_count_store_requests GET    /store_requests/region_count(.:format)                                                            store_requests#region_count
#                           store_requests GET    /store_requests(.:format)                                                                         store_requests#index
#                                          POST   /store_requests(.:format)                                                                         store_requests#create
#                        new_store_request GET    /store_requests/new(.:format)                                                                     store_requests#new
#                       edit_store_request GET    /store_requests/:id/edit(.:format)                                                                store_requests#edit
#                            store_request GET    /store_requests/:id(.:format)                                                                     store_requests#show
#                                          PATCH  /store_requests/:id(.:format)                                                                     store_requests#update
#                                          PUT    /store_requests/:id(.:format)                                                                     store_requests#update
#                                          DELETE /store_requests/:id(.:format)                                                                     store_requests#destroy
#                                 messages GET    /messages(.:format)                                                                               messages#index
#                                          POST   /messages(.:format)                                                                               messages#create
#                              new_message GET    /messages/new(.:format)                                                                           messages#new
#                             edit_message GET    /messages/:id/edit(.:format)                                                                      messages#edit
#                                  message GET    /messages/:id(.:format)                                                                           messages#show
#                                          PATCH  /messages/:id(.:format)                                                                           messages#update
#                                          PUT    /messages/:id(.:format)                                                                           messages#update
#                                          DELETE /messages/:id(.:format)                                                                           messages#destroy
#           validate_products_update_index POST   /products/update/validate(.:format)                                                               products/update#validate
#                    products_update_index POST   /products/update(.:format)                                                                        products/update#create
#                      new_products_update GET    /products/update/new(.:format)                                                                    products/update#new
#                               gift_cards GET    /gift_cards(.:format)                                                                             gift_cards#index
#                               promotions GET    /promotions(.:format)                                                                             promotions#index
#                                          POST   /promotions(.:format)                                                                             promotions#create
#                            new_promotion GET    /promotions/new(.:format)                                                                         promotions#new
#                           edit_promotion GET    /promotions/:id/edit(.:format)                                                                    promotions#edit
#                                promotion GET    /promotions/:id(.:format)                                                                         promotions#show
#                                          PATCH  /promotions/:id(.:format)                                                                         promotions#update
#                                          PUT    /promotions/:id(.:format)                                                                         promotions#update
#                                          DELETE /promotions/:id(.:format)                                                                         promotions#destroy
#                            login_reports POST   /reports/login(.:format)                                                                          reports#login
#                            sales_reports POST   /reports/sales(.:format)                                                                          reports#sales
#                           orders_reports POST   /reports/orders(.:format)                                                                         reports#orders
#                                  reports GET    /reports(.:format)                                                                                reports#index
#                                          POST   /reports(.:format)                                                                                reports#create
#                               new_report GET    /reports/new(.:format)                                                                            reports#new
#                              edit_report GET    /reports/:id/edit(.:format)                                                                       reports#edit
#                                   report GET    /reports/:id(.:format)                                                                            reports#show
#                                          PATCH  /reports/:id(.:format)                                                                            reports#update
#                                          PUT    /reports/:id(.:format)                                                                            reports#update
#                                          DELETE /reports/:id(.:format)                                                                            reports#destroy
#                  webhooks_sendgrid_index POST   /webhooks/sendgrid(.:format)                                                                      webhooks/sendgrid#create
#                                    about GET    /about(.:format)                                                                                  static#about
#                                    terms GET    /terms(.:format)                                                                                  static#terms
#                                  privacy GET    /privacy(.:format)                                                                                static#privacy
#                                                 /404(.:format)                                                                                    static#not_found
#                                                 /500(.:format)                                                                                    static#internal_server_error
#                              sidekiq_web        /sidekiq                                                                                          Sidekiq::Web
#                                   blazer        /blazer                                                                                           Blazer::Engine
#                        maintenance_tasks        /maintenance_tasks                                                                                MaintenanceTasks::Engine
#                                  pg_hero        /pghero                                                                                           PgHero::Engine
#                                                 /flipper                                                                                          Flipper::UI
#                             admin_brands GET    /admin/brands(.:format)                                                                           admin/brands#index
#                                          POST   /admin/brands(.:format)                                                                           admin/brands#create
#                          new_admin_brand GET    /admin/brands/new(.:format)                                                                       admin/brands#new
#                         edit_admin_brand GET    /admin/brands/:id/edit(.:format)                                                                  admin/brands#edit
#                              admin_brand GET    /admin/brands/:id(.:format)                                                                       admin/brands#show
#                                          PATCH  /admin/brands/:id(.:format)                                                                       admin/brands#update
#                                          PUT    /admin/brands/:id(.:format)                                                                       admin/brands#update
#                                          DELETE /admin/brands/:id(.:format)                                                                       admin/brands#destroy
#                         admin_categories GET    /admin/categories(.:format)                                                                       admin/categories#index
#                                          POST   /admin/categories(.:format)                                                                       admin/categories#create
#                       new_admin_category GET    /admin/categories/new(.:format)                                                                   admin/categories#new
#                      edit_admin_category GET    /admin/categories/:id/edit(.:format)                                                              admin/categories#edit
#                           admin_category GET    /admin/categories/:id(.:format)                                                                   admin/categories#show
#                                          PATCH  /admin/categories/:id(.:format)                                                                   admin/categories#update
#                                          PUT    /admin/categories/:id(.:format)                                                                   admin/categories#update
#                                          DELETE /admin/categories/:id(.:format)                                                                   admin/categories#destroy
#                          admin_countries GET    /admin/countries(.:format)                                                                        admin/countries#index
#                                          POST   /admin/countries(.:format)                                                                        admin/countries#create
#                        new_admin_country GET    /admin/countries/new(.:format)                                                                    admin/countries#new
#                       edit_admin_country GET    /admin/countries/:id/edit(.:format)                                                               admin/countries#edit
#                            admin_country GET    /admin/countries/:id(.:format)                                                                    admin/countries#show
#                                          PATCH  /admin/countries/:id(.:format)                                                                    admin/countries#update
#                                          PUT    /admin/countries/:id(.:format)                                                                    admin/countries#update
#                                          DELETE /admin/countries/:id(.:format)                                                                    admin/countries#destroy
#                             admin_orders GET    /admin/orders(.:format)                                                                           admin/orders#index
#                                          POST   /admin/orders(.:format)                                                                           admin/orders#create
#                          new_admin_order GET    /admin/orders/new(.:format)                                                                       admin/orders#new
#                         edit_admin_order GET    /admin/orders/:id/edit(.:format)                                                                  admin/orders#edit
#                              admin_order GET    /admin/orders/:id(.:format)                                                                       admin/orders#show
#                                          PATCH  /admin/orders/:id(.:format)                                                                       admin/orders#update
#                                          PUT    /admin/orders/:id(.:format)                                                                       admin/orders#update
#                                          DELETE /admin/orders/:id(.:format)                                                                       admin/orders#destroy
#                             admin_prices GET    /admin/prices(.:format)                                                                           admin/prices#index
#                                          POST   /admin/prices(.:format)                                                                           admin/prices#create
#                          new_admin_price GET    /admin/prices/new(.:format)                                                                       admin/prices#new
#                         edit_admin_price GET    /admin/prices/:id/edit(.:format)                                                                  admin/prices#edit
#                              admin_price GET    /admin/prices/:id(.:format)                                                                       admin/prices#show
#                                          PATCH  /admin/prices/:id(.:format)                                                                       admin/prices#update
#                                          PUT    /admin/prices/:id(.:format)                                                                       admin/prices#update
#                                          DELETE /admin/prices/:id(.:format)                                                                       admin/prices#destroy
#                           admin_products GET    /admin/products(.:format)                                                                         admin/products#index
#                                          POST   /admin/products(.:format)                                                                         admin/products#create
#                        new_admin_product GET    /admin/products/new(.:format)                                                                     admin/products#new
#                       edit_admin_product GET    /admin/products/:id/edit(.:format)                                                                admin/products#edit
#                            admin_product GET    /admin/products/:id(.:format)                                                                     admin/products#show
#                                          PATCH  /admin/products/:id(.:format)                                                                     admin/products#update
#                                          PUT    /admin/products/:id(.:format)                                                                     admin/products#update
#                                          DELETE /admin/products/:id(.:format)                                                                     admin/products#destroy
#                            admin_regions GET    /admin/regions(.:format)                                                                          admin/regions#index
#                                          POST   /admin/regions(.:format)                                                                          admin/regions#create
#                         new_admin_region GET    /admin/regions/new(.:format)                                                                      admin/regions#new
#                        edit_admin_region GET    /admin/regions/:id/edit(.:format)                                                                 admin/regions#edit
#                             admin_region GET    /admin/regions/:id(.:format)                                                                      admin/regions#show
#                                          PATCH  /admin/regions/:id(.:format)                                                                      admin/regions#update
#                                          PUT    /admin/regions/:id(.:format)                                                                      admin/regions#update
#                                          DELETE /admin/regions/:id(.:format)                                                                      admin/regions#destroy
#                              admin_sales GET    /admin/sales(.:format)                                                                            admin/sales#index
#                                          POST   /admin/sales(.:format)                                                                            admin/sales#create
#                           new_admin_sale GET    /admin/sales/new(.:format)                                                                        admin/sales#new
#                          edit_admin_sale GET    /admin/sales/:id/edit(.:format)                                                                   admin/sales#edit
#                               admin_sale GET    /admin/sales/:id(.:format)                                                                        admin/sales#show
#                                          PATCH  /admin/sales/:id(.:format)                                                                        admin/sales#update
#                                          PUT    /admin/sales/:id(.:format)                                                                        admin/sales#update
#                                          DELETE /admin/sales/:id(.:format)                                                                        admin/sales#destroy
#                             admin_states GET    /admin/states(.:format)                                                                           admin/states#index
#                                          POST   /admin/states(.:format)                                                                           admin/states#create
#                          new_admin_state GET    /admin/states/new(.:format)                                                                       admin/states#new
#                         edit_admin_state GET    /admin/states/:id/edit(.:format)                                                                  admin/states#edit
#                              admin_state GET    /admin/states/:id(.:format)                                                                       admin/states#show
#                                          PATCH  /admin/states/:id(.:format)                                                                       admin/states#update
#                                          PUT    /admin/states/:id(.:format)                                                                       admin/states#update
#                                          DELETE /admin/states/:id(.:format)                                                                       admin/states#destroy
#                             admin_stores GET    /admin/stores(.:format)                                                                           admin/stores#index
#                                          POST   /admin/stores(.:format)                                                                           admin/stores#create
#                          new_admin_store GET    /admin/stores/new(.:format)                                                                       admin/stores#new
#                         edit_admin_store GET    /admin/stores/:id/edit(.:format)                                                                  admin/stores#edit
#                              admin_store GET    /admin/stores/:id(.:format)                                                                       admin/stores#show
#                                          PATCH  /admin/stores/:id(.:format)                                                                       admin/stores#update
#                                          PUT    /admin/stores/:id(.:format)                                                                       admin/stores#update
#                                          DELETE /admin/stores/:id(.:format)                                                                       admin/stores#destroy
#                       admin_store_chains GET    /admin/store_chains(.:format)                                                                     admin/store_chains#index
#                                          POST   /admin/store_chains(.:format)                                                                     admin/store_chains#create
#                    new_admin_store_chain GET    /admin/store_chains/new(.:format)                                                                 admin/store_chains#new
#                   edit_admin_store_chain GET    /admin/store_chains/:id/edit(.:format)                                                            admin/store_chains#edit
#                        admin_store_chain GET    /admin/store_chains/:id(.:format)                                                                 admin/store_chains#show
#                                          PATCH  /admin/store_chains/:id(.:format)                                                                 admin/store_chains#update
#                                          PUT    /admin/store_chains/:id(.:format)                                                                 admin/store_chains#update
#                                          DELETE /admin/store_chains/:id(.:format)                                                                 admin/store_chains#destroy
#                              admin_users GET    /admin/users(.:format)                                                                            admin/users#index
#                                          POST   /admin/users(.:format)                                                                            admin/users#create
#                           new_admin_user GET    /admin/users/new(.:format)                                                                        admin/users#new
#                          edit_admin_user GET    /admin/users/:id/edit(.:format)                                                                   admin/users#edit
#                               admin_user GET    /admin/users/:id(.:format)                                                                        admin/users#show
#                                          PATCH  /admin/users/:id(.:format)                                                                        admin/users#update
#                                          PUT    /admin/users/:id(.:format)                                                                        admin/users#update
#                                          DELETE /admin/users/:id(.:format)                                                                        admin/users#destroy
#                               admin_root GET    /admin(.:format)                                                                                  admin/sales#index
#                       authenticated_root GET    /                                                                                                 dashboard#index
#                                     help GET    /help(.:format)                                                                                   dashboard#help_form
#                                          POST   /help(.:format)                                                                                   dashboard#help
#                       rails_health_check GET    /up(.:format)                                                                                     rails/health#show
#                                     root GET    /                                                                                                 static#index
#         turbo_recede_historical_location GET    /recede_historical_location(.:format)                                                             turbo/native/navigation#recede
#         turbo_resume_historical_location GET    /resume_historical_location(.:format)                                                             turbo/native/navigation#resume
#        turbo_refresh_historical_location GET    /refresh_historical_location(.:format)                                                            turbo/native/navigation#refresh
#            rails_postmark_inbound_emails POST   /rails/action_mailbox/postmark/inbound_emails(.:format)                                           action_mailbox/ingresses/postmark/inbound_emails#create
#               rails_relay_inbound_emails POST   /rails/action_mailbox/relay/inbound_emails(.:format)                                              action_mailbox/ingresses/relay/inbound_emails#create
#            rails_sendgrid_inbound_emails POST   /rails/action_mailbox/sendgrid/inbound_emails(.:format)                                           action_mailbox/ingresses/sendgrid/inbound_emails#create
#      rails_mandrill_inbound_health_check GET    /rails/action_mailbox/mandrill/inbound_emails(.:format)                                           action_mailbox/ingresses/mandrill/inbound_emails#health_check
#            rails_mandrill_inbound_emails POST   /rails/action_mailbox/mandrill/inbound_emails(.:format)                                           action_mailbox/ingresses/mandrill/inbound_emails#create
#             rails_mailgun_inbound_emails POST   /rails/action_mailbox/mailgun/inbound_emails/mime(.:format)                                       action_mailbox/ingresses/mailgun/inbound_emails#create
#           rails_conductor_inbound_emails GET    /rails/conductor/action_mailbox/inbound_emails(.:format)                                          rails/conductor/action_mailbox/inbound_emails#index
#                                          POST   /rails/conductor/action_mailbox/inbound_emails(.:format)                                          rails/conductor/action_mailbox/inbound_emails#create
#        new_rails_conductor_inbound_email GET    /rails/conductor/action_mailbox/inbound_emails/new(.:format)                                      rails/conductor/action_mailbox/inbound_emails#new
#            rails_conductor_inbound_email GET    /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                                      rails/conductor/action_mailbox/inbound_emails#show
# new_rails_conductor_inbound_email_source GET    /rails/conductor/action_mailbox/inbound_emails/sources/new(.:format)                              rails/conductor/action_mailbox/inbound_emails/sources#new
#    rails_conductor_inbound_email_sources POST   /rails/conductor/action_mailbox/inbound_emails/sources(.:format)                                  rails/conductor/action_mailbox/inbound_emails/sources#create
#    rails_conductor_inbound_email_reroute POST   /rails/conductor/action_mailbox/:inbound_email_id/reroute(.:format)                               rails/conductor/action_mailbox/reroutes#create
# rails_conductor_inbound_email_incinerate POST   /rails/conductor/action_mailbox/:inbound_email_id/incinerate(.:format)                            rails/conductor/action_mailbox/incinerates#create
#                       rails_service_blob GET    /rails/active_storage/blobs/redirect/:signed_id/*filename(.:format)                               active_storage/blobs/redirect#show
#                 rails_service_blob_proxy GET    /rails/active_storage/blobs/proxy/:signed_id/*filename(.:format)                                  active_storage/blobs/proxy#show
#                                          GET    /rails/active_storage/blobs/:signed_id/*filename(.:format)                                        active_storage/blobs/redirect#show
#                rails_blob_representation GET    /rails/active_storage/representations/redirect/:signed_blob_id/:variation_key/*filename(.:format) active_storage/representations/redirect#show
#          rails_blob_representation_proxy GET    /rails/active_storage/representations/proxy/:signed_blob_id/:variation_key/*filename(.:format)    active_storage/representations/proxy#show
#                                          GET    /rails/active_storage/representations/:signed_blob_id/:variation_key/*filename(.:format)          active_storage/representations/redirect#show
#                       rails_disk_service GET    /rails/active_storage/disk/:encoded_key/*filename(.:format)                                       active_storage/disk#show
#                update_rails_disk_service PUT    /rails/active_storage/disk/:encoded_token(.:format)                                               active_storage/disk#update
#                     rails_direct_uploads POST   /rails/active_storage/direct_uploads(.:format)                                                    active_storage/direct_uploads#create
#
# Routes for Blazer::Engine:
#       run_queries POST   /queries/run(.:format)            blazer/queries#run
#    cancel_queries POST   /queries/cancel(.:format)         blazer/queries#cancel
#     refresh_query POST   /queries/:id/refresh(.:format)    blazer/queries#refresh
#    tables_queries GET    /queries/tables(.:format)         blazer/queries#tables
#    schema_queries GET    /queries/schema(.:format)         blazer/queries#schema
#      docs_queries GET    /queries/docs(.:format)           blazer/queries#docs
#           queries GET    /queries(.:format)                blazer/queries#index
#                   POST   /queries(.:format)                blazer/queries#create
#         new_query GET    /queries/new(.:format)            blazer/queries#new
#        edit_query GET    /queries/:id/edit(.:format)       blazer/queries#edit
#             query GET    /queries/:id(.:format)            blazer/queries#show
#                   PATCH  /queries/:id(.:format)            blazer/queries#update
#                   PUT    /queries/:id(.:format)            blazer/queries#update
#                   DELETE /queries/:id(.:format)            blazer/queries#destroy
#         run_check GET    /checks/:id/run(.:format)         blazer/checks#run
#            checks GET    /checks(.:format)                 blazer/checks#index
#                   POST   /checks(.:format)                 blazer/checks#create
#         new_check GET    /checks/new(.:format)             blazer/checks#new
#        edit_check GET    /checks/:id/edit(.:format)        blazer/checks#edit
#             check PATCH  /checks/:id(.:format)             blazer/checks#update
#                   PUT    /checks/:id(.:format)             blazer/checks#update
#                   DELETE /checks/:id(.:format)             blazer/checks#destroy
# refresh_dashboard POST   /dashboards/:id/refresh(.:format) blazer/dashboards#refresh
#        dashboards POST   /dashboards(.:format)             blazer/dashboards#create
#     new_dashboard GET    /dashboards/new(.:format)         blazer/dashboards#new
#    edit_dashboard GET    /dashboards/:id/edit(.:format)    blazer/dashboards#edit
#         dashboard GET    /dashboards/:id(.:format)         blazer/dashboards#show
#                   PATCH  /dashboards/:id(.:format)         blazer/dashboards#update
#                   PUT    /dashboards/:id(.:format)         blazer/dashboards#update
#                   DELETE /dashboards/:id(.:format)         blazer/dashboards#destroy
#              root GET    /                                 blazer/queries#home
#
# Routes for MaintenanceTasks::Engine:
#  pause_task_run PUT  /tasks/:task_id/runs/:id/pause  maintenance_tasks/runs#pause
# cancel_task_run PUT  /tasks/:task_id/runs/:id/cancel maintenance_tasks/runs#cancel
# resume_task_run PUT  /tasks/:task_id/runs/:id/resume maintenance_tasks/runs#resume
#       task_runs POST /tasks/:task_id/runs            maintenance_tasks/runs#create
#           tasks GET  /tasks                          maintenance_tasks/tasks#index
#            task GET  /tasks/:id                      maintenance_tasks/tasks#show
#            root GET  /                               maintenance_tasks/tasks#index
#
# Routes for PgHero::Engine:
#                     space GET  (/:database)/space(.:format)                     pg_hero/home#space
#            relation_space GET  (/:database)/space/:relation(.:format)           pg_hero/home#relation_space
#               index_bloat GET  (/:database)/index_bloat(.:format)               pg_hero/home#index_bloat
#              live_queries GET  (/:database)/live_queries(.:format)              pg_hero/home#live_queries
#                   queries GET  (/:database)/queries(.:format)                   pg_hero/home#queries
#                show_query GET  (/:database)/queries/:query_hash(.:format)       pg_hero/home#show_query
#                    system GET  (/:database)/system(.:format)                    pg_hero/home#system
#                 cpu_usage GET  (/:database)/cpu_usage(.:format)                 pg_hero/home#cpu_usage
#          connection_stats GET  (/:database)/connection_stats(.:format)          pg_hero/home#connection_stats
#     replication_lag_stats GET  (/:database)/replication_lag_stats(.:format)     pg_hero/home#replication_lag_stats
#                load_stats GET  (/:database)/load_stats(.:format)                pg_hero/home#load_stats
#          free_space_stats GET  (/:database)/free_space_stats(.:format)          pg_hero/home#free_space_stats
#                   explain GET  (/:database)/explain(.:format)                   pg_hero/home#explain
#                      tune GET  (/:database)/tune(.:format)                      pg_hero/home#tune
#               connections GET  (/:database)/connections(.:format)               pg_hero/home#connections
#               maintenance GET  (/:database)/maintenance(.:format)               pg_hero/home#maintenance
#                      kill POST (/:database)/kill(.:format)                      pg_hero/home#kill
# kill_long_running_queries POST (/:database)/kill_long_running_queries(.:format) pg_hero/home#kill_long_running_queries
#                  kill_all POST (/:database)/kill_all(.:format)                  pg_hero/home#kill_all
#        enable_query_stats POST (/:database)/enable_query_stats(.:format)        pg_hero/home#enable_query_stats
#                           POST (/:database)/explain(.:format)                   pg_hero/home#explain
#         reset_query_stats POST (/:database)/reset_query_stats(.:format)         pg_hero/home#reset_query_stats
#              system_stats GET  (/:database)/system_stats(.:format)              redirect(301, system)
#               query_stats GET  (/:database)/query_stats(.:format)               redirect(301, queries)
#                      root GET  /(:database)(.:format)                           pg_hero/home#index

require "sidekiq/web"
require "sidekiq/cron/web"

Rails.application.routes.draw do
  concern :importable do
    post "/import", action: :import, on: :collection
  end

  concern :countable do
    get :pending_count, action: :pending_count, on: :collection
    get :region_count, action: :region_count, on: :collection
  end

  concern :statable do
    get :stats, action: :stats, on: :collection
  end

  devise_for :users, controllers: {
    registrations: "users/registrations",
    sessions: "users/sessions",
    passwords: "users/reset"
  }

  namespace :account do
    resource :password, only: [:edit, :update]
    resource :notifications, only: [:edit, :update]
  end

  resources :carts, only: [:show, :destroy]
  resources :line_items, only: [:create, :show, :destroy] do
    member do
      post :add
      post :remove
    end
  end

  resources :users, concerns: [:importable, :statable], except: [:new, :create] do
    post :stop_impersonating, on: :collection
    resources :adjustments, only: [:new, :create], module: :users
    resources :orders, only: [:new, :create], module: :users
    member do
      post :approve
      post :decline
      post :impersonate
      get :ledger
      get :audit
    end
    collection do
      get :count
    end
    resources :activities, only: [:index]
  end

  resources :notifications, only: [] do
    get :read, on: :member
  end

  resource :shop, only: [:show]
  namespace :shops do
    resources :products, only: [:show]
  end

  resources :search, only: [:index]

  resources :store_chains
  resources :stores, concerns: [:importable, :statable] do
    collection do
      get :count
    end
  end
  resources :regions
  resources :categories, concerns: :importable
  resources :sales, concerns: [:countable, :importable, :statable]
  resources :orders, concerns: [:countable, :statable], except: [:new]
  resources :products, concerns: :importable do
    member do
      get :sale
    end
    collection do
      post :search
    end

    resources :prices, except: [:index, :show]
  end
  resources :store_requests, concerns: :countable
  resources :messages

  namespace :products do
    resources :update, only: [:new, :create] do
      collection do
        post :validate
      end
    end
  end

  resources :gift_cards, only: [:index]

  resources :promotions

  resources :reports do
    collection do
      get :analytics
      get :sales_analytics
      get :user_analytics
      post :login
      post :sales
      post :orders
    end
  end

  resources :real_time_analytics, only: [] do
    collection do
      get :dashboard
      get :live_metrics
      get :sales_stream
      get :user_activity
      get :system_health
      post :refresh_all
    end
  end

  namespace :webhooks do
    resources :sendgrid, only: [:create]
  end

  scope controller: :static do
    get :about
    get :terms
    get :privacy
  end

  match "/404", via: :all, to: "static#not_found"
  match "/500", via: :all, to: "static#internal_server_error"

  authenticated :user, lambda { |u| u.super_admin? } do
    mount Sidekiq::Web, at: "sidekiq"
    mount Blazer::Engine, at: "blazer"
    mount MaintenanceTasks::Engine, at: "maintenance_tasks"
    mount PgHero::Engine, at: "pghero"
    mount Flipper::UI.app(Flipper), at: "flipper"

    namespace :admin do
      resources :brands
      resources :categories
      resources :countries
      resources :orders
      resources :prices
      resources :products
      resources :regions
      resources :sales
      resources :states
      resources :stores
      resources :store_chains
      resources :users

      root to: "sales#index"
    end
  end

  authenticated :user do
    root to: "dashboard#index", as: :authenticated_root
    get :help, to: "dashboard#help_form"
    post :help, to: "dashboard#help"
  end

  direct :cdn_image do |model, options|
    expires_in = options.delete(:expires_in) {
      ActiveStorage.urls_expire_in
    }

    if model.respond_to?(:signed_id)
      route_for(
        :rails_service_blob_proxy,
        model.signed_id(expires_in: expires_in),
        model.filename,
        options.merge(host: ENV["CDN_HOST"])
      )
    else
      signed_blob_id = model.blob_signed_id(expires_in: expires_in)
      variation_key = model.variation.key
      filename = model.blob.filename

      route_for(
        :rails_blob_representation_proxy,
        signed_blob_id,
        variation_key,
        filename,
        options.merge(host: ENV["CDN_HOST"])
      )
    end
  end

  get :up, to: "rails/health#show", as: :rails_health_check

  root to: "static#index"
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html
end
