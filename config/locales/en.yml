# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  activerecord:
    errors:
      models:
        sale:
          attributes:
            sold_at:
              on_or_before: must be on or before today
            serial_number:
              on_list: is already used
      messages:
        invalid_upc: is invalid UPC
  notifications:
    item:
      new_sale_notification_message: New Sale
      new_sale_notification_description: "%{name} has entered a new sale."
      updated_sale_notification_message: Updated Sale
      updated_sale_notification_description: "Your sale has been updated."
      new_order_notification_message: New Order
      new_order_notification_description: "%{name} has entered a new order."
      updated_order_notification_message: Updated Order
      updated_order_notification_description: "Your order has been updated."
      new_message_notification_message: New Message
      new_message_notification_description: "%{name} has sent a message."
    upcoming_promotion_notification:
      message: "Hi! We're excited to let you know that we will be offering a point multiplier promotion starting on %{start}. During this promotion, you will earn %{multiplier}x the points for every %{product_name} sold. We hope you take advantage of this opportunity to earn some extra points!"
    starting_promotion_notification:
      message: "Great news! The promotion for %{multiplier}x the points on %{product_name} starts today."
