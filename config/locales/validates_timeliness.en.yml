en:
  errors:
    messages:
      invalid_date: "is not a valid date"
      invalid_time: "is not a valid time"
      invalid_datetime: "is not a valid datetime"
      is_at: "must be at %{restriction}"
      before: "must be before %{restriction}"
      on_or_before: "must be on or before %{restriction}"
      after: "must be after %{restriction}"
      on_or_after: "must be on or after %{restriction}"
  validates_timeliness:
    error_value_formats:
      date: '%Y-%m-%d'
      time: '%H:%M:%S'
      datetime: '%Y-%m-%d %H:%M:%S'
