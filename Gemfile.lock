GIT
  remote: https://github.com/basecamp/name_of_person.git
  revision: bab0a4496a167fc5e153f4eb6514ba45f5d26338
  specs:
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)

GIT
  remote: https://github.com/coder2000/meilisearch-rails.git
  revision: c0386b55b1aa5af8509960f1b7d415339d4c4c9e
  specs:
    meilisearch-rails (0.13.0)
      meilisearch (~> 0.26.0)

GIT
  remote: https://github.com/georgekaraszi/activerecordextended.git
  revision: bf991bd98af4348692d9fd8df8d057cbcca7d952
  specs:
    active_record_extended (3.3.0)
      activerecord (>= 5.2, < 8.1)
      pg (< 3.0)

GEM
  remote: https://rubygems.org/
  specs:
    action_policy (0.6.9)
      ruby-next-core (>= 1.0)
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_median (0.6.0)
      activesupport (>= 7.1)
    active_record_doctor (1.15.0)
      activerecord (>= 4.2.0)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activerecord-import (1.8.1)
      activerecord (>= 4.2)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    administrate (0.19.0)
      actionpack (>= 5.0)
      actionview (>= 5.0)
      activerecord (>= 5.0)
      jquery-rails (>= 4.0)
      kaminari (>= 1.0)
      sassc-rails (~> 2.1)
      selectize-rails (~> 0.6)
    ahoy_matey (5.4.0)
      activesupport (>= 7.1)
      device_detector (>= 1)
      safely_block (>= 0.4)
    annotaterb (4.15.0)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1117.0)
    aws-sdk-core (3.226.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.105.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.189.1)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    barby (0.7.0)
    barkick (0.2.0)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    blazer (3.3.0)
      activerecord (>= 7.1)
      chartkick (>= 5)
      csv
      railties (>= 7.1)
      safely_block (>= 0.4)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    byebug (12.0.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    chartkick (5.2.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    counter_culture (3.10.1)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.5)
    cuprite (0.17)
      capybara (~> 3.0)
      ferrum (~> 0.17.0)
    data_migrate (11.3.0)
      activerecord (>= 6.1)
      railties (>= 6.1)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug_inspector (1.2.0)
    device_detector (1.1.3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_last_seen (0.2.2)
      devise
    diff-lcs (1.6.2)
    drb (2.2.3)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-initializer-rails (3.1.1)
      dry-initializer (>= 2.4, < 4)
      rails (> 3.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-schema (1.14.1)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-logic (~> 1.5)
      dry-types (~> 1.8)
      zeitwerk (~> 2.6)
    dry-types (1.8.3)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    fastimage (2.4.0)
    ferrum (0.17.1)
      addressable (~> 2.5)
      base64 (~> 0.2)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.2-x86_64-linux-gnu)
    flipper (1.3.4)
      concurrent-ruby (< 2)
    flipper-active_record (1.3.4)
      activerecord (>= 4.2, < 9)
      flipper (~> 1.3.4)
    flipper-ui (1.3.4)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.4)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-protobuf (4.31.1-x86_64-linux-gnu)
      bigdecimal
      rake (>= 13)
    has_barcode (0.2.3)
      activesupport (>= 3)
      barby
      i18n
      rake
    hashdiff (1.2.0)
    hightop (0.6.0)
      activesupport (>= 7.1)
    honeybadger (5.28.0)
      logger
      ostruct
    htmlentities (4.3.4)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    inline_svg (1.10.0)
      activesupport (>= 3.0)
      nokogiri (>= 1.6)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    job-iteration (1.10.0)
      activejob (>= 6.1)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.12.2)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    local_time (3.0.3)
    lockbox (1.4.1)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    maintenance_tasks (2.12.0)
      actionpack (>= 7.0)
      activejob (>= 7.0)
      activerecord (>= 7.0)
      csv
      job-iteration (>= 1.3.6)
      railties (>= 7.0)
      zeitwerk (>= 2.6.2)
    marcel (1.0.4)
    matrix (0.4.2)
    meilisearch (0.26.0)
      httparty (>= 0.17.1, < 0.22.0)
    method_source (1.1.0)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    mjml-rails (4.15.1)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.8.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    noticed (2.7.0)
      rails (>= 6.1.0)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    pagy (8.6.3)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pg_query (5.1.0)
      google-protobuf (>= 3.22.3)
    pghero (3.7.0)
      activerecord (>= 7.1)
    posthog-ruby (3.0.1)
      concurrent-ruby (~> 1)
    pp (0.6.2)
      prettyprint
    pretender (0.6.0)
      actionpack (>= 7.1)
    prettyprint (0.2.0)
    prism (1.4.0)
    prosopite (1.4.2)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_real_favicon (0.1.1)
      json (>= 1.7, < 3)
      rails
      rubyzip (~> 2)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.25.0)
      connection_pool
    reek (6.5.0)
      dry-schema (~> 1.13)
      logger (~> 1.6)
      parser (~> 3.3.0)
      rainbow (>= 2.0, < 4.0)
      rexml (~> 3.1)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    requestjs-rails (0.0.13)
      railties (>= 7.1.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rorvswild (1.9.2)
    rouge (4.5.2)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.5)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-next-core (1.1.1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    rubyzip (2.4.1)
    safely_block (0.5.0)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selectize-rails (0.12.6)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    simple_calendar (3.1.0)
      rails (>= 6.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    standard (1.50.0)
      language_server-protocol (~> ********)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    strong_migrations (1.8.0)
      activerecord (>= 5.2)
    tailwindcss-rails (4.2.3)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.10-x86_64-linux-gnu)
    thor (1.3.2)
    tilt (2.6.0)
    timeliness (0.5.3)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    validates_timeliness (8.0.0)
      activemodel (>= 8.0.0, < 9)
      timeliness (>= 0.3.10, < 1)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  action_policy (~> 0.6.0)
  active_median (~> 0.4)
  active_record_doctor
  active_record_extended!
  activerecord-import (~> 1.5)
  administrate
  ahoy_matey (~> 5.0)
  annotaterb
  aws-sdk-s3 (~> 1)
  barby (~> 0.6)
  barkick (~> 0.2.0)
  better_errors
  binding_of_caller
  blazer (~> 3.0)
  bootsnap (>= 1.4.4)
  brakeman
  byebug
  capybara
  caxlsx (~> 4.0)
  caxlsx_rails (~> 0.6)
  chartkick (~> 5.0)
  counter_culture (~> 3.5)
  cuprite
  data_migrate
  database_cleaner-active_record
  devise (~> 4.9)
  devise_last_seen
  dry-initializer (~> 3.1)
  dry-initializer-rails (~> 3.1)
  dry-types (~> 1.7)
  factory_bot_rails
  faker
  fastimage (~> 2.2)
  flipper
  flipper-active_record
  flipper-ui
  friendly_id (~> 5.5)
  geocoder (~> 1.8)
  has_barcode (~> 0.2)
  hightop (~> 0.4)
  honeybadger (~> 5.3)
  image_processing (~> 1.12)
  inline_svg (~> 1.9)
  jbuilder (~> 2.11)
  jsbundling-rails (~> 1.2)
  launchy
  listen (~> 3.3)
  local_time (~> 3.0)
  lockbox (~> 1.3)
  maintenance_tasks (~> 2.3)
  meilisearch-rails (~> 0.13)!
  mjml-rails (~> 4.9)
  money-rails (~> 1.12)
  name_of_person (~> 1.1)!
  net-sftp (~> 4.0)
  noticed (~> 2.7)
  pagy (~> 8)
  paper_trail (~> 16)
  pg (~> 1.5)
  pg_query (~> 5.1)
  pghero (~> 3.4)
  posthog-ruby
  pretender (~> 0.5)
  prosopite (~> 1.4)
  puma (~> 6.4)
  pundit (~> 2.3)
  rack-cors (~> 2.0)
  rails (~> 8.0.2)
  rails-controller-testing
  rails_real_favicon
  redis (~> 5.0)
  reek
  requestjs-rails (~> 0.0)
  rorvswild
  rspec-rails (~> 6.0)
  shoulda-matchers
  sidekiq (~> 7.2)
  sidekiq-cron (~> 1.11)
  simple_calendar (~> 3.0)
  sprockets-rails (~> 3.4)
  standard
  stimulus-rails (~> 1.3)
  strong_migrations (~> 1.6)
  tailwindcss-rails (~> 4.2.3)
  turbo-rails (~> 2.0)
  tzinfo-data
  validates_timeliness
  view_component (~> 3)
  web-console (>= 4.1.0)
  webmock

RUBY VERSION
   ruby 3.3.0p0

BUNDLED WITH
   2.5.9
